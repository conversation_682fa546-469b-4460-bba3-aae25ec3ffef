name: PR Merged

on:
  pull_request:
    types: [closed]
    branches:
      - "master"
      - "cloud"
      - "main"
      - "onprem_[0-9]*.[0-9]*"

jobs:
  on-merge:
    runs-on: ubuntu-latest
    if: github.event.pull_request.merged == true
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Post Merge Add Label
        env:
          PR_TITLE: ${{ github.event.pull_request.title }}
          PR_TITLE_REGEX_PATTERN: ${{ vars.PR_TITLE_REGEX_PATTERN }}
        run: |
          echo "*******************************************************************"
          echo "base_branch:${{ github.base_ref }}"
          echo "pr_title:$PR_TITLE"
          echo "*******************************************************************"
          base_branch="${{ github.base_ref }}"
          dev_subfix="_dev"
          base_branch=${base_branch//$dev_subfix/}
          matches=$(echo "$PR_TITLE" | grep -oP "$PR_TITLE_REGEX_PATTERN" || true)

          if [[ -z $matches ]]; then
            errorMessage="Invalid PR title format. Please follow the correct format: \"([)ticket-number(]) type(scope): description;\" \nExample: TOOLS-1234 feat(login): add forgot password link;\n\n"
            echo "Failed:$errorMessage"
          else
              echo "Matching strings: $matches"
              IFS=$'\n'
              for match in $matches; do
                ticket=$(echo "$match" | grep -m 1 -oP "[a-zA-Z]+-\d+" | awk 'NR==1{print $1}')
                echo "Matching: $match"
                echo "JIRA Ticket Number: $ticket"
                api_url="https://graphsql.atlassian.net/rest/api/latest/issue/$ticket"
                issue_info=$(curl -s -u "${{ secrets.JIRA_USERNAME }}:${{ secrets.JIRA_API_TOKEN }}" -H "Content-Type: application/json" "$api_url")
                labels=$(echo "$issue_info" | jq -r '.fields.labels[]')
                echo "Labels: $labels"
                merged_banch_label="merged_"$base_branch
                if [[ "$labels" == *"merged_$base_branch"* ]]; then
                  echo "This PR has already been merged to the base branch. If you want to merge it again, please remove the \"merged_$base_branch\" label from the corresponding JIRA ticket $ticket"
                  errorMessage+="This PR has already been merged to the base branch. If you want to merge it again, please remove the \"merged_$base_branch\" label from the corresponding JIRA ticket [$ticket](https://graphsql.atlassian.net/browse/$ticket).\n"
                elif [[ "$labels" != *"$base_branch"* ]]; then
                  echo "Missing label for JIRA ticket $ticket:$base_branch. Please add the required label."
                  errorMessage+="Missing label for JIRA ticket [$ticket](https://graphsql.atlassian.net/browse/$ticket): \"$base_branch\". Please add the required label.\n"
                else
                  echo "Ticket $ticket meets requirements"
                  curl -s -X PUT -u "${{ secrets.JIRA_USERNAME }}:${{ secrets.JIRA_API_TOKEN }}" -H "Content-Type: application/json" "$api_url" -d '{"update": {"labels": [{"add": "merged_'$base_branch'"}]}}'
                  # NEW_STATUS="QA TEST"
                  # REST_API_URL="${JIRA_API_URL}/rest/api/2/issue/${ticket}/transitions"
                  # TRANSITION_ID=$(curl -s -u "${{ secrets.JIRA_USERNAME }}:${{ secrets.JIRA_API_TOKEN }}" -H "Content-Type: application/json" "${REST_API_URL}" | jq --arg NEW_STATUS "${NEW_STATUS}" -r '.transitions[] | select(.to.name==$NEW_STATUS) | .id')
                  # echo "TRANSITION_ID=$TRANSITION_ID"
                  # curl -s -u "${{ secrets.JIRA_USERNAME }}:${{ secrets.JIRA_API_TOKEN }}" -H "Content-Type: application/json" --request POST --data "{\"transition\": {\"id\": \"${TRANSITION_ID}\"}}" "${REST_API_URL}"
                fi
              done
          fi

          if [ -n "$errorMessage" ]; then
            echo "ERROR_MESSAGE=$errorMessage" >> $GITHUB_ENV
            exit 1
          else
            echo "PR validation is done";
          fi

      - name: Comment on PR
        uses: actions/github-script@v3
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const fs = require('fs');
            github.pulls.createReview({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.payload.pull_request.number,
              event: 'COMMENT',
              body: 'PR labels has been added'
            });
