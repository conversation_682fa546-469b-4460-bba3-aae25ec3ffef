{"test_env": "https://classic.tgcloud-dev.com", "cluster_name": "", "org": "qe-dev", "user": "<EMAIL>", "password": "Cust1234", "login": [{"case_name": "login_with_user_password_1", "org": "qe-dev", "user": "<EMAIL>", "password": "Cust1234"}, {"case_name": "login_with_user_password_2", "org": "jm-org-627", "user": "<EMAIL>", "password": "Cust1234"}], "create_clusters": [{"case_name": "create_cluster_1", "enable": "true", "org": "qe_create_clusters", "user": "<EMAIL>", "password": "Cust1234", "cluster_name": "", "tg_version": "3.8.0", "platform": "aws", "region": "us-east-1", "instance_type": "TG.C4.M16", "disk_size": "128", "partition_factor": "1", "replication_factor": "1", "starter_kit": "gsql101"}], "cloudbenchmark": [{"case_name": "100g_benchmark_s3", "test_env": "https://classic.tgcloud-dev.com", "enable": "true", "dataset": "100k", "datasource": "s3", "org": "qe_benchmark_100k", "user": "<EMAIL>", "password": "Cust1234", "cluster_name": "", "tg_version": "3.8.0", "solution_tarball": "https://qe-test.s3.amazonaws.com/ldbc-data-set/solution_tarball/LDBC_380_SF100K_100G_AWS_S3_Mapped_for_TGC_BenchmarkTest.tar.gz", "platform": "azure", "region": "west<PERSON>", "instance_type": "TG.C16.M64", "disk_size": "128", "partition_factor": "3", "replication_factor": "1", "load_timeout": 7200, "total_vertex": "282,639,955", "total_edge": "3,551,027,712"}, {"case_name": "100g_benchmark_gcs", "test_env": "https://classic.tgcloud-dev.com", "enable": "false", "dataset": "100k", "datasource": "gcs", "org": "qe_benchmark_100k", "user": "<EMAIL>", "password": "Cust1234", "cluster_name": "", "tg_version": "3.8.0", "solution_tarball": "https://qe-test.s3.amazonaws.com/ldbc-data-set/solution_tarball/ldbc_new_100G_gc3.tar.gz", "platform": "azure", "region": "west<PERSON>", "instance_type": "TG.C16.M64", "disk_size": "256", "partition_factor": "3", "replication_factor": "1", "load_timeout": 10800, "total_vertex": "2,686,783,677", "total_edge": "35,577,469,496"}, {"case_name": "1T_benchmark_s3", "test_env": "https://classic.tgcloud-dev.com", "enable": "false", "dataset": "1000k", "org": "qe_benchmark_1000k_s3", "user": "<EMAIL>", "password": "Cust1234", "cluster_name": "", "tg_version": "3.8", "datasource": "s3", "solution_tarball": "https://qe-test.s3.amazonaws.com/ldbc-data-set/solution_tarball/LDBC_361_SF1000K_AWS_S3_Mapped_for_TGC_BenchmarkTest.tar.gz", "platform": "azure", "region": "west<PERSON>", "instance_type": "TG.C64.M432", "disk_size": "1024", "partition_factor": "3", "replication_factor": "1", "load_timeout": 36000, "total_vertex": "177,034,107", "total_edge": "2,172,964,630"}, {"case_name": "1T_benchmark_gcs", "test_env": "https://classic.tgcloud-dev.com", "enable": "false", "dataset": "1000k", "org": "qe_benchmark_1000k_gcs", "user": "<EMAIL>", "password": "Cust1234", "cluster_name": "", "tg_version": "3.8", "datasource": "gcs", "solution_tarball": "https://qe-test.s3.amazonaws.com/ldbc-data-set/solution_tarball/LDBC_NEW_gc3_IT.tar.gz", "platform": "azure", "region": "west<PERSON>", "instance_type": "TG.C64.M432", "disk_size": "1024", "partition_factor": "3", "replication_factor": "1", "load_timeout": 54000, "total_vertex": "1,693,845,442", "total_edge": "22,096,909,364"}]}