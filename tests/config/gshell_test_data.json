{"Solution": "COVID-19 Analysis", "GraphName": "MyGraph", "gshell_paths": {"upload_data_path": "./tests/data/gshelldata/upload_data", "download_data_path": "./tests/data/gshelldata/download_data", "upload_toGST_path": "./tests/data/gshelldata/upload_toGST"}, "testcase_load_gsqlfile": {"USE_GLOBAL": {"gsqlfile": "USE_GLOBAL.gsql", "output": "", "assertTimeout": 30}}, "testcase_queries": {"0_drop_query": {"gsqlfile": "drop_gshell_age_distribution.gsql", "output": "", "assertTimeout": 100}, "createinstall_query": {"gsqlfile": "createinstall_gshell_age_distribution.gsql", "output": "Query installation finished", "assertTimeout": 600}, "run_query": {"gsqlfile": "run_gshell_age_distribution.gsql", "output": "@@age_map", "assertTimeout": 300, "jsonOutput": true}, "run_opencypher_query": {"gsqlfile": "opencypher_query.gsql", "output": "patient_count", "assertTimeout": 300}, "drop_query": {"gsqlfile": "drop_gshell_age_distribution.gsql", "output": "Successfully dropped queries", "assertTimeout": 100}}, "testcase_primary_key": {"0_drop_query": {"gsqlfile": "drop_global_vertex_with_primary_key.gsql", "output": "", "assertTimeout": 100}, "create_global_vertex": {"gsqlfile": "create_global_vertex_with_primary_key.gsql", "output": "Successfully created vertex types", "assertTimeout": 600}, "drop_local_schema_change_job": {"gsqlfile": "drop_local_vertex_with_primary_key.gsql", "output": "", "assertTimeout": 300}, "create_local_schema_change_job": {"gsqlfile": "create_local_vertex_with_primary_key.gsql", "output": "Local schema change succeeded", "assertTimeout": 300}}, "testcase_prepare_insights_queries": {"province_all_connection": {"gsqlfile": "province_all_connection.gsql", "output": "Query installation finished", "assertTimeout": 600}, "my_page_rank": {"gsqlfile": "my_page_rank.gsql", "output": "Query installation finished", "assertTimeout": 600}, "test_map_parameter_for_query": {"gsqlfile": "test_map_parameter_for_query_tg420.gsql", "output": "Query installation finished", "assertTimeout": 600}, "test_int_input_for_installed_query": {"gsqlfile": "test_int_input_for_installed_query.gsql", "output": "Query installation finished", "assertTimeout": 600}, "test_list_parameter_for_query": {"gsqlfile": "test_list_parameter_for_query_tg420.gsql", "output": "Successfully created queries", "assertTimeout": 200}, "test_int_input_for_interpreted_query": {"gsqlfile": "test_int_input_for_interpreted_query.gsql", "output": "Successfully created queries", "assertTimeout": 200}, "test_input_vertex_from_query": {"gsqlfile": "test_input_vertex_from_query.gsql", "output": "Successfully created queries", "assertTimeout": 200}, "my_page_rank_for_interpreted_query": {"gsqlfile": "my_page_rank_for_interpreted_query.gsql", "output": "Successfully created queries", "assertTimeout": 200}}, "testcase_prepare_negative_queries": {"run_installed_query_with_limit_parameter_tg393": {"gsqlfile": "run_installed_query_with_limit_parameter_tg393.gsql", "output": "Successfully created queries", "assertTimeout": 200}, "install_all_queries_with_partial_success_tg420": {"gsqlfile": "install_all_queries_with_partial_success_tg420.gsql", "output": "Successfully created queries", "assertTimeout": 200}}, "testcase_attributes": {"0_drop_attributesGraphs": {"gsqlfile": "drop_attributesGraphs.gsql", "output": "", "assertTimeout": 100}, "Graph_test_udt": {"gsqlfile": "Graph_test_udt.gsql", "output": "\"error\": false", "assertTimeout": 100}, "Graph_test_SetList": {"gsqlfile": "Graph_test_SetList.gsql", "output": "\"error\": false", "assertTimeout": 100}, "Graph_test_Map": {"gsqlfile": "Graph_test_Map.gsql", "output": "\"error\": false", "assertTimeout": 100}, "drop_attributesGraphs": {"gsqlfile": "drop_attributesGraphs.gsql", "output": "Successfully dropped vertex types: [Vertex_map]", "assertTimeout": 60}}, "testcase_create_queries_check_on_GST": {"0_drop_attributesGraphs": {"gsqlfile": "drop_queries.gsql", "output": "", "assertTimeout": 100}, "Key_null_json_result_check": {"gsqlfile": "Key_null_json.gsql", "output": "Successfully", "assertTimeout": 100}, "Value2_null_json_result_check": {"gsqlfile": "Value_null_json.gsql", "output": "Successfully", "assertTimeout": 100}, "Key_value_null_json_result_check": {"gsqlfile": "Key_value_null_json.gsql", "output": "Successfully", "assertTimeout": 100}}}