{"test_env": "http://34.31.117.27:14240", "domain": "", "org": "cloud-app-org", "user": "<EMAIL>", "password": "Cust12341234", "platform": "gcp", "cluster_name": "E2E-debug", "tools_cdn": "https://tools.tgcloud-dev.com/#/apps", "login_way": "default_user", "login_cloud": [{"case_name": "login_with_user_password_1", "org": "qe-dev", "user": "<EMAIL>", "password": "Cust1234"}, {"case_name": "login_with_user_password_2", "org": "jm-org-627", "user": "<EMAIL>", "password": "Cust1234"}], "login_onprem": [{"type": "default_user", "username": "tigergraph", "password": "tigergraph"}, {"type": "global_designer", "username": "u_globaldesigner", "password": "globaldesigner"}, {"type": "local_admin", "username": "u_localadmin", "password": "localadmin"}, {"type": "global_UDF_role", "username": "u_globalUDF", "password": "globalUDF"}, {"type": "local_UDF_role", "username": "u_localUDF", "password": "localUDF"}], "benchmark": [{"case_name": "1g_benchmark_s3", "test_env": "https://classic.tgcloud-dev.com", "enable": "false", "cases": "", "datasource": "s3", "org": "qe_benchmark_100k", "user": "<EMAIL>", "password": "Cust1234", "cluster_name": "1g_benchmark_s3", "tg_version": "3.8.0", "solution_tarball": "https://qe-test.s3.amazonaws.com/ldbc-data-set/solution_tarball/LDBC_380_AWS_S3_1K_Mapped_for_TGC_BenchmarkTest_UDF.tar.gz", "graph_name": "ldbc_snb", "solution": "ldbc-new", "parameter_file": "sf_100k_queries_dist.json", "platform": "gcp", "region": "us-west1", "instance_type": "TG.C4.M16", "disk_size": "128", "partition_factor": "1", "replication_factor": "1", "load_timeout": 7200, "total_vertex": "2,121,858", "total_edge": "21,595,882", "backup_name": ""}, {"case_name": "100g_benchmark_s3", "test_env": "https://classic.tgcloud-dev.com", "enable": "true", "cases": "import_solution,load_data,install_query,run_installed_query,backup,clear_data,restore", "datasource": "s3", "org": "qe_benchmark_100k", "user": "<EMAIL>", "password": "Cust1234", "cluster_name": "100g_benchmark_s3", "tg_version": "3.9.3", "solution_tarball": "https://qe-test.s3.amazonaws.com/ldbc-data-set/solution_tarball/LDBC_380_SF100K_100G_AWS_S3_Mapped_for_TGC_BenchmarkTest_no_UDF.tar.gz", "graph_name": "ldbc_snb", "solution": "ldbc", "parameter_file": "sf_100k_queries_dist.json", "platform": "aws", "region": "us-east-1", "instance_type": "TG.C16.M64", "disk_size": "128", "partition_factor": "3", "replication_factor": "1", "load_timeout": 9600, "total_vertex": "282,639,955", "total_edge": "3,551,027,712", "backup_name": ""}, {"case_name": "100g_benchmark_gcs", "test_env": "https://classic.tgcloud-dev.com", "enable": "false", "cases": "import_solution,load_data,run_interpreted_query,install_query,run_installed_query,backup,clear_data,restore", "datasource": "gcs", "org": "qe_benchmark_100k", "user": "<EMAIL>", "password": "Cust1234", "cluster_name": "100g_benchmark_gcs", "tg_version": "3.8.0", "solution_tarball": "https://qe-test.s3.amazonaws.com/ldbc-data-set/solution_tarball/ldbc_new_100G_gc3_UDF.tar.gz", "graph_name": "ldbc_snb", "solution": "ldbc-new", "parameter_file": "sf_100k_queries_dist.json", "platform": "gcp", "region": "us-west1", "instance_type": "TG.C16.M64", "disk_size": "256", "partition_factor": "3", "replication_factor": "1", "load_timeout": 10800, "total_vertex": "177,034,107", "total_edge": "2,160,130,441", "backup_name": ""}, {"case_name": "1T_benchmark_s3", "test_env": "https://classic.tgcloud-dev.com", "enable": "false", "cases": "import_solution,load_data,run_interpreted_query,install_query,run_installed_query,backup,clear_data,restore", "org": "qe_benchmark_1000k_s3", "user": "<EMAIL>", "password": "Cust1234", "cluster_name": "1T_benchmark_s3", "tg_version": "3.8.0", "datasource": "s3", "solution_tarball": "https://qe-test.s3.amazonaws.com/ldbc-data-set/solution_tarball/LDBC_361_S3_1T_UDF.tgz", "graph_name": "ldbc_snb", "solution": "ldbc-new", "parameter_file": "sf_1000k_queries_dist.json", "platform": "gcp", "region": "us-west1", "instance_type": "TG.C64.M512", "disk_size": "2048", "partition_factor": "3", "replication_factor": "1", "load_timeout": 36000, "total_vertex": "2,686,783,677", "total_edge": "35,577,469,496", "backup_name": ""}, {"case_name": "1T_benchmark_gcs", "test_env": "https://classic.tgcloud-dev.com", "enable": "false", "cases": "import_solution,load_data,run_interpreted_query,install_query,run_installed_query,backup,clear_data,restore", "org": "qe_benchmark_1000k_gcs", "user": "<EMAIL>", "password": "Cust1234", "cluster_name": "1T_benchmark_gcs", "tg_version": "3.8.0", "datasource": "gcs", "solution_tarball": "https://qe-test.s3.amazonaws.com/ldbc-data-set/solution_tarball/LDBC_NEW_GCS_IT_UDF.tar.gz", "graph_name": "ldbc_snb", "solution": "ldbc-new", "parameter_file": "sf_1000k_queries_dist.json", "platform": "gcp", "region": "us-west1", "instance_type": "TG.C64.M512", "disk_size": "2048", "partition_factor": "3", "replication_factor": "1", "load_timeout": 54000, "total_vertex": "1,693,845,442", "total_edge": "22,096,909,364", "backup_name": ""}]}