import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.api.tools_api import tools
from src.operation.gus import GUS
from src.common.api_basecase import APIBaseCase
from parameterized import parameterized
import allure


class TestLogout(APIBaseCase):

    login_user_data = data.get_yaml_data("general/login_data.yml")
    logout_test_data_invalid = login_user_data.get("invalid_cookie")
    test_data_invalid = []
    for case in logout_test_data_invalid:
        test_data_invalid.append(
            (
                case,
                logout_test_data_invalid.get(case).get("cookie")
            )
        )


    @allure.title("logout tools with valid cookie")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the cookie will invalid after logout \n"
        "Description: login action will inactive the cookie \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.apilogout
    @pytest.mark.run(order=1)
    def test_logout_valid(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()
        assert cookie != "", "the cookie is null"

        #check the meta data can be retrive via valid cookie
        gus = GUS()
        meta_res = gus.loading_job_meta(cookie)
        meta_res.assert_no_error()

        #logout with the cookie
        logout_res = login.logout(cookie)
        logout_res.assert_logout_success()



    @allure.title("logout tools with invalid cookie")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check can not logout with invalid cookie \n"
        "Description: logout negative test case for logout with invalid cookie \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(test_data_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.apilogout
    @pytest.mark.run(order=1)
    def test_logout_invalid(self, case, cookie):
        #check the meta data can not be retrive via invalid cookie
        gus = GUS()
        meta_res = gus.loading_job_meta(cookie)
        meta_res.assert_api_no_authorized()

        #logout with the cookie
        login = Login()
        logout_res = login.logout(cookie)
        logout_res.assert_logout_success()

