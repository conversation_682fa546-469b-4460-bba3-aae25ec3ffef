import os
import re
from wsgiref import headers
from src.api.tools_api import tools
from src.api.tools_api import Tools
from src.common.read_data import data
from src.common.update_data import updateData
from src.common.logger import logger
from src.common.result_base import ResultBase
from src.common.cookies_base import CookiesBase
from src.operation.cloud import Cloud
import json
import sys
from pathlib import Path
from urllib import parse
utils_path = Path(__file__).resolve().parents[3].joinpath("src")
sys.path.append(utils_path)
from utils.data_util.data_resolver import read_test_data


class Login():
    login_user_data = data.get_yaml_data("general/login_data.yml")
    test_data = read_test_data(file="tools_test_data.json")
    test_env = test_data.get("test_env")
    org = test_data.get("org")
    cluster_name = test_data.get("cluster_name")
    cloud_test_data = login_user_data.get("cloud_login_data_dev")
    cloud_tools_domain = cloud_test_data.get("cloud_tools_domain")

    # check the login way is cloud
    def is_oncloud(self):
        if "tgcloud" in self.test_env:
            return True
        else:
            return False
        
    # login method, we automatically choose the login way
    def login(self, username="tigergraph", password="tigergraph"):
        if self.is_oncloud():
            return self.login_cloud()
        else:
            return ResultBase(self.login_onprem(username, password))

    # login onprem with API
    def login_onprem(self, username="tigergraph", password="tigergraph"):
        json_data = {
                "username": username,
                "password": password
        }
        header = {
            "Content-Type": "application/json"
        }
        login_response = tools.login_gst(json=json.dumps(json_data), headers=header, verify=False)
        return login_response

    def login_cloud(self):
        cloud = Cloud()
        is_cloud_logged_data = data.read_test_data("general/cloud_data.json")
        id_token = is_cloud_logged_data.get("id_token")
        access_token = is_cloud_logged_data.get("access_token")
        cluster_id = is_cloud_logged_data.get("cluster_id")
        domain = is_cloud_logged_data.get("domain")
        # obtain cloud id_token
        if not cluster_id:
            token, domain, cluster_id = cloud.init_cloud_env()
        else:
            # cookeis was obtained, no need refresh it
            token = {
                "id_token": id_token,
                "access_token": access_token
            }
        return CookiesBase(cloud.access_tools(token, domain, cluster_id))        

    # logout tools with API    
    def logout(self, cookies):
        """
        Logout GraphStudio
        :param cookies: Cookies gotten from GST
        :return: return the response of api with custom keywords
        """
        logger.info("start to logout with cookie ....")
        header = {
            "Cookie": cookies,
            "Content-Type": "application/json"
        }
        return ResultBase(tools.logout_gst(headers=header))
