
CREATE OR REPLACE QUERY large_query_test() SYNTAX v3 {
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        print "f:";
                        print "d";
                        print "h";
                        res1 = select s1 from Patient:s1 limit 10;
                        PRINT res1;
                        }