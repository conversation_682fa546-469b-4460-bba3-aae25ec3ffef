USE GRAPH MyMultiEdge

DROP QUERY my_page_rank

CREATE DISTRIBUTED QUERY my_page_rank (FLOAT max_change, INT max_iter, FLOAT damping, BOOL display, INT output_limit) FOR GRAPH MyMultiEdge SYNTAX V2 {
# Compute the pageRank score for each vertex in the GRAPH
# In each iteration, compute a score for each vertex:
#   score = (1-damping) + damping*sum(received scores FROM its neighbors).
# The pageRank algorithm stops when either of the following is true:
#  a) it reaches maxIter iterations;
#  b) the max score change for any vertex compared to the last iteration <= maxChange.

        TYPEDEF TUPLE<vertex Vertex_ID, FLOAT score> vertexScore;
        HeapAccum<vertexScore>(output_limit, score DESC) @@topScores;
        MaxAccum<float> @@maxDiff = 9999; # max score change in an iteration
        SumAccum<float> @received_score = 0; # sum of scores each vertex receives FROM neighbors
        SumAccum<float> @score = 1;   # Initial score for every vertex is 1.
        SetAccum<EDGE> @@edgeSet;                   # list of all edges, if display is needed

        Start = {Patient.*};   #  Start with all vertices of specified type(s)
        WHILE @@maxDiff > max_change LIMIT max_iter DO
                @@maxDiff = 0;
                V = SELECT s
                    FROM Start:s

                    POST-ACCUM s.@score = (1.0-damping) + damping * s.@received_score,
                               s.@received_score = 0,
                               @@maxDiff += abs(s.@score - s.@score');
        END; # END WHILE loop


        IF output_limit > 0 THEN
                V = SELECT s FROM Start:s
                    POST-ACCUM @@topScores += vertexScore(s, s.@score);
                PRINT @@topScores;
        END;


        IF display THEN
                PRINT Start[Start.@score];
                Start = SELECT s
                        FROM Start:s limit output_limit;
                PRINT Start;
        END;
}

INSTALL QUERY my_page_rank
