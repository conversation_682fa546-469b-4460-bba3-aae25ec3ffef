{"defaultGraph": "MyMultiEdge", "iconURL": "/insights/static/media/atom.14f5dd297b1a450cae3413a44f69a75b.svg", "id": "qGLVHXkE2QZgFUFHFGN4B4", "owner": "<EMAIL>", "pageConfigSeparated": true, "pages": [{"chartMap": {"3Y7vJeGJtxZVgNdLNqX642": {"chartSettings": {}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "3Y7vJeGJtxZVgNdLNqX642", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "d567fef3-9503-4586-a356-47a8bfa482a0", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}, {"data": "action_redirect_variable_for_city", "id": "774fbd23-6ea9-4dd1-8e03-4d2115276d66", "paramGlobalInput": "action_redirect_variable_for_city", "paramName": "id", "paramType": "STRING", "type": "PARAM"}], "staticData": "{}", "title": "action_redirect_graph_for_city", "type": "internal-graph", "version": "1744785353502555721"}, "4VgoFJWLXHbVf87aHTFdjf": {"chartSettings": {"widgetActions": [{"pageID": "kCKgu4z11KPfsH8g9dtccK", "params": [{"name": "bool_dropdown", "value": ""}, {"name": "datepicker", "value": ""}, {"name": "datetime_dropdown", "value": ""}, {"name": "datetime_input", "value": ""}, {"name": "input_float_variable", "value": ""}, {"name": "number_dropdown", "value": ""}, {"name": "number_input", "value": ""}, {"name": "slider", "value": ""}, {"isCreatable": false, "name": "string_dropdown", "paramGlobalInput": "text_variable", "value": "text_variable"}, {"name": "string_dropdown_from_query", "value": ""}, {"isCreatable": false, "name": "string_input", "paramGlobalInput": "text_variable", "value": "text_variable"}, {"name": "vertex_input", "value": ""}, {"name": "vertex_variable_test_ui_overlap", "value": ""}], "text": "redirect to input variable string dropdown and input", "url": "", "urlName": "input variable"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "4VgoFJWLXHbVf87aHTFdjf", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "c45bd692-7b1b-4e7f-8ba2-ae2af9f4da8e", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}, {"data": "text_variable", "id": "19ff6722-cb88-43bf-84fb-571c007f1c31", "paramGlobalInput": "text_variable", "paramName": "id", "paramType": "STRING", "type": "PARAM"}], "staticData": "{}", "title": "text_variable_for_city", "type": "internal-graph", "version": "1744791470910650584"}, "7drTcDjKFvM76Dv54Gt2Ro": {"chartSettings": {"showColumns": [{"isChecked": true, "name": "City1"}, {"isChecked": true, "name": "City2"}], "tableColumns": [{"id": "Matched pattern", "isChecked": "true", "label": ""}, {"children": [], "id": "Vertices", "isChecked": "true", "isExpanded": true, "label": "Vertices"}, {"children": [], "id": "<PERSON>s", "isChecked": "true", "isExpanded": false, "label": "<PERSON>s"}], "tableHeaders": ["City1", "City2"]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "7drTcDjKFvM76Dv54Gt2Ro", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"data": "test_map_parameter_for_query", "id": "test_map_parameter_for_query", "type": "QUERY"}, {"data": "", "id": "9c7652d8-7c51-4945-ac40-4d0a7927a43c", "keyType": "STRING", "paramGlobalInput": "map_string_variable", "paramName": "CityID", "paramType": "MAP", "paramTypeReadonly": true, "type": "PARAM", "valueType": "STRING"}], "staticData": "{}", "title": " run query with map", "type": "table", "version": "1744699855283649973"}, "a4rtM1xTXXa6mcUs87sZue": {"chartSettings": {"inputStates": [{"dataType": "string", "id": "input_iKxChJhvLwF4iKqg3qw28h", "name": "text_variable", "placeholder": "text", "settings": {}, "widgetType": "Text"}, {"dataType": "string", "id": "input_ikGFNSnS8Jb9eWuHZGGfpv", "keyType": "string", "name": "map_string_variable", "settings": {}, "valueType": "string", "widgetType": "Map"}, {"dataType": "string", "id": "input_qh7vEqXgVLJkpFPNam1RLM", "name": "list_string_variable", "settings": {}, "widgetType": "List"}]}, "graphName": "MyGraph", "hideWidgetName": false, "id": "a4rtM1xTXXa6mcUs87sZue", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [], "staticData": "{}", "title": "other variable", "type": "Inputs", "version": "1744699855290751664"}, "jgVD3qqmaGrJ7v9ZRKUMkX": {"chartSettings": {"showColumns": [{"isChecked": true, "name": "CityID"}], "tableColumns": [{"id": "Matched pattern", "isChecked": "true", "label": ""}, {"children": [], "id": "Vertices", "isChecked": "true", "isExpanded": true, "label": "Vertices"}, {"children": [], "id": "<PERSON>s", "isChecked": "true", "isExpanded": false, "label": "<PERSON>s"}], "tableHeaders": ["CityID"]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "jgVD3qqmaGrJ7v9ZRKUMkX", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"data": "test_list_parameter_for_query", "id": "test_list_parameter_for_query", "type": "QUERY"}, {"data": "", "elementType": "STRING", "id": "c1b5a918-4a9f-45c6-910b-41b6f056eace", "paramGlobalInput": "list_string_variable", "paramName": "CityID", "paramType": "LIST", "paramTypeReadonly": true, "type": "PARAM"}], "staticData": "{}", "title": "run query with list", "type": "table", "version": "1744699855298968517"}, "n3x2GMkDikkYVEKjDNTWaT": {"chartSettings": {"inputStates": [{"dataType": "string", "id": "input_d7Qpr7ncZzZgXgVcHPEDnc", "name": "action_redirect_variable_for_city", "settings": {}, "widgetType": "Input"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "n3x2GMkDikkYVEKjDNTWaT", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [], "staticData": "{}", "title": "New Widget", "type": "Inputs", "version": "1744785317819753448"}}, "globalParameters": {"action_redirect_variable_for_city": {"id": "input_d7Qpr7ncZzZgXgVcHPEDnc", "name": "action_redirect_variable_for_city", "type": "STRING", "value": ""}, "list_string_variable": {"elementType": "STRING", "id": "input_qh7vEqXgVLJkpFPNam1RLM", "name": "list_string_variable", "type": "LIST", "value": ["<PERSON><PERSON><PERSON>-si", "Cheonan-si2"]}, "map_string_variable": {"id": "input_ikGFNSnS8Jb9eWuHZGGfpv", "keyType": "string", "name": "map_string_variable", "type": "MAP", "value": [{"key": "City1", "value": "<PERSON><PERSON><PERSON>-si"}, {"key": "City2", "value": "Mokpo-<PERSON>"}], "valueType": "string"}, "text_variable": {"id": "input_iKxChJhvLwF4iKqg3qw28h", "name": "text_variable", "type": "STRING", "value": "<PERSON><PERSON><PERSON>-si"}}, "iconURL": "/insights/static/media/library.d3f7f207c6bb1d7be8e64045a19991b2.svg", "id": "5uvYzz5EoNwCKDYWRU95vE", "isDetail": true, "isNew": false, "layouts": {"md": [{"h": 12, "i": "3Y7vJeGJtxZVgNdLNqX642", "moved": false, "static": false, "w": 4, "x": 4, "y": 31}, {"h": 16, "i": "4VgoFJWLXHbVf87aHTFdjf", "moved": false, "static": false, "w": 7, "x": 0, "y": 15}, {"h": 12, "i": "7drTcDjKFvM76Dv54Gt2Ro", "moved": false, "static": false, "w": 4, "x": 7, "y": 12}, {"h": 15, "i": "a4rtM1xTXXa6mcUs87sZue", "moved": false, "static": false, "w": 7, "x": 0, "y": 0}, {"h": 12, "i": "jgVD3qqmaGrJ7v9ZRKUMkX", "moved": false, "static": false, "w": 4, "x": 7, "y": 0}, {"h": 12, "i": "n3x2GMkDikkYVEKjDNTWaT", "moved": false, "static": false, "w": 4, "x": 0, "y": 31}], "xs": [{"h": 12, "i": "a4rtM1xTXXa6mcUs87sZue", "moved": false, "static": false, "w": 4, "x": 0, "y": 0}, {"h": 12, "i": "jgVD3qqmaGrJ7v9ZRKUMkX", "moved": false, "static": false, "w": 4, "x": 0, "y": 12}, {"h": 16, "i": "4VgoFJWLXHbVf87aHTFdjf", "moved": false, "static": false, "w": 4, "x": 0, "y": 24}, {"h": 12, "i": "7drTcDjKFvM76Dv54Gt2Ro", "moved": false, "static": false, "w": 4, "x": 0, "y": 40}, {"h": 12, "i": "n3x2GMkDikkYVEKjDNTWaT", "moved": false, "static": false, "w": 4, "x": 0, "y": 52}, {"h": 12, "i": "3Y7vJeGJtxZVgNdLNqX642", "moved": false, "static": false, "w": 4, "x": 0, "y": 64}]}, "title": "list map variable", "version": "1744785317812429641", "weight": 20}, {"globalParameters": {}, "iconURL": "/insights/static/media/library.d3f7f207c6bb1d7be8e64045a19991b2.svg", "id": "gVEXUx9NwLQ6stM4RRo6pt", "isDetail": true, "isNew": false, "layouts": {"md": [{"h": 12, "i": "2bow2yDaPmY7fjAxpQ8514", "moved": false, "static": false, "w": 4, "x": 4, "y": 0}, {"h": 12, "i": "3K11HNvGp5JahU6CgvTWi7", "moved": false, "static": false, "w": 4, "x": 0, "y": 0}, {"h": 12, "i": "4E8abNadgtYuwSRaTS5Rn2", "moved": false, "static": false, "w": 4, "x": 4, "y": 24}, {"h": 12, "i": "aMkYhMM3ctXLEMjXWq1c84", "moved": false, "static": false, "w": 4, "x": 0, "y": 36}, {"h": 12, "i": "bK5TPEEymktHzVLRvPFMKh", "moved": false, "static": false, "w": 4, "x": 0, "y": 12}, {"h": 12, "i": "fRsqcsaQyjJvr3KHUz2Qhf", "moved": false, "static": false, "w": 4, "x": 0, "y": 24}, {"h": 12, "i": "hQc4weEE92CErX96xLDyke", "moved": false, "static": false, "w": 4, "x": 8, "y": 0}, {"h": 12, "i": "qC9fisoThy7bvSRtB7PwFm", "moved": false, "static": false, "w": 4, "x": 4, "y": 36}, {"h": 12, "i": "qq7ktqyXx7Bqska2qTcP6W", "moved": false, "static": false, "w": 4, "x": 4, "y": 12}], "xs": [{"h": 12, "i": "3K11HNvGp5JahU6CgvTWi7", "moved": false, "static": false, "w": 4, "x": 0, "y": 0}, {"h": 12, "i": "2bow2yDaPmY7fjAxpQ8514", "moved": false, "static": false, "w": 4, "x": 0, "y": 12}, {"h": 12, "i": "bK5TPEEymktHzVLRvPFMKh", "moved": false, "static": false, "w": 4, "x": 0, "y": 24}, {"h": 12, "i": "qq7ktqyXx7Bqska2qTcP6W", "moved": false, "static": false, "w": 4, "x": 0, "y": 36}, {"h": 12, "i": "fRsqcsaQyjJvr3KHUz2Qhf", "moved": false, "static": false, "w": 4, "x": 0, "y": 48}, {"h": 12, "i": "4E8abNadgtYuwSRaTS5Rn2", "moved": false, "static": false, "w": 4, "x": 0, "y": 60}, {"h": 12, "i": "aMkYhMM3ctXLEMjXWq1c84", "moved": false, "static": false, "w": 4, "x": 0, "y": 72}, {"h": 12, "i": "qC9fisoThy7bvSRtB7PwFm", "moved": false, "static": false, "w": 4, "x": 0, "y": 84}, {"h": 12, "i": "hQc4weEE92CErX96xLDyke", "moved": false, "static": false, "w": 4, "x": 0, "y": 96}]}, "title": "widget action", "version": "1744699855257258513", "weight": 30, "chartMap": {"2bow2yDaPmY7fjAxpQ8514": {"chartSettings": {"category": [{"id": "City.id", "type": "string"}], "chartActions": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"isCreatable": false, "name": "action_redirect_variable", "paramGlobalInput": "", "value": "City.id"}, {"isCreatable": false, "name": "list_string_variable", "paramGlobalInput": "", "value": "City.id"}, {"isCreatable": false, "name": "map_string_variable", "paramGlobalInput": "", "value": "City.id"}, {"isCreatable": false, "name": "text_variable", "paramGlobalInput": "", "value": "City.id"}], "text": "pie chart", "url": "", "urlName": "list map variable"}], "tableHeaders": ["Matched pattern", "City"], "tableIndex": 0, "value": [{"id": "City.age", "type": "number"}], "widgetActions": [{"pageID": "wMCwcSRDeEPND4Cc88KjKb", "params": [{"name": "input_json_for_city", "value": "Dong<PERSON>cheon-si"}, {"name": "string_dropdown_for_country", "value": ""}, {"name": "string_input_province", "value": ""}], "text": "widget action", "url": "", "urlName": "json input"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "2bow2yDaPmY7fjAxpQ8514", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "972a29f3-5b04-4bde-b5b0-35c35b14ee33", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}], "staticData": "{}", "title": "Pie chart", "type": "pie", "version": "1744795565048487853"}, "3K11HNvGp5JahU6CgvTWi7": {"chartSettings": {"actionsByType": {"City": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"isCreatable": false, "name": "action_redirect_variable", "paramGlobalInput": "", "value": "id"}], "text": "test", "url": "", "urlName": "list map variable"}]}, "widgetActions": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"name": "action_redirect_variable", "value": "11"}, {"name": "list_string_variable", "value": ""}, {"name": "map_string_variable", "value": ""}, {"name": "text_variable", "value": ""}], "text": "graph action", "url": "", "urlName": "list map variable"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "3K11HNvGp5JahU6CgvTWi7", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "77aee78a-54b6-4f3c-8170-91e3e3e30233", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}], "staticData": "{}", "title": "graph Widget", "type": "internal-graph", "version": "1744699855316416196"}, "4E8abNadgtYuwSRaTS5Rn2": {"chartSettings": {"showColumns": [{"isChecked": true, "name": "Matched pattern"}, {"isChecked": true, "name": "City.id"}, {"isChecked": true, "name": "City.age"}, {"isChecked": true, "name": "City.myDate"}, {"isChecked": true, "name": "City.name"}], "tableActions": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"isCreatable": false, "name": "action_redirect_variable", "paramGlobalInput": "", "value": "City.id"}, {"isCreatable": false, "name": "list_string_variable", "paramGlobalInput": "", "value": "City.id"}, {"isCreatable": false, "name": "map_string_variable", "paramGlobalInput": "", "value": "City.id"}, {"isCreatable": false, "name": "text_variable", "paramGlobalInput": "", "value": "City.id"}], "text": "ta", "url": "", "urlName": "list map variable"}], "tableColumns": [{"id": "Matched pattern", "isChecked": "true", "label": ""}, {"children": [{"children": [{"id": "id", "isChecked": "true", "type": "vertex"}, {"id": "name", "isChecked": "true", "type": "vertex"}, {"id": "age", "isChecked": "true", "type": "vertex"}, {"id": "myDate", "isChecked": "true", "type": "vertex"}], "id": "City", "isChecked": "true", "type": "vertex"}], "id": "Vertices", "isChecked": "true", "isExpanded": true, "label": "Vertices"}, {"children": [], "id": "<PERSON>s", "isChecked": "true", "isExpanded": false, "label": "<PERSON>s"}], "tableHeaders": ["Matched pattern", "City"], "widgetActions": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"name": "action_redirect_variable", "value": "tab widget action"}, {"name": "list_string_variable", "value": "tab widget action"}, {"name": "map_string_variable", "value": "tab widget action"}, {"name": "text_variable", "value": "tab widget action"}], "text": "tab widget action", "url": "", "urlName": "list map variable"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "4E8abNadgtYuwSRaTS5Rn2", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "9d9d035d-807a-4d5c-8025-97b889595763", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}], "staticData": "{}", "title": "table action", "type": "table", "version": "1744699855321453972"}, "aMkYhMM3ctXLEMjXWq1c84": {"chartSettings": {"category": [{"id": "City.id", "type": "string"}], "chartActions": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"isCreatable": false, "name": "action_redirect_variable", "paramGlobalInput": "", "value": "City.id"}, {"isCreatable": false, "name": "list_string_variable", "paramGlobalInput": "", "value": "City.id"}, {"isCreatable": false, "name": "map_string_variable", "paramGlobalInput": "", "value": "City.id"}, {"isCreatable": false, "name": "text_variable", "paramGlobalInput": "", "value": "City.id"}], "text": "sc", "url": "", "urlName": "list map variable"}], "tableHeaders": ["Matched pattern", "City"], "tableIndex": 0, "value": [{"id": "City.age", "type": "number"}], "values": [{"id": "4CSMTwGumHZAUTchPpJ1ie", "label": "City", "labelKey": "id", "name": "City", "styleRule": [], "xaxis": "age", "yaxis": "age"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "aMkYhMM3ctXLEMjXWq1c84", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "d103ec4a-f672-4f1b-a7c0-2d934980fd0e", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}], "staticData": "{}", "title": "scatter chart", "type": "scatter", "version": "1744699855328654031"}, "bK5TPEEymktHzVLRvPFMKh": {"chartSettings": {"category": [{"id": "City.id", "type": "string"}], "chartActions": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"isCreatable": false, "name": "action_redirect_variable", "paramGlobalInput": "", "value": "City.id"}], "text": "test", "url": "", "urlName": "list map variable"}], "showLegend": true, "tableHeaders": ["Matched pattern", "City"], "tableIndex": 0, "value": [{"id": "City.age", "type": "number"}], "widgetActions": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"name": "action_redirect_variable", "value": "123"}, {"name": "list_string_variable", "value": "789"}, {"name": "map_string_variable", "value": "112"}, {"name": "text_variable", "value": "456"}], "text": "widget action", "url": "", "urlName": "list map variable"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "bK5TPEEymktHzVLRvPFMKh", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "2888d18f-80f2-43c9-b7a3-64d8988e41bf", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}], "staticData": "{}", "title": "bar chart", "type": "bar", "version": "1744699855334381378"}, "fRsqcsaQyjJvr3KHUz2Qhf": {"chartSettings": {"category": [{"id": "City.id", "type": "string"}], "chartActions": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"isCreatable": false, "name": "action_redirect_variable", "paramGlobalInput": "", "value": "City.id"}, {"isCreatable": false, "name": "list_string_variable", "paramGlobalInput": "", "value": "City.id"}, {"isCreatable": false, "name": "map_string_variable", "paramGlobalInput": "", "value": "City.id"}, {"name": "text_variable", "value": ""}], "text": "linechar", "url": "", "urlName": "list map variable"}], "showBrush": false, "tableHeaders": ["Matched pattern", "City"], "tableIndex": 0, "value": [{"id": "City.age", "type": "number"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "fRsqcsaQyjJvr3KHUz2Qhf", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "979d5070-dded-47e2-99f0-8675d740d685", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}], "staticData": "{}", "title": "line chart", "type": "line", "version": "1744699855339494010"}, "hQc4weEE92CErX96xLDyke": {"chartSettings": {"actionsByType": {"City": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"isCreatable": false, "name": "action_redirect_variable", "paramGlobalInput": "", "value": "id"}, {"isCreatable": false, "name": "list_string_variable", "paramGlobalInput": "", "value": "id"}, {"isCreatable": false, "name": "map_string_variable", "paramGlobalInput": "", "value": "id"}, {"isCreatable": false, "name": "text_variable", "paramGlobalInput": "", "value": "id"}], "text": "map action", "url": "", "urlName": "list map variable"}], "TravelEvent": [{"pageID": "kCKgu4z11KPfsH8g9dtccK", "params": [{"name": "bool_dropdown", "value": ""}, {"name": "datepicker", "value": ""}, {"name": "datetime_dropdown", "value": ""}, {"name": "datetime_input", "value": ""}, {"isCreatable": false, "name": "input_float_variable", "paramGlobalInput": "", "value": "latitude"}, {"name": "input_number", "value": ""}, {"name": "number_dropdown", "value": ""}, {"name": "number_input", "value": ""}, {"name": "slider", "value": ""}, {"name": "string_dropdown", "value": ""}, {"name": "string_dropdown_from_query", "value": ""}, {"name": "string_input", "value": ""}, {"name": "vertex_input", "value": ""}, {"name": "vertex_variable_test_ui_overlap", "value": ""}], "text": "redirect to input float", "url": "", "urlName": "input variable"}]}, "coordinates": {"TravelEvent": {"latitude": "latitude", "longitude": "longitude"}}, "showEdges": true, "widgetActions": [{"pageID": "kCKgu4z11KPfsH8g9dtccK", "params": [{"name": "bool_dropdown", "value": ""}, {"name": "datepicker", "value": ""}, {"name": "datetime_dropdown", "value": ""}, {"name": "datetime_input", "value": ""}, {"name": "input_float_variable", "value": "1.001"}, {"name": "input_number", "value": ""}, {"name": "number_dropdown", "value": ""}, {"name": "number_input", "value": ""}, {"name": "slider", "value": ""}, {"name": "string_dropdown", "value": ""}, {"name": "string_dropdown_from_query", "value": ""}, {"name": "string_input", "value": ""}, {"name": "vertex_input", "value": ""}, {"name": "vertex_variable_test_ui_overlap", "value": ""}], "text": "map widget action to input float", "url": "", "urlName": "input variable"}]}, "graphName": "MyGraph", "hideWidgetName": false, "id": "hQc4weEE92CErX96xLDyke", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "TravelEvent", "data": "TravelEvent", "id": "0b82a4ba-b2fe-4c81-8bec-133050389a29", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_TravelEvent_0.latitude"}, "label": "TravelEvent.latitude"}], "type": "vertex"}], "staticData": "{}", "title": "map", "type": "map", "version": "1744791804011083232"}, "qC9fisoThy7bvSRtB7PwFm": {"chartSettings": {"values": [{"fontColor": "#000000", "fontSize": 24, "iconColor": "#000000", "iconPostion": "", "iconSize": 24, "id": "wvSKWrodXiut4sMpCF5vMR", "label": "untitled", "labelColor": "#000000", "labelPostion": "", "labelSize": 24, "styleRule": []}], "widgetActions": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"name": "action_redirect_variable", "value": "value action"}, {"name": "list_string_variable", "value": "value action"}, {"name": "map_string_variable", "value": "value action"}, {"name": "text_variable", "value": "value action"}], "text": "value action", "url": "", "urlName": "list map variable"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "qC9fisoThy7bvSRtB7PwFm", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "9e2cd009-ad79-4596-9d2b-abad3ef0b263", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}], "staticData": "{}", "title": "value widget", "type": "value", "version": "1744699855351499115"}, "qq7ktqyXx7Bqska2qTcP6W": {"chartSettings": {"chartActions": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"name": "action_redirect_variable", "value": "sankey action"}, {"name": "list_string_variable", "value": "sankey action"}, {"name": "map_string_variable", "value": "sankey action"}, {"name": "text_variable", "value": "sankey action"}], "text": "sankey action", "url": "", "urlName": "list map variable"}], "widgetActions": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"name": "action_redirect_variable", "value": "sankey chart "}, {"name": "list_string_variable", "value": "sankey chart "}, {"name": "map_string_variable", "value": "sankey chart "}, {"name": "text_variable", "value": "sankey chart "}], "text": "sankey chart action", "url": "", "urlName": "list map variable"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "qq7ktqyXx7Bqska2qTcP6W", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "Country", "data": "Country", "id": "bc06ec04-888c-48b6-8b58-dffdf7b53321", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_Country_0.name"}, "label": "Country.name"}], "type": "vertex"}, {"alias": "multi_edge_4", "data": "multi_edge_4", "direction": 0, "edgePairs": [], "from": "Country", "id": "e94dba84-e073-4351-9ba6-665ecd1354a7", "to": "Country", "type": "edge"}, {"alias": "Country_2", "data": "Country", "id": "bc06ec04-888c-48b6-8b58-dffdf7b53321", "type": "vertex"}], "staticData": "{}", "title": "sankey chart ", "type": "sankey", "version": "1744699855356571269"}}}, {"chartMap": {"1ArBixYQzECKUiDM19s9g6": {"chartSettings": {}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "1ArBixYQzECKUiDM19s9g6", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "911da4a1-b9fc-4d81-b20f-5c05bd9f6905", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}, {"data": "slider", "id": "50fb60b1-3ed6-4dbd-8d6b-4a7d1f3525a7", "paramGlobalInput": "slider", "paramName": "vertex_limit", "paramOperator": "eq", "paramType": "INT", "type": "PARAM"}], "staticData": "{}", "title": "slider", "type": "internal-graph", "version": "1744699855363601980"}, "3dpbT4qkReoYkeH6okto5g": {"chartSettings": {"inputStates": [{"dataType": "number", "id": "input_7NKbCUjWaSvAb3okmYn73c", "name": "number_input", "placeholder": "input_number", "settings": {}, "value": "", "widgetType": "Input"}, {"dataType": "string", "id": "input_qypfKNjtGc8C73sGmUGpuF", "label": "", "name": "string_input", "placeholder": "city id", "settings": {}, "widgetType": "Input"}, {"dataType": "vertex", "id": "input_7Ebj3nKpbsq3Uvfdfb3rZT", "name": "vertex_input", "selectedGraph": "MyGraph", "settings": {}, "value": "", "widgetType": "Input"}, {"dataType": "vertex", "id": "input_958M8Tm2corqti44cVPwqU", "name": "vertex_variable_test_ui_overlap", "selectedGraph": "mysql_yuling", "settings": {}, "value": "", "widgetType": "Input"}, {"dataType": "datetime", "id": "input_ojvc3SVFXALCLzFx9kPJ7d", "name": "datetime_input", "settings": {}, "value": "", "widgetType": "Input"}, {"dataType": "datetime", "id": "input_nJSTHpqPJM4AkwCo6CeMxp", "name": "datepicker", "settings": {}, "widgetType": "Datepicker"}, {"dataType": "number", "id": "input_7N4vTsQuXP7i1aTvtssXvt", "name": "number_dropdown", "settings": {"options": [{"isCreatable": true, "label": "label1", "value": "1"}, {"isCreatable": true, "label": "label2", "value": "2"}]}, "value": "", "widgetType": "Dropdown"}, {"dataType": "string", "id": "input_3VGk2JhDPLnNfmmS37efcU", "name": "string_dropdown", "settings": {"options": [{"isCreatable": true, "label": "label1", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"isCreatable": true, "label": "label2", "value": "Seongbuk-gu"}]}, "value": "", "widgetType": "Dropdown"}, {"dataType": "string", "id": "input_7ArcpF9QsDdtfGchbQQyUg", "name": "string_dropdown_from_query", "settings": {"graphName": "MyMultiEdge", "labelKey": "v_id", "open": false, "options": [], "patternLimit": 300, "query": "", "searchPattern": [{"data": "test_input_vertex_from_query", "id": "test_input_vertex_from_query", "type": "QUERY"}, {"data": "10", "id": "91840cbd-2ad2-4ace-b6c8-0215495a5b15", "paramGlobalInput": "", "paramName": "num", "paramType": "INT", "paramTypeReadonly": true, "type": "PARAM"}], "staticData": "{}", "tableHeaders": ["v_id", "v_type", "age", "myDate", "name"], "useQuery": true, "valueKey": "v_id"}, "widgetType": "Dropdown"}, {"dataType": "datetime", "id": "input_eDpziDuL3DhaL3eTpdSiT2", "name": "datetime_dropdown", "settings": {"options": [{"isCreatable": true, "label": "l1", "value": "2020-03-08 00:00:00"}, {"isCreatable": true, "label": "l2", "value": "2020-02-20 00:00:00"}, {"isCreatable": true, "label": "l3", "value": "2020-02-10 00:00:00"}]}, "value": "", "widgetType": "Dropdown"}, {"dataType": "bool", "id": "input_56bPGmuaGjfBPY6ngewXKy", "name": "bool_dropdown", "settings": {}, "value": "", "widgetType": "Dropdown"}, {"dataType": "number", "id": "input_dDYTSYpCbeC8NWqDbe5pxA", "name": "slider", "settings": {"max": 100, "min": 0, "step": 1}, "widgetType": "Slide<PERSON>"}], "invisibleInPreviewMode": false}, "graphName": "MyGraph", "hideWidgetName": false, "id": "3dpbT4qkReoYkeH6okto5g", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [], "staticData": "{}", "title": "New Widget", "type": "Inputs", "version": "1744704861701785128"}, "7dXkemeQJTcwpZ54t9qutg": {"chartSettings": {"layout": "Force", "rulesByType": {"Patient": [{"condition": "=", "conditionEndValue": 0, "conditionStartValue": 0, "conditionValue": "<PERSON>", "fieldName": "id", "fieldType": "string", "palateName": "", "styleEndLabel": "", "styleEndValue": "", "styleKey": "background-color", "styleLabel": "Vertex color", "styleStartLabel": "", "styleStartValue": "", "styleType": "color", "styleValue": "#69ff00"}, {"condition": "=", "conditionEndValue": 0, "conditionStartValue": 0, "conditionValue": "<PERSON>", "fieldName": "id", "fieldType": "string", "palateName": "", "styleEndLabel": "", "styleEndValue": "", "styleKey": "node-radius", "styleLabel": "4x", "styleStartLabel": "", "styleStartValue": "", "styleType": "numeric", "styleValue": 4}, {"condition": "=", "conditionEndValue": 0, "conditionStartValue": 0, "conditionValue": "<PERSON>", "fieldName": "id", "fieldType": "string", "palateName": "", "styleEndLabel": "", "styleEndValue": "", "styleKey": "background-color", "styleLabel": "Vertex color", "styleStartLabel": "", "styleStartValue": "", "styleType": "color", "styleValue": "#fffc00"}]}}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "7dXkemeQJTcwpZ54t9qutg", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "Patient", "data": "Patient", "id": "5f68d818-31a3-4041-be28-28f093e1c93a", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_Patient_0.name"}, "label": "Patient.name"}], "type": "vertex"}, {"data": "datepicker", "id": "b1b0850a-2c58-436d-9252-71209f9920e3", "paramGlobalInput": "datepicker", "paramName": "birthday", "paramOperator": "lt", "paramType": "DATETIME", "type": "PARAM"}], "staticData": "{}", "title": "datetime_picker", "type": "internal-graph", "version": "1744794326367864679"}, "akjctRomBmZ67xmHS98Hb6": {"chartSettings": {}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "akjctRomBmZ67xmHS98Hb6", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"data": "my_page_rank", "id": "my_page_rank", "type": "QUERY"}, {"data": "number_input", "id": "ca2e2d64-3a1f-4b29-9881-1a144e9d9f32", "paramGlobalInput": "number_input", "paramName": "max_change", "paramType": "FLOAT", "paramTypeReadonly": true, "type": "PARAM"}, {"data": "number_input", "id": "c6939267-60ea-46cf-9ef6-c4ee6e92ac1e", "paramGlobalInput": "number_input", "paramName": "max_iter", "paramType": "INT", "paramTypeReadonly": true, "type": "PARAM"}, {"data": "number_input", "id": "d7dbca1d-ae37-4481-8892-9f07e3a48bd4", "paramGlobalInput": "number_input", "paramName": "damping", "paramType": "FLOAT", "paramTypeReadonly": true, "type": "PARAM"}, {"data": "bool_dropdown", "id": "c0b09e7d-bc55-48fe-92e4-9e1d012af445", "paramGlobalInput": "bool_dropdown", "paramName": "display", "paramType": "BOOL", "paramTypeReadonly": true, "type": "PARAM"}, {"data": "number_input", "id": "6bb2b9d1-89fa-4f2b-bea3-adb97db9bef3", "paramGlobalInput": "number_input", "paramName": "output_limit", "paramType": "INT", "paramTypeReadonly": true, "type": "PARAM"}], "staticData": "{}", "title": "bool_dropdown", "type": "internal-graph", "version": "1744699855380859129"}, "br5ksyvQghpoiGS3GeNsJx": {"chartSettings": {"actionsByType": {"City": [{"pageID": "wMCwcSRDeEPND4Cc88KjKb", "params": [{"isCreatable": false, "name": "input_json_for_city", "paramGlobalInput": "", "value": "id"}, {"name": "", "value": ""}], "text": "redirect to input json city", "url": "", "urlName": "New Page"}]}, "rulesByType": {"City": [{"condition": "=", "conditionEndValue": 0, "conditionStartValue": 0, "conditionValue": "0", "fieldName": "age", "fieldType": "number", "palateName": "", "styleEndLabel": "", "styleEndValue": "", "styleKey": "background-color", "styleLabel": "Vertex color", "styleStartLabel": "", "styleStartValue": "", "styleType": "color", "styleValue": "#ff0000"}, {"condition": ">", "conditionEndValue": 0, "conditionStartValue": 0, "conditionValue": "0", "fieldName": "age", "fieldType": "number", "palateName": "", "styleEndLabel": "", "styleEndValue": "", "styleKey": "node-radius", "styleLabel": "4x", "styleStartLabel": "", "styleStartValue": "", "styleType": "numeric", "styleValue": 4}]}, "showColumns": [{"isChecked": true, "name": "v_id"}, {"isChecked": true, "name": "v_type"}, {"isChecked": true, "name": "academy_ratio"}, {"isChecked": true, "name": "city"}, {"isChecked": true, "name": "elderly_alone_ratio"}, {"isChecked": true, "name": "elderly_population_ratio"}, {"isChecked": true, "name": "elementary_school_count"}, {"isChecked": true, "name": "kindergarten_count"}, {"isChecked": true, "name": "nursing_home_count"}, {"isChecked": true, "name": "university_count"}], "tableColumns": [{"id": "Matched pattern", "isChecked": "true", "label": ""}, {"children": [], "id": "Vertices", "isChecked": "true", "isExpanded": true, "label": "Vertices"}, {"children": [], "id": "<PERSON>s", "isChecked": "true", "isExpanded": false, "label": "<PERSON>s"}], "tableHeaders": ["v_id", "v_type", "academy_ratio", "city", "elderly_alone_ratio", "elderly_population_ratio", "elementary_school_count", "kindergarten_count", "nursing_home_count", "university_count"]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "br5ksyvQghpoiGS3GeNsJx", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"data": "test_int_input_for_installed_query", "id": "test_int_input_for_installed_query", "type": "QUERY"}, {"data": "number_input", "id": "861222d1-decf-444a-8165-44e07af20668", "paramGlobalInput": "number_input", "paramName": "num", "paramType": "INT", "paramTypeReadonly": true, "type": "PARAM"}], "staticData": "{}", "title": "input_num for installed query", "type": "internal-graph", "version": "1744794077031007097"}, "bz8VuSeheiBGfhndc5eH3Z": {"chartSettings": {"category": [{"id": "City.id", "type": "string"}], "chartActions": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"isCreatable": false, "name": "action_redirect_variable_for_city", "paramGlobalInput": "", "value": "City.id"}, {"isCreatable": false, "name": "list_string_variable", "paramGlobalInput": "", "value": "City.id"}, {"isCreatable": false, "name": "map_string_variable", "paramGlobalInput": "", "value": "City.id"}, {"isCreatable": false, "name": "text_variable", "paramGlobalInput": "", "value": "City.id"}], "text": "redirect to list map city all variable", "url": "", "urlName": "list map variable"}], "lowerBoundParameterName": "string_input", "series": [], "showBrush": true, "tableHeaders": ["Matched pattern", "City", "Province", "multi_edge_1"], "tableIndex": 0, "upperBoundParameterName": "string_input", "value": [{"id": "City.age", "type": "number"}, {"id": "multi_edge_1.account", "type": "number"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "bz8VuSeheiBGfhndc5eH3Z", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "d3bb7172-62e0-4feb-bf35-1c47a04fa187", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}, {"data": "string_input", "id": "d913a2fe-1d91-411d-93fa-94a0b69a80a0", "paramGlobalInput": "string_input", "paramName": "id", "paramType": "STRING", "type": "PARAM"}, {"alias": "multi_edge_1", "data": "multi_edge_1", "direction": -1, "edgePairs": [], "from": "Province", "id": "fe7e1364-4115-400b-81cf-30213415d378", "to": "City", "type": "edge"}, {"alias": "Province", "data": "Province", "id": "2b89a892-75ce-4abd-bd1d-cca0e835ebbc", "type": "vertex"}], "staticData": "{}", "title": "input_string_bar_chart", "type": "bar", "version": "1744785956797096356"}, "c4TMhcPu5yjkaS29a9BXXT": {"chartSettings": {}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "c4TMhcPu5yjkaS29a9BXXT", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "f23519b0-bedc-4658-95df-afe75144c997", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}, {"data": "datetime_dropdown", "id": "32039683-cb93-4571-8df5-704426797372", "paramGlobalInput": "datetime_dropdown", "paramName": "myDate", "paramOperator": "lt", "paramType": "DATETIME", "type": "PARAM"}], "staticData": "{}", "title": "datetime_dropdown", "type": "internal-graph", "version": "1744699855398055999"}, "c8v3BTQwX5KxcP1rTZUmZq": {"chartSettings": {"category": [{"id": "Province.id", "type": "string"}], "chartActions": [{"pageID": "wMCwcSRDeEPND4Cc88KjKb", "params": [{"name": "input_json_for_city", "value": ""}, {"isCreatable": false, "name": "string_input_province", "paramGlobalInput": "", "value": "Province.id"}], "text": "redirect json input province", "url": "", "urlName": "json input"}], "lowerBoundParameterName": "input_number", "showBrush": true, "showColumns": [{"isChecked": true, "name": "Matched pattern"}, {"isChecked": true, "name": "Province.id"}, {"isChecked": true, "name": "Province.age"}, {"isChecked": true, "name": "Province.myDate"}, {"isChecked": true, "name": "Province.name"}], "tableColumns": [{"id": "Matched pattern", "isChecked": "true", "label": ""}, {"children": [{"children": [{"id": "id", "isChecked": "true", "type": "vertex"}, {"id": "name", "isChecked": "true", "type": "vertex"}, {"id": "age", "isChecked": "true", "type": "vertex"}, {"id": "myDate", "isChecked": "true", "type": "vertex"}], "id": "Province", "isChecked": "true", "type": "vertex"}], "id": "Vertices", "isChecked": "true", "isExpanded": true, "label": "Vertices"}, {"children": [], "id": "<PERSON>s", "isChecked": "true", "isExpanded": false, "label": "<PERSON>s"}], "tableHeaders": ["Matched pattern", "Province"], "tableIndex": 0, "upperBoundParameterName": "input_number", "value": [{"id": "Province.age", "type": "number"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "c8v3BTQwX5KxcP1rTZUmZq", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "Province", "data": "Province", "id": "6c59d2f7-78ed-4b40-a80f-78fb88c5693f", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_Province_0.name"}, "label": "Province.name"}], "type": "vertex"}, {"data": "string_dropdown_from_query", "id": "b7b92db8-2c1c-4089-9130-ebf9b6268dde", "paramGlobalInput": "string_dropdown_from_query", "paramName": "id", "paramType": "STRING", "type": "PARAM"}], "staticData": "{}", "title": "string_dropdown_from_query_Pie_chart", "type": "pie", "version": "1744785219217834986"}, "cZXdAf6ZC2MWHHecZmGNoK": {"chartSettings": {"showColumns": [{"isChecked": true, "name": "display"}], "tableColumns": [{"id": "Matched pattern", "isChecked": "true", "label": ""}, {"children": [], "id": "Vertices", "isChecked": "true", "isExpanded": true, "label": "Vertices"}, {"children": [], "id": "<PERSON>s", "isChecked": "true", "isExpanded": false, "label": "<PERSON>s"}], "tableHeaders": ["display"]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "cZXdAf6ZC2MWHHecZmGNoK", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"data": "my_page_rank_for_interpreted_query", "id": "my_page_rank_for_interpreted_query", "type": "QUERY"}, {"data": "input_float_variable", "id": "5390d30a-95e5-4837-8723-2fef5e6e07ae", "paramGlobalInput": "input_float_variable", "paramName": "max_change", "paramType": "FLOAT", "paramTypeReadonly": true, "type": "PARAM"}, {"data": "number_input", "id": "3e11acc7-8937-4f8c-9969-92a79c321417", "paramGlobalInput": "number_input", "paramName": "max_iter", "paramType": "INT", "paramTypeReadonly": true, "type": "PARAM"}, {"data": "input_float_variable", "id": "ff2a09ca-3c2e-4949-9e4e-52b0a97be171", "paramGlobalInput": "input_float_variable", "paramName": "damping", "paramType": "FLOAT", "paramTypeReadonly": true, "type": "PARAM"}, {"data": "bool_dropdown", "id": "592e559b-5f94-4cf1-88c4-8c89ba526a55", "paramGlobalInput": "bool_dropdown", "paramName": "display", "paramType": "BOOL", "paramTypeReadonly": true, "type": "PARAM"}, {"data": "number_input", "id": "b0d58cdc-55b0-4c7a-a431-39fab12d6c02", "paramGlobalInput": "number_input", "paramName": "output_limit", "paramType": "INT", "paramTypeReadonly": true, "type": "PARAM"}], "staticData": "{}", "title": "table for interpreted query", "type": "table", "version": "1744702082213962495"}, "eVL5XdkBzkUpBC9aquLU9S": {"chartSettings": {}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "eVL5XdkBzkUpBC9aquLU9S", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "15d99c49-4b0c-406b-9197-d0c59cc54fdc", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}, {"data": "number_dropdown", "id": "35a05690-978a-4acd-9ebf-65a039e90864", "paramGlobalInput": "number_dropdown", "paramName": "vertex_limit", "paramOperator": "eq", "paramType": "INT", "type": "PARAM"}, {"alias": "multi_edge_2", "data": "multi_edge_2", "direction": 1, "edgePairs": [], "from": "City", "id": "e51e2309-4bb8-43a0-a243-3fdf6157e688", "to": "Province", "type": "edge"}, {"alias": "Province", "data": "Province", "id": "fc814e29-9aaa-4dbc-8db0-abad50dda6e7", "type": "vertex"}], "staticData": "{}", "title": "number_dropdown", "type": "internal-graph", "version": "1744699855412222382"}, "fmb8WXCZwWGFjhFVgetERL": {"chartSettings": {"category": [{"id": "score", "type": "number"}], "rulesByType": {"Patient": [{"condition": "always", "conditionEndValue": 0, "conditionStartValue": 0, "conditionValue": "", "fieldName": "Start.@score", "fieldType": "number", "palateName": "", "styleEndLabel": "", "styleEndValue": "", "styleKey": "background-color", "styleLabel": "Vertex color", "styleStartLabel": "", "styleStartValue": "", "styleType": "color", "styleValue": "#ff0000"}]}, "series": [{"id": "Vertex_ID", "type": "string"}], "tableHeaders": ["Vertex_ID", "score"], "tableIndex": 0, "value": [{"id": "score", "type": "number"}], "valueAggregation": [{"id": "COUNT"}], "values": [{"id": "xohAVhMfQsFgRYjArWkmHa", "label": "Patient", "labelKey": "id", "name": "Patient", "styleRule": [], "xaxis": "id", "yaxis": "Start.@score"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "fmb8WXCZwWGFjhFVgetERL", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"data": "my_page_rank", "id": "my_page_rank", "type": "QUERY"}, {"data": "input_float_variable", "id": "44ea6642-54d4-4453-8d3e-771d6aae964f", "paramGlobalInput": "input_float_variable", "paramName": "max_change", "paramType": "FLOAT", "paramTypeReadonly": true, "type": "PARAM"}, {"data": "number_input", "id": "7d7db027-c83f-4f22-a88a-74cbd1cd33db", "paramGlobalInput": "number_input", "paramName": "max_iter", "paramType": "INT", "paramTypeReadonly": true, "type": "PARAM"}, {"data": "input_float_variable", "id": "4ba99650-4bd1-40d3-82e1-7eb7e2bc9208", "paramGlobalInput": "input_float_variable", "paramName": "damping", "paramType": "FLOAT", "paramTypeReadonly": true, "type": "PARAM"}, {"data": "bool_dropdown", "id": "ff0b5383-6b44-4755-a5a9-f7ce4e1332c3", "paramGlobalInput": "bool_dropdown", "paramName": "display", "paramType": "BOOL", "paramTypeReadonly": true, "type": "PARAM"}, {"data": "number_dropdown", "id": "32bac427-3347-4e66-bb48-3138dd456bd8", "paramGlobalInput": "number_dropdown", "paramName": "output_limit", "paramType": "INT", "paramTypeReadonly": true, "type": "PARAM"}], "staticData": "{}", "title": "my pagerank for  input number and bool dropdown", "type": "internal-graph", "version": "1744699855417556420"}, "gKs3Z1RJoGXy1aKdP6jQRx": {"chartSettings": {"actionsByType": {"City": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"isCreatable": false, "name": "action_redirect_variable", "paramGlobalInput": "", "value": "id"}, {"isCreatable": false, "name": "list_string_variable", "paramGlobalInput": "", "value": "id"}, {"isCreatable": false, "name": "map_string_variable", "paramGlobalInput": "", "value": "id"}, {"isCreatable": false, "name": "text_variable", "paramGlobalInput": "", "value": "id"}], "text": "redirect_list_map_all_variable", "url": "", "urlName": "list map variable"}]}}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "gKs3Z1RJoGXy1aKdP6jQRx", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "b8b6dc48-bc09-4e66-a9a1-5986dc901d64", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}, {"data": "string_dropdown", "id": "9d4c02d3-0484-408e-87c3-d6c8a3d6d013", "paramGlobalInput": "string_dropdown", "paramName": "id", "paramOperator": "eq", "paramType": "STRING", "type": "PARAM"}], "staticData": "{}", "title": "string_dropdown_pattern_search", "type": "internal-graph", "version": "1744788565924905929"}, "ny8B2gyzMyuiskYyTdcFXC": {"chartSettings": {"actionsByType": {"Province": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"isCreatable": false, "name": "variable", "paramGlobalInput": "input_number", "value": "input_number"}], "text": "to list map variable page with number", "url": "", "urlName": "list map variable"}]}, "showColumns": [{"isChecked": true, "name": "v_id"}, {"isChecked": true, "name": "v_type"}, {"isChecked": true, "name": "age"}, {"isChecked": true, "name": "myDate"}, {"isChecked": true, "name": "name"}], "styleRules": [{"columnName": "v_id", "condition": "=", "conditionEndValue": 0, "conditionStartValue": 0, "conditionValue": "Chrome", "fieldName": "v_id", "fieldType": "string", "palateName": "", "styleEndLabel": "", "styleEndValue": "", "styleKey": "backgroundColor", "styleLabel": "Row Color", "styleStartLabel": "", "styleStartValue": "", "styleType": "color", "styleValue": "#ff0000"}, {"condition": "=", "conditionEndValue": 0, "conditionStartValue": 0, "conditionValue": "Internet Explorer", "fieldName": "v_id", "fieldType": "string", "palateName": "", "styleEndLabel": "", "styleEndValue": "", "styleKey": "backgroundColor", "styleLabel": "Row Color", "styleStartLabel": "", "styleStartValue": "", "styleType": "color", "styleValue": "#14ff00"}], "tableActions": [{"pageID": "wMCwcSRDeEPND4Cc88KjKb", "params": [{"isCreatable": false, "name": "string_input_province", "paramGlobalInput": "", "value": "v_id"}], "text": "redirect_to_json_input_province", "url": "", "urlName": "json input"}], "tableColumns": [{"id": "Matched pattern", "isChecked": "true", "label": ""}, {"children": [], "id": "Vertices", "isChecked": "true", "isExpanded": true, "label": "Vertices"}, {"children": [], "id": "<PERSON>s", "isChecked": "true", "isExpanded": false, "label": "<PERSON>s"}], "tableHeaders": ["v_id", "v_type", "age", "myDate", "name"]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "ny8B2gyzMyuiskYyTdcFXC", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"data": "test_int_input_for_interpreted_query", "id": "test_int_input_for_interpreted_query", "type": "QUERY"}, {"data": "number_input", "id": "d1d97d58-060f-4fc2-89fb-055b2365f27a", "paramGlobalInput": "number_input", "paramName": "num", "paramType": "INT", "paramTypeReadonly": true, "type": "PARAM"}], "staticData": "{}", "title": "input num for interpreted query", "type": "table", "version": "1744794402917408760"}, "pfHWAr1kLfs2LuDbePkFeR": {"chartSettings": {"inputStates": [{"dataType": "number", "id": "input_sAkhodSfc7Ncm9rk2saevV", "name": "input_float_variable", "settings": {"max": "999", "min": "10", "step": "0.1"}, "value": "", "widgetType": "Input"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "pfHWAr1kLfs2LuDbePkFeR", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [], "staticData": "{}", "title": "New Widget", "type": "Inputs", "version": "1744699855434775601"}, "tftg72mGgBwT9QHtQNPqYM": {"chartSettings": {"actionsByType": {"City": [{"pageID": "5uvYzz5EoNwCKDYWRU95vE", "params": [{"isCreatable": false, "name": "action_redirect_variable_for_city", "paramGlobalInput": "", "value": "id"}], "text": "redirect to list map city", "url": "", "urlName": "list map variable"}]}, "rulesByType": {"multi_edge_2": [{"condition": ">=", "conditionEndValue": 0, "conditionStartValue": 0, "conditionValue": "4000000043", "fieldName": "name", "fieldType": "string", "palateName": "", "styleEndLabel": "", "styleEndValue": "", "styleKey": "line-color", "styleLabel": "Edge color", "styleStartLabel": "", "styleStartValue": "", "styleType": "color", "styleValue": "#00ff03"}, {"condition": "=", "conditionEndValue": 0, "conditionStartValue": 0, "conditionValue": "2020-03-25 00:00:00", "fieldName": "myDate", "fieldType": "string", "palateName": "", "styleEndLabel": "", "styleEndValue": "", "styleKey": "line-style", "styleLabel": {"_owner": null, "key": null, "props": {"children": {"_owner": null, "key": null, "props": {"style": {"stroke": "black", "strokeDasharray": "10 5", "strokeWidth": 2}, "x1": "0", "x2": "200", "y1": "5", "y2": "5"}, "ref": null, "type": "line"}, "height": "10", "width": "100%"}, "ref": null, "type": "svg"}, "styleStartLabel": "", "styleStartValue": "", "styleType": "edgetype", "styleValue": "dashed"}, {"condition": "<", "conditionEndValue": 0, "conditionStartValue": 0, "conditionValue": "4000000043", "fieldName": "name", "fieldType": "string", "palateName": "", "styleEndLabel": "", "styleEndValue": "", "styleKey": "edge-width", "styleLabel": "6", "styleStartLabel": "", "styleStartValue": "", "styleType": "numeric", "styleValue": "6"}]}}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "tftg72mGgBwT9QHtQNPqYM", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"data": "province_all_connection", "id": "province_all_connection", "type": "QUERY"}, {"data": "vertex_input", "id": "22a39c1b-153a-4897-98af-ecb9fb427bae", "paramGlobalInput": "vertex_input", "paramName": "p", "paramType": "VERTEX", "paramTypeReadonly": true, "type": "PARAM", "vertexType": "Province"}], "staticData": "{}", "title": "vertex_input", "type": "internal-graph", "version": "1744793905793244118"}, "x8hdZoaXEbE5e8DYFNwWk2": {"chartSettings": {"actionsByType": {"Country": [{"pageID": "wMCwcSRDeEPND4Cc88KjKb", "params": [{"name": "input_json_for_city", "value": ""}, {"isCreatable": false, "name": "string_dropdown_for_country", "paramGlobalInput": "", "value": "id"}, {"name": "string_input_province", "value": ""}], "text": "redirect json input for contry", "url": "", "urlName": "json input"}]}}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "x8hdZoaXEbE5e8DYFNwWk2", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "Country", "data": "Country", "id": "0596aecd-4d57-4d7a-838e-c071c631a5cc", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_Country_0.name"}, "label": "Country.name"}], "type": "vertex"}, {"data": "datetime_input", "id": "c4b862c4-0c47-4ff8-958c-772659108f6e", "paramGlobalInput": "datetime_input", "paramName": "birthday", "paramOperator": "lt", "paramType": "DATETIME", "type": "PARAM"}], "staticData": "{}", "title": "datetime_input", "type": "internal-graph", "version": "1744786871423433152"}}, "globalParameters": {"bool_dropdown": {"elementType": "STRING", "id": "input_56bPGmuaGjfBPY6ngewXKy", "name": "bool_dropdown", "type": "BOOLEAN", "value": true}, "datepicker": {"id": "input_nJSTHpqPJM4AkwCo6CeMxp", "name": "datepicker", "type": "DATETIME", "value": "2025-04-02 03:06:12"}, "datetime_dropdown": {"id": "input_eDpziDuL3DhaL3eTpdSiT2", "name": "datetime_dropdown", "type": "DATETIME", "value": "2025-04-02 04:23:00"}, "datetime_input": {"id": "input_ojvc3SVFXALCLzFx9kPJ7d", "name": "datetime_input", "type": "DATETIME", "value": "2025-04-02 04:56:42"}, "input_float_variable": {"id": "input_sAkhodSfc7Ncm9rk2saevV", "name": "input_float_variable", "type": "NUMBER", "value": "5.075"}, "input_number": {"value": "Chrome"}, "number_dropdown": {"id": "input_7N4vTsQuXP7i1aTvtssXvt", "name": "number_dropdown", "type": "NUMBER", "value": "1"}, "number_input": {"id": "input_7NKbCUjWaSvAb3okmYn73c", "name": "number_input", "type": "NUMBER", "value": "2"}, "slider": {"id": "input_dDYTSYpCbeC8NWqDbe5pxA", "name": "slider", "type": "NUMBER", "value": 0}, "string_dropdown": {"id": "input_3VGk2JhDPLnNfmmS37efcU", "name": "string_dropdown", "type": "STRING", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "string_dropdown_from_query": {"id": "input_7ArcpF9QsDdtfGchbQQyUg", "name": "string_dropdown_from_query", "type": "STRING", "value": "Chrome"}, "string_input": {"id": "input_qypfKNjtGc8C73sGmUGpuF", "name": "string_input", "type": "STRING", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "vertex_input": {"id": "input_7Ebj3nKpbsq3Uvfdfb3rZT", "name": "vertex_input", "type": "VERTEX", "value": {"vertexID": "Chungcheongbuk-do", "vertexType": "Patient"}}, "vertex_variable_test_ui_overlap": {"id": "input_958M8Tm2corqti44cVPwqU", "name": "vertex_variable_test_ui_overlap", "type": "VERTEX", "value": {"vertexID": "1", "vertexType": "tb_emp1"}}}, "iconURL": "/insights/static/media/library.d3f7f207c6bb1d7be8e64045a19991b2.svg", "id": "kCKgu4z11KPfsH8g9dtccK", "isDetail": true, "isNew": false, "layouts": {"md": [{"h": 12, "i": "1ArBixYQzECKUiDM19s9g6", "moved": false, "static": false, "w": 4, "x": 4, "y": 53}, {"h": 17, "i": "3dpbT4qkReoYkeH6okto5g", "isResizable": true, "moved": false, "static": false, "w": 12, "x": 0, "y": 0}, {"h": 12, "i": "7dXkemeQJTcwpZ54t9qutg", "moved": false, "static": false, "w": 4, "x": 8, "y": 29}, {"h": 12, "i": "akjctRomBmZ67xmHS98Hb6", "moved": false, "static": false, "w": 4, "x": 0, "y": 65}, {"h": 12, "i": "br5ksyvQghpoiGS3GeNsJx", "moved": false, "static": false, "w": 4, "x": 0, "y": 29}, {"h": 12, "i": "bz8VuSeheiBGfhndc5eH3Z", "moved": false, "static": false, "w": 4, "x": 8, "y": 17}, {"h": 12, "i": "c4TMhcPu5yjkaS29a9BXXT", "moved": false, "static": false, "w": 4, "x": 8, "y": 53}, {"h": 12, "i": "c8v3BTQwX5KxcP1rTZUmZq", "moved": false, "static": false, "w": 4, "x": 8, "y": 41}, {"h": 12, "i": "cZXdAf6ZC2MWHHecZmGNoK", "moved": false, "static": false, "w": 4, "x": 8, "y": 65}, {"h": 12, "i": "eVL5XdkBzkUpBC9aquLU9S", "moved": false, "static": false, "w": 4, "x": 0, "y": 53}, {"h": 12, "i": "fmb8WXCZwWGFjhFVgetERL", "moved": false, "static": false, "w": 4, "x": 4, "y": 65}, {"h": 12, "i": "gKs3Z1RJoGXy1aKdP6jQRx", "moved": false, "static": false, "w": 4, "x": 4, "y": 41}, {"h": 12, "i": "ny8B2gyzMyuiskYyTdcFXC", "moved": false, "static": false, "w": 4, "x": 4, "y": 17}, {"h": 12, "i": "pfHWAr1kLfs2LuDbePkFeR", "moved": false, "static": false, "w": 4, "x": 0, "y": 17}, {"h": 12, "i": "tftg72mGgBwT9QHtQNPqYM", "moved": false, "static": false, "w": 4, "x": 0, "y": 41}, {"h": 12, "i": "x8hdZoaXEbE5e8DYFNwWk2", "moved": false, "static": false, "w": 4, "x": 4, "y": 29}], "xs": [{"h": 17, "i": "3dpbT4qkReoYkeH6okto5g", "isResizable": true, "moved": false, "static": false, "w": 4, "x": 0, "y": 0}, {"h": 12, "i": "pfHWAr1kLfs2LuDbePkFeR", "moved": false, "static": false, "w": 4, "x": 0, "y": 17}, {"h": 12, "i": "ny8B2gyzMyuiskYyTdcFXC", "moved": false, "static": false, "w": 4, "x": 0, "y": 29}, {"h": 12, "i": "bz8VuSeheiBGfhndc5eH3Z", "moved": false, "static": false, "w": 4, "x": 0, "y": 41}, {"h": 12, "i": "br5ksyvQghpoiGS3GeNsJx", "moved": false, "static": false, "w": 4, "x": 0, "y": 53}, {"h": 12, "i": "x8hdZoaXEbE5e8DYFNwWk2", "moved": false, "static": false, "w": 4, "x": 0, "y": 65}, {"h": 12, "i": "7dXkemeQJTcwpZ54t9qutg", "moved": false, "static": false, "w": 4, "x": 0, "y": 77}, {"h": 12, "i": "tftg72mGgBwT9QHtQNPqYM", "moved": false, "static": false, "w": 4, "x": 0, "y": 89}, {"h": 12, "i": "gKs3Z1RJoGXy1aKdP6jQRx", "moved": false, "static": false, "w": 4, "x": 0, "y": 101}, {"h": 12, "i": "c8v3BTQwX5KxcP1rTZUmZq", "moved": false, "static": false, "w": 4, "x": 0, "y": 113}, {"h": 12, "i": "eVL5XdkBzkUpBC9aquLU9S", "moved": false, "static": false, "w": 4, "x": 0, "y": 125}, {"h": 12, "i": "1ArBixYQzECKUiDM19s9g6", "moved": false, "static": false, "w": 4, "x": 0, "y": 137}, {"h": 12, "i": "c4TMhcPu5yjkaS29a9BXXT", "moved": false, "static": false, "w": 4, "x": 0, "y": 149}, {"h": 12, "i": "akjctRomBmZ67xmHS98Hb6", "moved": false, "static": false, "w": 4, "x": 0, "y": 161}, {"h": 12, "i": "fmb8WXCZwWGFjhFVgetERL", "moved": false, "static": false, "w": 4, "x": 0, "y": 173}, {"h": 12, "i": "cZXdAf6ZC2MWHHecZmGNoK", "moved": false, "static": false, "w": 4, "x": 0, "y": 185}, {"h": 12, "i": "eAxxgzwqSqSHwwsC26LNxz", "moved": false, "static": false, "w": 4, "x": 0, "y": 197}]}, "title": "input variable", "version": "1744791546002739200", "weight": 10}, {"chartMap": {"1rptFqjbEkhWUc5hmin2hb": {"chartSettings": {}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "1rptFqjbEkhWUc5hmin2hb", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "City", "data": "City", "id": "4a22c526-6fa0-48b7-b998-0cc549b63a93", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.name"}, "label": "City.name"}], "type": "vertex"}, {"data": "input_json_for_city", "id": "fcad6486-fe5e-434a-aa2e-04d5011632eb", "paramGlobalInput": "input_json_for_city", "paramName": "id", "paramType": "STRING", "type": "PARAM"}], "staticData": "[\n    {\n        \"key\": \"value\"\n    }\n]", "title": "city_with_json_input_query", "type": "internal-graph", "version": "1744708796103706610"}, "mZ8i6jnGuCQZUZvNENBx6b": {"chartSettings": {}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "mZ8i6jnGuCQZUZvNENBx6b", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "Province", "data": "Province", "id": "7d5a66e6-4763-4393-a255-d4ed0b8e7366", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_Province_0.name"}, "label": "Province.name"}], "type": "vertex"}, {"data": "string_input_province", "id": "9a87d40b-2ea5-434b-80d1-d818cfedb4e6", "paramGlobalInput": "string_input_province", "paramName": "id", "paramType": "STRING", "type": "PARAM"}], "staticData": "[\n    {\n        \"key\": \"value\"\n    }\n]", "title": "province_with_input_string", "type": "internal-graph", "version": "1744708830695596754"}, "nBhm4pceyT35aJDYhTWKV5": {"chartSettings": {}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "nBhm4pceyT35aJDYhTWKV5", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [{"alias": "Country", "data": "Country", "id": "4a341ecc-c36d-4ecc-83b4-4d761e260b02", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_Country_0.name"}, "label": "Country.name"}], "type": "vertex"}, {"data": "string_dropdown_for_country", "id": "2c5f7ceb-154e-4e02-a5ec-05af94414deb", "paramGlobalInput": "string_dropdown_for_country", "paramName": "id", "paramType": "STRING", "type": "PARAM"}], "staticData": "[\n        {\n            \"res\": [\n                \n                {\n                    \"attributes\": {\n                        \"age\": 0,\n                        \"myDate\": \"1970-01-01 00:00:00\",\n                        \"name\": \"\"\n                    },\n                    \"v_id\": \"Seongnam-si1~!@#$%^&*()_+;][=-009]\",\n                    \"v_type\": \"City\"\n                },\n                {\n                    \"attributes\": {\n                        \"age\": 0,\n                        \"myDate\": \"1970-01-01 00:00:00\",\n                        \"name\": \"\"\n                    },\n                    \"v_id\": \"Dongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-siDongducheon-si\",\n                    \"v_type\": \"City\"\n                }\n            ]\n        }\n    ]", "title": "country via string dropdown", "type": "internal-graph", "version": "1744786417152997369"}, "tjRq5gq4vN2V5Q6brYWt48": {"chartSettings": {"inputStates": [{"dataType": "string", "id": "input_gswBb3YcrfQ8Dr4Piz7xHa", "name": "input_json_for_city", "settings": {"graphName": "MyMultiEdge", "labelKey": "v_id", "open": false, "options": [], "patternLimit": 300, "query": "", "queryType": "static_data", "searchPattern": [], "staticData": "[\n        {\n            \"res\": [\n                {\n                    \"attributes\": {\n                        \"age\": 0,\n                        \"myDate\": \"1970-01-01 00:00:00\",\n                        \"name\": \"\"\n                    },\n                    \"v_id\": \"ChinaGoyang-si\",\n                    \"v_type\": \"City\"\n                },\n                {\n                    \"attributes\": {\n                        \"age\": 0,\n                        \"myDate\": \"1970-01-01 00:00:00\",\n                        \"name\": \"\"\n                    },\n                    \"v_id\": \"Uijeongbu-si\",\n                    \"v_type\": \"City\"\n                },\n                {\n                    \"attributes\": {\n                        \"age\": 0,\n                        \"myDate\": \"1970-01-01 00:00:00\",\n                        \"name\": \"\"\n                    },\n                    \"v_id\": \"Seongnam-si\",\n                    \"v_type\": \"City\"\n                },\n                {\n                    \"attributes\": {\n                        \"age\": 0,\n                        \"myDate\": \"1970-01-01 00:00:00\",\n                        \"name\": \"\"\n                    },\n                    \"v_id\": \"Dongducheon-si\",\n                    \"v_type\": \"City\"\n                }\n            ]\n        }\n    ]", "tableHeaders": ["v_id", "v_type", "age", "myDate", "name"], "useQuery": true, "valueKey": "v_id"}, "widgetType": "Dropdown"}, {"dataType": "String", "id": "input_t55hdeGhAq6cTVvpMgQMeZ", "name": "string_input_province", "settings": {"options": [{"isCreatable": true, "label": "Gangwon-do", "value": "Gangwon-do"}, {"isCreatable": true, "label": "Busan", "value": "Busan"}, {"isCreatable": true, "label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}]}, "widgetType": "Dropdown"}]}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "tjRq5gq4vN2V5Q6brYWt48", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [], "staticData": "[\n    {\n        \"key\": \"value\"\n    }\n]", "title": "New Widget", "type": "Inputs", "version": "1744708773149463295"}, "vmaxRGr5fkLVEsnCAiy1KM": {"chartSettings": {"inputStates": [{"dataType": "string", "id": "input_tDk8SiL71BdcYHXaeH2LB4", "name": "string_dropdown_for_country", "settings": {"graphName": "MyMultiEdge", "labelKey": "Country.id", "open": false, "options": [], "patternLimit": 300, "query": "", "searchPattern": [{"alias": "Country", "data": "Country", "id": "54fbcfcd-c243-4a26-b845-edef0a7917af", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_Country_0.name"}, "label": "Country.name"}], "type": "vertex"}, {"data": "10", "id": "94dac079-36c2-472c-8831-d3d7bf8df8a0", "paramGlobalInput": "", "paramName": "vertex_limit", "paramOperator": "eq", "paramType": "INT", "type": "PARAM"}], "staticData": "[\n    {\n        \"key\": \"value\"\n    }\n]", "tableHeaders": ["Matched pattern", "Country"], "useQuery": true, "valueKey": "Country.id"}, "widgetType": "Dropdown"}], "invisibleInPreviewMode": true}, "graphName": "MyMultiEdge", "hideWidgetName": false, "id": "vmaxRGr5fkLVEsnCAiy1KM", "patternLimit": 5, "query": "", "queryType": "pattern", "refreshRate": 0, "searchPattern": [], "staticData": "[\n    {\n        \"key\": \"value\"\n    }\n]", "title": "New Widget", "type": "Inputs", "version": "1744786335419926855"}}, "globalParameters": {"input_json_for_city": {"id": "input_gswBb3YcrfQ8Dr4Piz7xHa", "name": "input_json_for_city", "type": "STRING", "value": "ChinaGoyang-si"}, "string_dropdown_for_country": {"id": "input_tDk8SiL71BdcYHXaeH2LB4", "name": "string_dropdown_for_country", "type": "STRING", "value": ""}, "string_input_province": {"id": "input_t55hdeGhAq6cTVvpMgQMeZ", "name": "string_input_province", "type": "STRING", "value": ""}}, "iconURL": "/insights/static/media/library.d3f7f207c6bb1d7be8e64045a19991b2.svg", "id": "wMCwcSRDeEPND4Cc88KjKb", "isDetail": true, "isNew": false, "layouts": {"md": [{"h": 12, "i": "1rptFqjbEkhWUc5hmin2hb", "moved": false, "static": false, "w": 4, "x": 0, "y": 9}, {"h": 12, "i": "mZ8i6jnGuCQZUZvNENBx6b", "moved": false, "static": false, "w": 4, "x": 4, "y": 9}, {"h": 12, "i": "nBhm4pceyT35aJDYhTWKV5", "moved": false, "static": false, "w": 4, "x": 0, "y": 33}, {"h": 9, "i": "tjRq5gq4vN2V5Q6brYWt48", "moved": false, "static": false, "w": 8, "x": 0, "y": 0}, {"h": 12, "i": "vmaxRGr5fkLVEsnCAiy1KM", "isResizable": false, "moved": false, "static": false, "w": 12, "x": 0, "y": 21}], "xs": [{"h": 12, "i": "tjRq5gq4vN2V5Q6brYWt48", "moved": false, "static": false, "w": 4, "x": 0, "y": 0}, {"h": 12, "i": "mZ8i6jnGuCQZUZvNENBx6b", "moved": false, "static": false, "w": 4, "x": 0, "y": 12}, {"h": 12, "i": "1rptFqjbEkhWUc5hmin2hb", "moved": false, "static": false, "w": 4, "x": 0, "y": 24}, {"h": 12, "i": "vmaxRGr5fkLVEsnCAiy1KM", "isResizable": false, "moved": false, "static": false, "w": 4, "x": 0, "y": 36}, {"h": 12, "i": "nBhm4pceyT35aJDYhTWKV5", "moved": false, "static": false, "w": 4, "x": 0, "y": 48}]}, "title": "json input", "version": "1744786483762655995", "weight": 40}], "title": "input_variables_regression_test_set", "userRoleForApp": "owner", "version": "1744709329719267241"}