from selenium.webdriver.common.by import By

class InsightLocators:
    # The use of img and part of the name to locate is to
    input_username = "//label[text()='Username']/../../div/div/div/input"
    input_password = "//label[text()='Password']/../../div/div/div/input"
    addapp_button = 'div[data-tour="create-application"]>button'
    submit_button = "//button[@type='submit']"
    click_submit_button_progressbar = "//button[@type='submit']/div[2]/span"
    close_app_panel_button = 'button[aria-label="Close"]'
    sso_login_button = '//button[text()="Login With SSO"]'
    sso_login_send_anyway = 'button#proceed-button'
    guide_img = "//img[@alt='Application Building Guide']"
    create_newpage = "//button[@data-tour='create-page']"
    create_widget = "//button[@data-tour='create-widget']"
    confirm_mess = "//div[@data-baseweb='toaster']/div[@data-baseweb='toast']/div"
    homepage = "//a[@href='/insights/apps']"

    #application page
    search_app = "//input[@placeholder='Search applications']"
    app_name = "//div[@data-baseweb='form-control-container'][1]/div/div/input"
    app_icon = "//div[@data-baseweb='form-control-container'][2]"
    default_graph = "//div[@data-baseweb='form-control-container'][3]"
    config_settings = "//span[text()='Settings']"
    config_duplicate = "//span[text()='Duplicate']"
    config_duplicate_compatible_widget = "//span[contains(text(),'Duplicate')]"
    config_duplicate_widget = "//span[text()='Duplicate to']"
    config_duplicate_application = "//span[text()='Duplicate']"
    config_delete = "//span[text()='Delete']"
    config_share = "//span[text()='Share']"
    app_number = "//div[@data-baseweb='flex-grid']/div"
    ok_button = "//button[text()='OK']"
    cancel_button = "//button[text()='Cancel']"
    guide_panel = "//div[@class='reactour__popover']"
    close_button = "//button[@aria-label='Close Tour']"
    # other_application_button = "//button[@id='tabs-bui1-tab-other']" # css has changed
    # first_application_area = "//div[@aria-labelledby='tabs-bui1-tab-other']/div/div[@data-baseweb='flex-grid-item'][1]/div/a"  # css has changed
    # first_application_area = 'a[target="_self"]' # can't find for sometimes
    first_application_area = '//a[contains(@href,"app")]'
    other_application_flag = "//span[text()='Powered by Tigergraph Insights']"
    select_default_graph = "//div[@data-baseweb='select']"
    application_grid = 'div[data-baseweb="flex-grid-item"]'
    application_config_svg = 'button[aria-haspopup="true"]'
    application_icon = 'img[alt="app icon"]'
    generate_new_token_button = "//button[contains(text(),'token')]"
    generate_new_token_input_username_button = 'input[name="username"]'
    generate_new_token_input_psd_button = 'input[name="password"]'
    generate_new_token_submit_button = 'button[type="submit"]'
    generate_new_token_delete_button = '//button[text()="Delete"]'

    #widget page
    home_button = 'a[href="/insights/apps"]'
    create_page = "//span[text()='New page']"
    import_export_switch = "//div[@class='bm']"
    import_button = "//span[text()='Import']"
    export_button = "//span[text()='Export']"
    # export_button = "//span[text()='Export']"
    # >= 3.9.0
    more_button = "//h1[@data-baseweb='heading']/../button[1]"
    # 3.8.0
    save_button = "//h1[@data-baseweb='heading']/../button[1]"
    preview_button = "//h1[@data-baseweb='heading']/../button[2]"
    config_edit = "//span[text()='Edit']"
    # old version, < V3.9.3
    # widget_share_copy_button = "//button[text()='Copy']"
    widget_share_copy_button = 'div[data-baseweb="input"] > div > button'
    widget_share_URL = 'div[data-baseweb="base-input"] > input'
    close_share_widget = "//button[@aria-label='Close']"
    # share window
    add_user_input = 'input[aria-haspopup="listbox"]'
    no_result = '//li[text()="No results"]'
    find_user = 'ul[aria-label="Menu"] > li'
    set_role = "svg#Icons"  # 0: people access,  last-1: general access
    circle_progressbar = 'div>i'
    cluster_user_permission = "//div[text()='Cluster users']"
    cluster_user_restricted_permission = "//div[text()='Restricted']"
    remove_access = "//div[text()='Remove access']"
    edit_user_button = 'div[value="editor"]'
    delete_role = "//div[text()='Remove access']"
    user_editor_role = "//div[text()='Editor']"
    user_viewer_role = "//div[text()='Viewer']"
    add_role_button = "//button[text()='Add']"
    save_role_button = "//button[text()='Save']"
    done_button = "//button[text()='Done']"
    close_window_button = 'button[aria-label="Close"]'
    shared_app_with_me_button = 'button[data-baseweb="tab"]'
    contain_span_text = "//span[contains(.,'{0}')]"
    common_button_text = "//button[contains(.,'{0}')]"
    assert_shared_link_elements = 'button[data-baseweb="button"]'
    widget_loading_div = 'div[data-cy="loading"]'

    #widget type
    type_widget = "//button[@aria-label='{0}']"
    graph_widget = "//button[@aria-label='Graph']"
    barchart_widget = "//button[@aria-label='Bar chart']"
    linechart_widget = "//button[@aria-label='Line chart']"
    piechart_widget = "//button[@aria-label='Pie chart']"
    sankeychart_widget = "//button[@aria-label='Sankey chart']"
    table_widget = "//button[@aria-label='Table']"
    singlevalue_widget = "//button[@aria-label='Single value']"
    inputs_widget = "//button[@aria-label='Inputs']"
    map_widget = "//button[@aria-label='Map']"
    apply_widget = "//button[text()='Apply']"
    widget_name_input = "//div[text()='Widget Name']/../../../div[2]/div/input"
    add_filter = "//div[text()='Add filter']"
    vertex_limit = "//div[text()='vertex_limit']"
    int_input = "//div[text()='INT']/../div/input"
    pattern_setting_button = "//div[@data-tour='step-3']/button[1]"
    pattern_run_button = "//div[@data-tour='step-3']/button[2]"
    select_graph = "//div[@data-tour='step-1']/div/div"
    pattern_input = "//div[@data-tour='step-2']/div/input[@role='combobox']"
    proceed_button= "//button[text()='Proceed']"
    disable_path_finding_labels = 'div[aria-label="disable_path_finding"]>div>label>div'
    path_finding_function = '//button[text()="PathFinding"]'
    page_list = "li>a"
    page_list_button = "li>a>button"

    # conditional styling
    add_conditional_styling_button = "//div[text()='Conditional Styling']/../../button"
    get_city_name = "td>div"
    city_condition_input = "//div[@aria-selected='true' and contains(@value, 'City')]"
    city_condition_value_input = 'input[inputmode="text"]'
    city_condition_OK_button = "//button[contains(text(),'OK')]"
    city_condition_no_result = "//li[contains(text(),'No results')]"

    #pattern view
    search_keyword = "//div[text()='Search keywords']"
    show_schema = "//ul[@aria-label='Menu']/li[1]"
    select_query = "//div[contains(text(),'{0}')]"
    select_query_with_list_or_map = "//span[contains(text(),'{0}')]"
    select_query_int_input = 'input[placeholder="INT"]'
    select_query_string_input = 'input[placeholder="STRING"]'
    select_query_double_input = 'input[placeholder="DOUBLE"]'

    #graph operation under graph widget
    tabular_data_button = "//div[@data-testid='left-ui']/button[1]"
    open_algorithm_panel = "//div[@data-testid='left-ui']/button[2]"
    save_as_screenshot = "//div[@data-testid='left-ui']/button[3]"
    filter_button = "//div[@data-testid='filter']/button[1]"

    open_algo_dropdown = "//div[text()='Algorithm']/../../../div[2]/div/div[2]"
    pagerank_query = "//div[text()='Page Rank']"
    louvain_query = "//div[text()='Louvain']"
    run_algo_button = "//button[text()='Run algorithm']"

    #graph operation under application
    filter_button_prefix = "/../..//div[@data-testid='filter']/button[1]"
    close_filter = "//button[text()='Clear All']/../div[2]/button"

    # table widget
    add_conditional_styling_button = "//div[text()='Conditional Styling']/../../button"
    conditional_styling_condition = 'input[aria-label="Selected =. "]'
    conditional_styling_input = 'input[type="text"]'
    added_conditions = '//span[contains(text(),"City.city_id")]'
    delete_conditions = '//span[contains(text(),"City.city_id")]/../../../div/button'
    common_span_button = '//span[normalize-space(text())="{0}"]'
    common_button = '//button[normalize-space(text())="{0}"]'
    common_div_button = '//div[normalize-space(text())="{0}"]'
    common_a_button = '//a[normalize-space(text())="{0}"]'

    #input widget
    add_input_param_button = 'button[aria-haspopup="true"]'
    input_variablename = "input[aria-label*='Selected variable']"
    input_variable_type = 'div[value="Input"]'
    input_string_variable = 'input[placeholder="STRING"]'
    input_bool_variable = 'input[aria-haspopup="listbox"]'
    assert_variable_value = 'div[aria-haspopup="true"]>button>svg'
    assert_input_variable_value = 'input[value="{0}"]'