import re

import pytest
from locators.tools.admin_portal_locators import GAPHomeLocators, GAPLogLocators, GAPComponentsLocators, LocalQueryPrivilege,WLMLocator, GAPUsersLocator,GAPProfileLocator
from selenium.webdriver import <PERSON><PERSON>hains
from seleniumbase.common import decorators
from seleniumbase.common.exceptions import TextNotVisibleException

import os, time, logging
from locators.tools.graph_studio_locators import GSTHomeLocators
from pages.tools.gshell.gshell_page import GshellPage
from base.tools_basecase import ToolsBaseCase
from utils.data_util.data_resolver import read_test_data,GPT_update_test_data
from locators.tools.admin_portal_locators import GAPOutputLocators,UpgradeToolsLocators
from utils.data_util.login import LoginUtils
from pages.cloud.clusters.access_cluster_tools_page import ClusterToolsPage
from locators.cloud.cloud_locators import CloudClusterAccessToolsLocators
from locators.tools.admin_portal_locators import UpgradeToolsLocators
from locators.tools.admin_portal_locators import RestPPLocators

from locators.tools.admin_portal_locators import <PERSON><PERSON><PERSON>Locator
from locators.tools.admin_portal_locators import GAPSecretLocator
from selenium.webdriver.remote.file_detector import UselessFileDetector
from api.src.operation.gus import GUS
from api.src.operation.login import Login
import paramiko
from selenium.webdriver.common.by import By

LOGGER = logging.getLogger(__name__)
short_click_timeout = 10
click_timeout = 60
wait_click_timeout = 30
wait_render_timeout = 120
assertTextTimeout = 60
uploading_timeout = 600

class GAPPage():

    # OKTA
    SSOURL = 'https://dev-********.okta.com/app/dev-********_tigergraphqe02_1/exkqeyfvzqPHcJVKp5d6/sso/saml',
    IDP = 'http://www.okta.com/exkqeyfvzqPHcJVKp5d6',
    CONTEXT = 'urn:oasis:names:tc:SAML:2.0:ac:classes:Password',
    IdentityCertificate = '-----BEGIN CERTIFICATE----- \n\
            MIIDqDCCApCgAwIBAgIGAXgj2N3YMA0GCSqGSIb3DQEBCwUAMIGUMQswCQYDVQQGEwJVUzETMBEG  \n\
            A1UECAwKQ2FsaWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzENMAsGA1UECgwET2t0YTEU  \n\
            MBIGA1UECwwLU1NPUHJvdmlkZXIxFTATBgNVBAMMDGRldi03MTA5NzY4NTEcMBoGCSqGSIb3DQEJ  \n\
            ARYNaW5mb0Bva3RhLmNvbTAeFw0yMTAzMTIwMDMwNThaFw0zMTAzMTIwMDMxNThaMIGUMQswCQYD  \n\
            VQQGEwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzENMAsG  \n\
            A1UECgwET2t0YTEUMBIGA1UECwwLU1NPUHJvdmlkZXIxFTATBgNVBAMMDGRldi03MTA5NzY4NTEc  \n\
            MBoGCSqGSIb3DQEJARYNaW5mb0Bva3RhLmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoC  \n\
            ggEBAIJPryO35ZigDga40a1iDMViXIwasAClU0NhkSzo/ubpu+/nzr7N+fNq5a68e5ZTYmdP/Rtj  \n\
            gYnFdPj26TAsPjoBEDBCb45SIzJqVitakZoPhvOYMiLhjAeRxAGB/UPwJD9hrKQnyQRjAjdWD9/D  \n\
            85Gc2ZMPdMp4q44SBpIg6zuFunMtaqsuZq5t9bfntYWjRB4UqmG+hLMoU09HGFSmw59yl7XOz9Tx  \n\
            PdXTYIYST7nmhgK166g7KiWYLKb9o4s8W9qsFlm/PfRSK66wrUp47ozny/O5WWaf8/cmO61YkZtz  \n\
            e6J99RWqekJkYCwVjXBd9+znAWN/FKmZcJQ0LWyclLMCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEA  \n\
            XwW/8qU/PWYAuEAczVrVMcFLL64RN3Xp66vPp4aDB8y5sPA2cD6Ao8ChlhsvjO1GpDLVM2MZZ1zl  \n\
            QLtEWEbG7k/JULkCPukXgFf1v4SNRnGuSAX4oU1DCXLRRH07KbJMWSWil+XcLKyadNrormtlcTZT  \n\
            JHz4uvThQ6Q7Ml0MsStlHpfudMc9eQCjP6ogOViDKg61A/cXnM10G7B2UEjCPgiyTkiuw1jnu8Lz  \n\
            hIgy+BkbvUMxnCUvGDIwtMrr6HnoY9PmYFQgUqeh56+9kl4x9/wrxKEhuoN1hqAVLQgm4cukBvsp  \n\
            wHhz6uK2clX9WRDT9GsowAdwp1nUFl1EOsPe+A==  \n\
            -----END CERTIFICATE-----'
    # AZURE
    AZURE_SSOURL = "https://login.microsoftonline.com/718f143b-b1ef-468b-a3a8-c95651fdd511/saml2",
    AZURE_IDP = "https://sts.windows.net/718f143b-b1ef-468b-a3a8-c95651fdd511/",
    AZURE_CONTEXT = " ",
    AZURE_IdentityCertificate = "-----BEGIN CERTIFICATE-----\n\
          MIIC8DCCAdigAwIBAgIQfBPyKp/ZTK1CCLC3SrjtkjANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQD\n\
          ****************************************************************************\n\
          NTFaFw0yNTA4MjMwNjQ3NTFaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQg\n\
          U1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA84Xrfk19BHSK\n\
          qpKELnTiuLDILSpR+cbzG2kUmHBuh/kRQixnhaj582tamFG3xANYUHMOto61ZNIItfcdxcjPQjmz\n\
          WQu/SYqCls5vmABsMCh68dtInEA84xLIGD4bPE+CXEFwOiKtpPO3fKM1m03u49YMnPPMWUGNK1Kn\n\
          M7z+5sSZqvzM3meyqCGQ6wEWF0NyW6GUvI02zLqXM2TSRozHGHthrXXXO7jkCtIWYbYKrS8UsVa0\n\
          4cJ0R2RejwI3NM9ElgALI4cMHXTjzmR0B1w/F1gkv1+/zSHG3NOOrvCh2JXOxKYM+50eZeHm4LUB\n\
          HrWYyWLACfOcFC1fta06GpoPSQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQCNLbK+QTkYXtgfLw4N\n\
          7dnhm5pNCsftmXi0TAhIVuc1SMhgPgvTLdRRnAePDw+wetgFOHcqymCdodq6GdHjDRk2qq6zn7ir\n\
          Se64gHW8+rK7InnAVs9Jy9kSaS4OqhcjCfKXIWpqSjEDpusoxueQmTzHk28xwR2VVwcwteoevP5o\n\
          X9DTX84NvD8Z1Ih9Sxih5Ap18K0e9leopbKIyLeBKiizhGPvpJ9Vv9YAvKPPTvn0LdI6cJv/WJWs\n\
          PI6h6svg9CGw3NtdsXxnxmaL2hPaiunHu17SRq9vmJJeU30Pl+STvKHuVIMNefifx/rSntWaMcEm\n\
          gXDfPI7+O6AiesS3bw31\n\
          -----END CERTIFICATE-----"

    def __init__(self):
        self.test_env = read_test_data(file="tools_test_data.json").get("test_env")
        LOGGER.info("GAPPage init test_env: " + self.test_env)

    def GapLogin(self, sb, login_way="undefined", need_check_GAP=True):
        loginUtil = LoginUtils(sb)
        loginUtil.login(ToolsBaseCase.AdminPortal_URL, login_way)
        if need_check_GAP:
            self.check_open_GAP_successfully(sb)

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=10)
    def check_open_GAP_successfully(self, sb):
        try:
            # wait refresh button is visible on cloud and on-prem
            for i in range(wait_click_timeout):
                sb.sleep(1)
                if sb.is_element_visible(GAPHomeLocators.refresh_button):
                    sb.attach_allure_screenshot("refresh button visible screenshot")
                    LOGGER.info("refresh button visible, break")
                    break
                else:
                    LOGGER.info("refresh word still visible, continue to wait refresh button in dashboard")

            if not sb.is_element_visible(GAPHomeLocators.refresh_button):
                sb.attach_allure_screenshot("refresh button still not visible screenshot")
                raise Exception("GAP page not finish render, refresh button still not visible")
        except Exception as e:
            sb.attach_allure_screenshot("wait refresh button failed and will refresh screenshot")
            LOGGER.info("wait refresh button failed and will refresh")
            sb.refresh()
            raise Exception("check_open_GAP_successfully failed")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def intoMonitor(self, sb):
        try:
            sb.wait_for_element_clickable(GAPHomeLocators.monitor, timeout=click_timeout)
            sb.click(GAPHomeLocators.monitor, timeout=click_timeout)
            sb.wait_for_ready_state_complete()
            return True
        except Exception as e:
            # don't refresh, will cause logout GAP
            sb.attach_allure_screenshot("open menu failed screenshot")
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def intoLogManagement(self, sb):
        if self.intoMonitor(sb):
            sb.click(GAPHomeLocators.logmanagement)
        else:
            self.intoMonitor(sb)
            raise Exception("into monitor failed, retry")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def intoWLM(self, sb):
        if self.intoMonitor(sb):
            sb.click(GAPHomeLocators.wlm)
            sb.wait_for_ready_state_complete()
        else:
            raise "into Monitor fail..."

    @decorators.retry_on_exception(tries=1, delay=1, backoff=2, max_delay=20)
    def enlarge_items_per_page(self, sb):
        try:
            sb.wait_for_element_clickable(GAPSecretLocator.items_per_page, timeout=wait_render_timeout)
            sb.find_elements(GAPSecretLocator.items_per_page)[0].click()
            self.wait_and_click(sb, GAPSecretLocator.items_per_page_20)
        except Exception as e:
            LOGGER.info("enlarge_items_per_page failed: " + str(e))
            sb.attach_allure_screenshot("enlarge_items_per_page failed screenshot")


    def my_execute_js(self, sb, js):
        try:
            LOGGER.info("js: " + js)
            sb.execute_script(js)
        except Exception as e:
            LOGGER.info("JS exception: " + str(e))
            sb.attach_allure_screenshot("JS exception screenshot")

    @decorators.retry_on_exception(tries=1, delay=1, backoff=2, max_delay=10)
    def intoUserManegement(self, sb):
        try:
            sb.wait_for_element_clickable(GAPHomeLocators.dashboard, timeout=wait_click_timeout)
            sb.click(GAPHomeLocators.dashboard, timeout=click_timeout)
            sb.wait_for_element_clickable(GAPHomeLocators.management, timeout=wait_click_timeout)
            if not sb.is_element_visible(GAPHomeLocators.users):
                LOGGER.info("GAPHomeLocators.users is not visible, click")
                sb.click(GAPHomeLocators.management, timeout=click_timeout)
            sb.click(GAPHomeLocators.users)
        except Exception as e:
            sb.attach_allure_screenshot("open management menu failed screenshot")
            if not sb.is_element_visible(GAPHomeLocators.management):
                # sb.refresh() # didn't refresh since can't render management successfully
                sb.attach_allure_screenshot("skip refresh management menu temply screenshot")
            raise Exception(e)

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=10)
    def change_password_in_profile(self, sb):
        username = "u1"
        oldpw = "userpw"
        newpw = "changepw"
        self.createUser(sb, username, oldpw)

        # login by new user
        sb.sleep(10)
        self.my_execute_js(sb, GSTHomeLocators.tools_header_icon_JS)
        sb.sleep(2)
        self.my_execute_js(sb, GSTHomeLocators.logout_icon_JS)
        sb.sleep(2)
        sb.wait_for_ready_state_complete()

        sb.wait_for_element_clickable(GAPProfileLocator.loginname, timeout=60)
        sb.type(GAPProfileLocator.loginname, username)
        sb.type(GAPProfileLocator.loginpw, oldpw)
        sb.click(GAPProfileLocator.login)
        sb.wait_for_ready_state_complete()

        # change password
        self.intoUserManegement(sb)
        loginUtil = LoginUtils(sb)
        cloud_env_flag = loginUtil.is_oncloud()
        if cloud_env_flag:
            version_tmp, is_three_version = self.get_cloud_tg_version(sb)
        else:
            version_tmp, is_three_version = self.get_on_prem_tg_version(sb)
        LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))

        loginUtil.close_security_float_windows()
        sb.attach_allure_screenshot("u1 login result screenshot")
        if is_three_version:
            self.change_password_for_v3(sb, newpw)
        else:
            self.changeMypw(sb, oldpw, newpw)

        # relogin with new password
        self.my_execute_js(sb, GSTHomeLocators.tools_header_icon_JS)
        sb.sleep(2)
        self.my_execute_js(sb, GSTHomeLocators.logout_icon_JS)
        sb.sleep(2)
        sb.wait_for_ready_state_complete()

        sb.type(GAPProfileLocator.loginname, username)
        sb.type(GAPProfileLocator.loginpw, newpw)
        sb.click(GAPProfileLocator.login)
        sb.wait_for_ready_state_complete()

        # check login success with new password
        sb.assert_element_visible(GAPHomeLocators.management, timeout=30)
        sb.attach_allure_screenshot("change password and re-login successfully  screenshot")


    """
        index: 0(Overview), 1(Historic Cluster Monitoring)
    """
    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def into_dashboard_menu(self, sb, index=0):
        try:
            sb.wait_for_element_clickable(GAPHomeLocators.management, timeout=wait_click_timeout)
            sb.click(GAPHomeLocators.dashboard, timeout=click_timeout)
            sb.find_elements(GAPHomeLocators.tabs)[index].click()
            sb.attach_allure_screenshot("open dashboard tabs screenshot")
        except Exception as e:
            sb.attach_allure_screenshot("open menu failed screenshot")
            if not sb.is_element_visible(GAPHomeLocators.dashboard):
                sb.refresh()
                sb.attach_allure_screenshot("refresh menu screenshot")
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def into_security_menu(self, sb, sub_menu="LDAP"):
        try:
            sb.wait_for_element_clickable(GAPHomeLocators.management, timeout=wait_click_timeout)
            if not sb.is_element_visible(GAPHomeLocators.components):
                sb.click(GAPHomeLocators.management, timeout=click_timeout)
            if not sb.is_element_visible(GAPHomeLocators.LDAP):
                sb.click(GAPHomeLocators.security, timeout=click_timeout)

            if sub_menu == "LDAP":
                sb.click(GAPHomeLocators.LDAP, timeout=click_timeout)
            elif sub_menu == "SSO":
                sb.click(GAPHomeLocators.SSO, timeout=click_timeout)
            self.wait_progress_bar(sb, 15, 60)
            sb.attach_allure_screenshot("open " + sub_menu +" screenshot")
        except Exception as e:
            sb.attach_allure_screenshot("open menu failed screenshot")
            if not sb.is_element_visible(GAPHomeLocators.management):
                sb.refresh()
                sb.attach_allure_screenshot("refresh menu screenshot")
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def into_components_menu(self, sb, sub_menu="Nginx"):
        try:
            sb.wait_for_element_clickable(GAPHomeLocators.management, timeout=click_timeout)
            if not sb.is_element_visible(GAPHomeLocators.components):
                sb.click(GAPHomeLocators.management, timeout=click_timeout)
            if not sb.is_element_visible(GAPHomeLocators.Nginx):
                sb.click(GAPHomeLocators.components, timeout=click_timeout)

            if sub_menu == "Nginx":
                sb.click(GAPHomeLocators.Nginx, timeout=click_timeout)
            elif sub_menu == "RESTPP":
                sb.click(GAPHomeLocators.RESTPP, timeout=click_timeout)
            elif sub_menu == "GSQL":
                sb.click(GAPHomeLocators.GSQL, timeout=click_timeout)
            elif sub_menu == "GPE":
                sb.click(GAPHomeLocators.GPE, timeout=click_timeout)
            elif sub_menu == "GUI":
                sb.click(GAPHomeLocators.GUI, timeout=click_timeout)
            elif sub_menu == "System":
                sb.click(GAPHomeLocators.system, timeout=click_timeout)
            elif sub_menu == "Kafka":
                sb.click(GAPHomeLocators.Kafka, timeout=click_timeout)
            sb.wait_for_ready_state_complete()
            self.wait_progress_bar(sb,30,60)
            sb.attach_allure_screenshot("open " + sub_menu +" screenshot")
        except Exception as e:
            sb.attach_allure_screenshot("open menu failed screenshot")
            if not sb.is_element_visible(GAPHomeLocators.management):
                sb.refresh()
                sb.attach_allure_screenshot("refresh menu screenshot")
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def intoLicenseManegement(self, sb):
        try:
            sb.wait_for_element_clickable(GAPHomeLocators.management, timeout=click_timeout)
            if not sb.is_element_visible(GAPHomeLocators.license):
                sb.click(GAPHomeLocators.management, timeout=click_timeout)
            sb.click(GAPHomeLocators.license)
        except Exception as e:
            sb.attach_allure_screenshot("open menu failed screenshot")
            if not sb.is_element_visible(GAPHomeLocators.management):
                sb.refresh()
                sb.attach_allure_screenshot("refresh menu screenshot")
            raise Exception(e)

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=5)
    def get_cloud_tg_version(self, sb):
        version_text = sb.get_text(GSTHomeLocators.cloud_version_new_header, timeout=wait_click_timeout)
        LOGGER.info(version_text)
        if "version" not in version_text:
            LOGGER.info("Version not in version_text Exception")
        str_tmp = version_text.strip('"')
        LOGGER.info(str_tmp)
        match = re.search(r"\d+\.\d+\.\d+", str_tmp)  # find version number
        LOGGER.info(match)
        is_three_version = True
        if match:
            version_nums = match.group().split(".")
            LOGGER.info(version_nums)
            version = version_nums[0] + version_nums[1] + version_nums[2]
            if version_nums[0] != "3":
                is_three_version = False
                LOGGER.info("is not V3 serious, " + version_nums[0] + ", is_three_version: " + str(is_three_version))
            LOGGER.info("splice version: " + version)
            return version, is_three_version

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=5)
    def get_on_prem_tg_version(self, sb):
        version_text = sb.get_text(GSTHomeLocators.on_prem_version_new_header, timeout=wait_click_timeout)
        LOGGER.info(version_text)
        # in tg_3.11.1, it shows v3.11.1
        if "Version" not in version_text:
            LOGGER.info("Version not in version_text Exception")
        str_tmp = version_text.strip('"')
        LOGGER.info(str_tmp)
        match = re.search(r"\d+\.\d+\.\d+", str_tmp)  # find version number
        LOGGER.info(match)
        is_three_version = True
        if match:
            version_nums = match.group().split(".")
            LOGGER.info(version_nums)
            version = version_nums[0] + version_nums[1] + version_nums[2]
            if version_nums[0] != "3":
                is_three_version = False
                LOGGER.info("is not V3 serious, " + version_nums[0] + ", is_three_version: " + str(is_three_version))
            LOGGER.info("splice version: " + version)
            return version, is_three_version


    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def intoCDCManegement(self, sb, disable=False):
        try:
            support_CDC = True
            loginUtil = LoginUtils(sb)
            cloud_env_flag = loginUtil.is_oncloud()
            if cloud_env_flag:
                version_tmp, is_three_version = self.get_cloud_tg_version(sb)
            else:
                version_tmp, is_three_version = self.get_on_prem_tg_version(sb)
            LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))
            if version_tmp and version_tmp.strip():
                if is_three_version and int(version_tmp) < 3100:
                    support_CDC = False
                    LOGGER.info("Current version < 3.10.0, not support CDC")
                else:
                    LOGGER.info("Current version >= 3.10.0, support CDC")
            if support_CDC:
                sb.wait_for_element_clickable(GAPHomeLocators.management, timeout=click_timeout)
                sb.click(GAPHomeLocators.management, timeout=click_timeout)
                sb.click(GAPHomeLocators.components)
                sb.click(GAPHomeLocators.system)
                self.CDCManegement(sb, disable=disable)
            else:
                LOGGER.info("Current version < 3.10.0, not support CDC")
        except Exception as e:
            sb.attach_allure_screenshot("open menu failed screenshot")
            if not sb.is_element_visible(GAPHomeLocators.management):
                sb.refresh()
                sb.attach_allure_screenshot("refresh menu screenshot")
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def CDCManegement(self, sb, disable=False):
        sb.wait_for_element_visible(GAPCDCLocator.enablebtn, timeout=wait_render_timeout)
        el = sb.find_elements(GAPCDCLocator.enableinput)[0]
        LOGGER.info("Enable CDC switch status: " + str(el.get_attribute("aria-checked")))
        if disable:
            if el.get_attribute("aria-checked") == "true":
                LOGGER.info("disable CDC case, The current status is enable CDC, disable it")
                sb.click(GAPCDCLocator.enablebtn)
                sb.click(GAPCDCLocator.apply)
                sb.click(GAPCDCLocator.ok)
                self.wait_progress_bar(sb=sb)
                sb.assert_text("Successfully applied configurations", timeout=assertTextTimeout)
                sb.click(GAPCDCLocator.ok)
                self.wait_progress_bar(sb=sb)
                sb.assert_text("Successfully restarted the services", timeout=assertTextTimeout)
                sb.attach_allure_screenshot("Disable CDC config successfully screenshot")
                sb.wait(20)  # waiting for page ready
            else:
                LOGGER.info("The current status is disable CDC, return")
                sb.attach_allure_screenshot("No operation to disable CDC config successfully screenshot")

        else:
            if el.get_attribute("aria-checked") == "true":
                LOGGER.info("enable CDC case, the current status is enable CDC, should close it first")
                sb.click(GAPCDCLocator.enablebtn)
                sb.click(GAPCDCLocator.apply)
                sb.click(GAPCDCLocator.ok)
                self.wait_progress_bar(sb=sb)
                sb.assert_text("Successfully applied configurations", timeout=assertTextTimeout)
                sb.click(GAPCDCLocator.ok)
                self.wait_progress_bar(sb=sb)
                sb.assert_text("Successfully restarted the services", timeout=assertTextTimeout)
                sb.attach_allure_screenshot("Init CDC config successfully screenshot")
                sb.wait(20)  # waiting for page ready
                sb.refresh_page() # todo after refresh, the page will logout under http
            sb.wait_for_element_clickable(GAPCDCLocator.enablebtn, timeout=click_timeout)
            sb.click(GAPCDCLocator.enablebtn, timeout=click_timeout)
            cdc_path = os.path.join(os.getcwd(), "data", "gapdata", "ProducerConfig")
            sb.choose_file(GAPCDCLocator.second_input_file, cdc_path)
            sb.attach_allure_screenshot("Upload CDC file screenshot")
            sb.click(GAPCDCLocator.apply)
            sb.click(GAPCDCLocator.ok)
            self.wait_progress_bar(sb=sb)
            sb.assert_text("Successfully applied configurations", timeout=assertTextTimeout)
            sb.click(GAPCDCLocator.ok)
            self.wait_progress_bar(sb=sb)
            sb.assert_text("Successfully restarted the services", timeout=assertTextTimeout)
            sb.attach_allure_screenshot("CDC enable result screenshot")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def intoGsqlOutput(self, sb):
        try:
            sb.wait_for_element_clickable(GAPHomeLocators.others, timeout=wait_render_timeout)
            if not sb.is_element_visible(GAPHomeLocators.gsqloutput):
                sb.click(GAPHomeLocators.others)
            sb.click(GAPHomeLocators.gsqloutput)
        except Exception as e:
            sb.attach_allure_screenshot("open menu failed screenshot")
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def intoGsqlConfig(self, sb):
        try:
            sb.wait_for_element_clickable(GAPHomeLocators.management, timeout=wait_render_timeout)
            if not sb.is_element_visible(GAPHomeLocators.components):
                sb.click(GAPHomeLocators.management, timeout=click_timeout)
            if not sb.is_element_visible(GAPHomeLocators.gsqlconfig):
                sb.click(GAPHomeLocators.components)
            sb.click(GAPHomeLocators.gsqlconfig)
            self.wait_progress_bar(sb, 30, 60)
        except Exception as e:
            sb.attach_allure_screenshot("open menu failed screenshot")
            if not sb.is_element_visible(GAPHomeLocators.management):
                sb.refresh()
                sb.attach_allure_screenshot("refresh menu screenshot")
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def intoUpgradeTGSuite(self, sb):
        try:
            sb.wait_for_element_clickable(GAPHomeLocators.management, timeout=wait_render_timeout)
            if not sb.is_element_visible(GAPHomeLocators.components):
                sb.click(GAPHomeLocators.management, timeout=click_timeout)
            sb.click(GAPHomeLocators.upgradeTGsuite)
            sb.wait_for_element_present(UpgradeToolsLocators.upgrade_tools_span, timeout=click_timeout)
            sb.attach_allure_screenshot("AccessUpgradePage")
            return True
        except Exception as e:
            LOGGER.info("The current user doesn't have access to Upgrade TigerGraph Suites Page")
            sb.attach_allure_screenshot("NoAccess")
            if not sb.is_element_visible(GAPHomeLocators.management):
                sb.refresh()
                sb.attach_allure_screenshot("refresh menu screenshot")
            return False

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def setGsqlConfig(self, sb, size):
        sb.type(GAPComponentsLocators.gsqlresponsesize, size)
        sb.click(GAPComponentsLocators.apply)
        sb.click(GAPComponentsLocators.confirm, timeout=18)
        sb.wait(3)
        sb.click(GAPComponentsLocators.confirm, timeout=18)
        sb.wait(8)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def selectNode(self, sb, node):
        sb.wait(8)
        sb.click(GAPLogLocators.allnodecheckbox, timeout=click_timeout)
        sb.wait(8)
        el = sb.find_element('//*[@id="mat-checkbox-22-input"]')
        if "true" != el.get_attribute("checked"):
            if node == 1:
                sb.click(GAPLogLocators.m1node)
            elif node == 2:
                pass

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def selectComponent(self, sb, component):
        sb.wait_for_element_clickable(GAPLogLocators.search, timeout=wait_render_timeout)
        sb.wait_for_element_clickable(GAPLogLocators.allcomponent, timeout=wait_render_timeout)
        sb.wait(3)
        sb.click(GAPLogLocators.allcomponent, timeout=click_timeout)
        sb.wait(3)
        sb.click("//span[contains(.,'{}')]".format(component))

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def searchLogWithPattern(self, sb, text):
        sb.type(GAPLogLocators.logpattern, text)
        sb.click(GAPLogLocators.search)
        sb.wait_for_element_clickable(GAPLogLocators.allcomponent, timeout=wait_render_timeout)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def findText(self, sb, text):
        try:
            el = sb.find_element("//div[contains(.,'{}')]".format(text))
        except Exception:
            ToolsBaseCase.attach_allure_screenshot("findtexterror")
            sb.error("Text: {} not found".format(text))
            sb.fail()
            return False
        return el

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def displayAdminLog(self, sb):
        sb.click(GAPLogLocators.m1folder, timeout=click_timeout)
        sb.click(GAPLogLocators.admincomponent)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def drag(self, sb, element, x, y):
        ActionChains(sb.driver).drag_and_drop_by_offset(element, x, y).perform()

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def setChromeDownloadPath(self, sb, path="/tmp"):
        """
        set chrome download path, disable downloading popovers
        """
        path = path.rstrip(os.sep)
        Browser = os.getenv("Browser", "")
        # Browser = "edge"
        LOGGER.info("Browser= " + Browser)
        if Browser == "":
            Browser = "chrome"
        LOGGER.info("setChromeDownloadPath= " + path)
        try:
            if Browser == "chrome":
                sb.driver.command_executor._commands["send_command"] = ("POST", '/session/$sessionId/chromium/send_command')
                params = {'cmd': 'Page.setDownloadBehavior',
                          'params': {'behavior': 'allow', 'downloadPath': path}}
                sb.driver.execute("send_command", params)
            elif Browser == "firefox":
                if path == "":
                    LOGGER.info("file path is null, nothing to do, return")
                    return
                if not os.path.exists(path):
                    LOGGER.info("file path didn't exist, create")
                    os.makedirs(path)
                    os.system('chmod 777 ' + path)
                LOGGER.info("skip set download path, download to local folder ~/test/downloaded_files")
            elif Browser == "edge":
                sb.driver.command_executor._commands["send_command"] = ("POST", '/session/$sessionId/chromium/send_command')
                params = {'cmd': 'Page.setDownloadBehavior',
                          'params': {'behavior': 'allow', 'downloadPath': path}}
                sb.driver.execute("send_command", params)
            else:
                LOGGER.info("Browser is null: " + Browser)
        except Exception as e:
            LOGGER.info("set download path exception: " + str(e))

        if not os.path.exists(path):
            LOGGER.info("os.makedirs(path): " + path)
            os.makedirs(path)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def delete_files_under_chrome_download_path(self, path="", fileName="undefined", contain_file_name=False):
        """
            delete files under chrome download path
        """
        LOGGER.info("path= " + path)
        path = path.rstrip(os.sep)
        if path == "":
            LOGGER.info("file path is null, nothing to do, return")
            return
        if not os.path.exists(path):
            os.makedirs(path)
            os.system('chmod 777 ' + path)

        try:
            files = os.listdir(path)  # read files in path
            LOGGER.info("target files name: " + fileName)
            LOGGER.info("previous files length: " + str(len(files)))
            LOGGER.info("folder_path: " + os.path.abspath(path))
            for f in files:
                LOGGER.info("file: " + f)
                try:
                    tmp_ext = f.split(".")[0]
                    LOGGER.info("tmp_ext name: " + str(tmp_ext))
                    if contain_file_name:
                        LOGGER.info("delete contain " + fileName + " file")
                        if fileName in str(tmp_ext):
                            LOGGER.info('deleting ' + f)
                            os.system("rm -fr " + path + "/" + f)
                        else:
                            LOGGER.info('not delete ')
                    else:
                        if fileName == str(tmp_ext):
                            LOGGER.info('deleting ' + f)
                            os.system("rm -fr " + path + "/" + f)
                            break
                        else:
                            LOGGER.info('not delete ')
                except Exception as e:
                    LOGGER.info(str(e))
                finally:
                    LOGGER.info('next')
            files = os.listdir(path)
            LOGGER.info("after deleting files: " + str(files))
            LOGGER.info("after deleting files length: " + str(len(files)))
            for f in files:
                LOGGER.info("file: " + f)
            LOGGER.info("====== after deleting files =======")
        except Exception as e:
            LOGGER.info("delete files exception: " + str(e))

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=10)
    def judge_file_exist(self, check_times, check_path, file_name, file_ext="gz", sleep_sec=6, match_exactly=True):
        """
        check file downloaded
        :param check_times
        :param check_path
        :param file_name
        :param file_ext: File name extension after the file is downloaded
        :param sleep_sec: Wait time between each inspection
        """
        if os.path.exists(check_path) is False:
            raise Exception("path error...")
        if str.isdigit(str(check_times)) is False:
            raise Exception("param error...")
        LOGGER.info("check_path= " + check_path)
        for number in range(1, int(check_times)):

                LOGGER.info("check file time: {}".format(str(number)))
                LOGGER.info("check file_name: {}".format(file_name))
                files = os.listdir(check_path)  # read files in path
                LOGGER.info("read files in path: " + str(files))
                file_full_name = ""
                for i in files:
                    if match_exactly:
                        # match exactly
                        if "." in i:
                            tmp = i.split(".")[0]
                            LOGGER.info("match exactly read files name: " + str(tmp))
                            if file_name == tmp:
                                file_full_name = i
                                LOGGER.info("file_full_name:{}".format(file_full_name))
                                break
                        else:
                            LOGGER.info("match exactly read files name: " + str(i))
                            if file_name in i:
                                file_full_name = i
                                LOGGER.info("file_full_name:{}".format(file_full_name))
                                break
                    else:
                        LOGGER.info("match not exactly read files name: " + str(i))
                        if file_name in i:
                            file_full_name = i
                            LOGGER.info("file_full_name:{}".format(file_full_name))
                            break

                time.sleep(int(sleep_sec))
                LOGGER.info("file_full_name:{}".format(file_full_name))
                tmp_ext = os.path.splitext(file_full_name)[-1]
                LOGGER.info("tmp_ext:{}".format(tmp_ext))

                if "crdownload" == str(tmp_ext.rsplit(".")[-1]):
                    time.sleep(int(sleep_sec))
                    continue
                if "tmp" == str(tmp_ext.rsplit(".")[-1]):
                    time.sleep(int(sleep_sec))
                    continue
                if file_ext == "zipOrgz":
                    LOGGER.info("special format for GSQL output files: zipOrgz")
                    if "zip" == str(tmp_ext.rsplit(".")[-1]) or "gz" == str(tmp_ext.rsplit(".")[-1]):
                        return True, file_full_name
                elif file_ext == str(tmp_ext.rsplit(".")[-1]):
                    return True, file_full_name
        return False, ""

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def waitHistoryPerfomance(self, sb, check_times, query_name, wait_sec=10):
        """
        Because there is a delay in the history perfomance display, need loop detection result
        """
        for number in range(1, int(check_times)):
            LOGGER.info("check file time: {}".format(str(number)))
            sb.click(WLMLocator.refresh)
            sb.wait_for_ready_state_complete(timeout=10)
            try:
                sb.wait_for_text_visible(query_name, timeout=10)
                sb.attach_allure_screenshot("query name successfully screenshot")
                return True
            except TextNotVisibleException:
                sb.sleep(wait_sec)
                continue
        return False

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def get_tg_cookie(self, sb):
        sb.wait(3)  # wait for get cookie, or get None
        cc = sb.driver.get_cookie("TigerGraphApp")
        LOGGER.info("get_cookie:"+str(cc))
        if cc is None:
            raise Exception("get wrong cookie:" + str(cc))
        return cc["value"]

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def installQueryByAPI(self, sb):
        cc = self.get_tg_cookie(sb)
        command = "curl --location --request POST '{0}/api/queries/MyGraph/gsql/install?queryName=pageRank' \
                    --header 'Cookie:  TigerGraphApp={1}'".format(self.test_env,cc)
        api_res = os.popen(command).readlines()

        # Processing API results
        res = ""
        if len(api_res) > 0:
            for i in api_res:
                if "results" in i:
                    res = i
                    break
                else:
                    continue
        # Convert error flag, otherwise converting to dict will report error
        false = False
        true = True
        dict_res = eval(res.lstrip("data:"))
        if dict_res["error"]:
            sb.fail(msg=api_res)
        return dict_res["error"], dict_res["results"]

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def runQueryByAPI(self, sb):
        cc = self.get_tg_cookie(sb)
        host = self.test_env.split("//")[-1]
        param = '{"maxChange":0.1,"maxIter":10,"damping":0.1,"display":"true","outputLimit":10}'
        command = """
            curl --location --request POST '{0}/api/restpp/query/MyGraph/pageRank' \
            --header 'Accept:  text/event-stream' \
            --header 'Accept-Encoding:  gzip, deflate' \
            --header 'Accept-Language:  en,zh-CN;q=0.9,zh;q=0.8' \
            --header 'Connection:  keep-alive' \
            --header 'Content-Type:  text/plain;charset=UTF-8' \
            --header 'Cookie:  TigerGraphApp={2}' \
            --header 'Host:  {1}' \
            --header 'Origin:  {0}' \
            --header 'Referer:  {0}/studio/' \
            --header 'Request-Type:  long' \
            --header 'User-Agent:  Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
            --data-raw '{3}'
            """.format(self.test_env, host, cc, param)
        api_res = os.popen(command).readlines()

        # Processing API results
        res = ""
        if len(api_res) > 0:
            for i in api_res:
                if 'data:{"error":' in i:
                    res = i
                    break
                else:
                    continue
        # Convert error flag, otherwise converting to dict will report error
        false = False
        true = True
        dict_res = eval(res.lstrip("data:"))
        if dict_res["error"]:
            sb.fail(msg=api_res)

        return dict_res["error"], dict_res["results"]

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=10)
    def createUser(self, sb, username="", pw="", group_name="",name_key=""):
        try:
            if not sb.is_element_present(GAPHomeLocators.users):
                LOGGER.info("user menu not present, click management menu")
                sb.click(GAPHomeLocators.management, timeout=click_timeout)
                sb.click(GAPHomeLocators.users, timeout=click_timeout)
            sb.wait_for_element_clickable(GAPUsersLocator.usertab, timeout=click_timeout)
            sb.click(GAPUsersLocator.usertab)
            sb.wait_for_ready_state_complete()
            sb.wait_for_element_clickable(GAPUsersLocator.adduser, timeout=click_timeout)
            if group_name != "":
                sb.wait_for_element_clickable(GAPUsersLocator.proxy_group_button, timeout=click_timeout)
                sb.click(GAPUsersLocator.proxy_group_button, timeout=click_timeout)
            # if user is exists, will remove first
            sb.sleep(3)
            # select show 20/page
            self.enlarge_items_per_page(sb)
            sb.wait_for_element_clickable(GAPUsersLocator.proxy_group_button, timeout=click_timeout)
            userlist = sb.find_elements("//tr")  # get row of table
            LOGGER.info("first userlist length: " + str(len(userlist)))
            if len(userlist) > 0:
                for u in range(len(userlist)):
                    # must get element list again, or report error
                    userlist = sb.find_elements("//tr")
                    LOGGER.info("second userlist length: " + str(len(userlist)))
                    if username in userlist[u].text:
                        LOGGER.info("user {} exits, will remove the user first".format(username))
                        if group_name != "":
                            sb.click("(//button[@id='delete-group']/span/mat-icon)[{}]".format(u))
                        else:
                            sb.click("(//button[@id='delete-user']/span/mat-icon)[{}]".format(u))
                        sb.click("//button[contains(.,'OK')]")
                        sb.sleep(2)
                        break
            else:
                LOGGER.info("no username to delete ")
            sb.wait_for_ready_state_complete()
            #  add user
            if group_name == "":
                sb.wait_for_element_clickable(GAPUsersLocator.adduser, timeout=click_timeout)
                sb.click(GAPUsersLocator.adduser)
                sb.wait_for_element_clickable(GAPUsersLocator.inputusername, timeout=click_timeout)
                sb.type(selector=GAPUsersLocator.inputusername, text=username, timeout=click_timeout)
                sb.type(selector=GAPUsersLocator.inputpw, text=pw, timeout=click_timeout)
                sb.type(selector=GAPUsersLocator.cofirmpw,text=pw, timeout=click_timeout)
            else:
                sb.wait_for_element_clickable(GAPUsersLocator.add_group_button, timeout=click_timeout)
                sb.click(GAPUsersLocator.add_group_button)
                sb.wait_for_element_clickable(GAPUsersLocator.input_group_name, timeout=click_timeout)
                sb.type(selector=GAPUsersLocator.input_group_name, text=group_name, timeout=click_timeout)
                sb.type(selector=GAPUsersLocator.input_name_key, text=name_key, timeout=click_timeout)
                sb.type(selector=GAPUsersLocator.input_name_value, text=username, timeout=click_timeout)

            sb.click(GAPUsersLocator.usersubmit)
            sb.assert_text(username, timeout=assertTextTimeout)
            sb.attach_allure_screenshot("Create User {} successfully screenshot".format(username))
        except Exception as e:
            sb.attach_allure_screenshot("open menu failed when createUser screenshot")
            self.intoUserManegement(sb)
            raise Exception(e)
        
    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=10)
    def createRole(self, sb, role_name, is_global, graph_name="MyGraph"):
        try:
            if not sb.is_element_present(GAPHomeLocators.users):
                LOGGER.info("user menu not present, click management menu")
                sb.click(GAPHomeLocators.management, timeout=click_timeout)
                sb.click(GAPHomeLocators.users, timeout=click_timeout)
            # turn to user-defined role page
            sb.wait_for_element_clickable(GAPUsersLocator.roletab, timeout=click_timeout)
            sb.click(GAPUsersLocator.roletab)
            sb.wait_for_ready_state_complete()
            sb.wait_for_element_clickable(GAPUsersLocator.create_role_button, timeout=click_timeout)
            sb.sleep(3)
            # select show 20/page
            self.enlarge_items_per_page(sb)
            # delete the role if it already existing
            if sb.is_element_present(GAPUsersLocator.span_text.format(role_name)):
                self.deleteRole(sb, role_name)
            # click create role
            sb.wait_for_element_clickable(GAPUsersLocator.create_role_button, timeout=click_timeout)
            sb.click(GAPUsersLocator.create_role_button)
            sb.wait_for_ready_state_complete()
            # input the role name
            sb.type(selector=GAPUsersLocator.rolename, text=role_name, timeout=click_timeout)
            if is_global:
                sb.wait_for_element_clickable(GAPUsersLocator.role_type_global, timeout=wait_render_timeout)
                sb.click(GAPUsersLocator.role_type_global, timeout=click_timeout)
            else:
                sb.wait_for_element_clickable(GAPUsersLocator.role_type_local, timeout=wait_render_timeout)
                sb.click(GAPUsersLocator.role_type_local, timeout=click_timeout)
                sb.wait_for_element_clickable(GAPUsersLocator.select_a_graph, timeout=wait_render_timeout)
                sb.click(GAPUsersLocator.select_a_graph, timeout=click_timeout)
                sb.hover_on_element(GAPUsersLocator.span_text.format(graph_name))
                # choose the graph
                sb.wait_for_element_clickable(GAPUsersLocator.span_text.format(graph_name), timeout=click_timeout)
                sb.click(GAPUsersLocator.span_text.format(graph_name))
            # create role and check the results 
            sb.wait_for_element_clickable(GAPUsersLocator.create_button, timeout=click_timeout)
            sb.click(GAPUsersLocator.create_button)
            sb.assert_text("The role {} has been successfully created".format(role_name), timeout=assertTextTimeout)
            sb.wait_for_element_clickable(GAPUsersLocator.no_button, timeout=click_timeout)
            sb.click(GAPUsersLocator.no_button)
            self.wait_progress_bar(sb=sb, wait_present_time=10, wait_not_visible_time=15)
            sb.wait_for_element_clickable(GAPUsersLocator.create_role_button, timeout=click_timeout)
            sb.attach_allure_screenshot("Create role {} successfully screenshot".format(role_name))
        except Exception as e:
            sb.attach_allure_screenshot("open menu failed screenshot")
            self.intoUserManegement(sb)
            raise Exception(e)
        
    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def deleteRole(self, sb, role_name):
        try:
            # turn to user-defined role page
            sb.wait_for_element_clickable(GAPUsersLocator.roletab, timeout=click_timeout)
            sb.click(GAPUsersLocator.roletab)
            sb.wait_for_ready_state_complete()
            # click delete role
            sb.wait_for_element_clickable(GAPUsersLocator.create_role_button, timeout=click_timeout)
            sb.click(GAPUsersLocator.delete_rolename.format(role_name))
            # confirm delete the role
            sb.assert_text("Are you sure you want to delete the selected role", timeout=assertTextTimeout)
            sb.wait_for_element_clickable(GAPUsersLocator.ok_button, timeout=click_timeout)
            sb.click(GAPUsersLocator.ok_button)
            sb.wait_for_element_clickable(GAPUsersLocator.create_role_button, timeout=click_timeout)
            sb.wait_for_element_not_visible(GAPUsersLocator.delete_rolename.format(role_name), timeout=click_timeout)
            sb.attach_allure_screenshot("Delete role {} successfully screenshot".format(role_name))
        except Exception as e:
            sb.attach_allure_screenshot("delete role failed screenshot")
            self.intoUserManegement(sb)
            raise Exception(e)

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=10)
    def grantGlobalPrivilege(self, sb, role_name, privileges):
        actual_privileges=privileges
        # check the database version, switch use different query roles
        loginUtil = LoginUtils(self)
        cloud_env_flag = loginUtil.is_oncloud()
        if cloud_env_flag:
            version_tmp, is_three_version = self.get_cloud_tg_version(sb)
            if version_tmp and version_tmp.strip():
                if is_three_version:
                    LOGGER.info("on cloud current version is: {}".format(version_tmp))
                    actual_privileges = ["READ_SCHEMA", "READ_QUERY", "WRITE_QUERY", "READ_DATA"]
        else:
            version_tmp, is_three_version = self.get_on_prem_tg_version(sb)
            if version_tmp and version_tmp.strip():
                if is_three_version:
                    LOGGER.info("on-prem current version is: {}".format(version_tmp))
                    actual_privileges = ["READ_SCHEMA", "READ_QUERY", "WRITE_QUERY", "READ_DATA"]
        LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))

        try:
            # turn to user-defined role page
            sb.wait_for_element_clickable(GAPUsersLocator.roletab, timeout=click_timeout)
            sb.click(GAPUsersLocator.roletab)
            sb.wait_for_ready_state_complete()
            # click edit role
            sb.wait_for_element_clickable(GAPUsersLocator.create_role_button, timeout=click_timeout)
            sb.click(GAPUsersLocator.edit_rolename.format(role_name))
            # grant the provide privilege one by one
            sb.assert_text("Global privileges", timeout=assertTextTimeout)
            for privilege in actual_privileges:
                if not sb.is_checked(GAPUsersLocator.input_text.format(privilege), timeout=click_timeout):
                    sb.wait_for_element_clickable(GAPUsersLocator.span_text.format(privilege), timeout=click_timeout)
                    sb.click(GAPUsersLocator.span_text.format(privilege))
                    sb.assertTrue(sb.is_checked(GAPUsersLocator.input_text.format(privilege), timeout=wait_render_timeout))
                    sb.sleep(2)
            sb.attach_allure_screenshot("Grant global role {} successfully screenshot".format(role_name))
        except Exception as e:
            sb.attach_allure_screenshot("open menu failed screenshot")
            self.intoUserManegement(sb)
            raise Exception(e)

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=10)
    def grantLocalPrivilegeWithReadData(self, sb, role_name, privileges, attributes, query_name, graph_name="MyGraph"):
        actual_privileges=privileges
        actual_query_name=query_name
        # check the database version, switch use different query roles
        loginUtil = LoginUtils(self)
        cloud_env_flag = loginUtil.is_oncloud()
        if cloud_env_flag:
            version_tmp, is_three_version = self.get_cloud_tg_version(sb)
            if version_tmp and version_tmp.strip():
                if is_three_version:
                    LOGGER.info("on cloud current version is: {}".format(version_tmp))
                    actual_privileges = ["READ_SCHEMA", "READ_QUERY", "WRITE_QUERY"]
                    actual_query_name = []
        else:
            version_tmp, is_three_version = self.get_on_prem_tg_version(sb)
            if version_tmp and version_tmp.strip():
                if is_three_version:
                    LOGGER.info("on-prem current version is: {}".format(version_tmp))
                    actual_privileges = ["READ_SCHEMA", "READ_QUERY", "WRITE_QUERY"]
                    actual_query_name = []
        LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))

        try:
            # turn to user-defined role page
            sb.wait_for_element_clickable(GAPUsersLocator.roletab, timeout=click_timeout)
            sb.click(GAPUsersLocator.roletab)
            sb.attach_allure_screenshot("click roles tab screenshot")
            sb.wait_for_ready_state_complete()

            # click edit role
            sb.wait_for_element_clickable(GAPUsersLocator.create_role_button, timeout=click_timeout)
            sb.click(GAPUsersLocator.edit_rolename.format(role_name))
            sb.attach_allure_screenshot("click edit roles done screenshot")
            # grant the provide privilege one by one
            sb.assert_text(graph_name, timeout=assertTextTimeout)
            for privilege in actual_privileges:
                if not sb.is_checked(GAPUsersLocator.input_text.format(privilege), timeout=click_timeout):
                    sb.wait_for_element_clickable(GAPUsersLocator.span_text.format(privilege), timeout=click_timeout)
                    sb.click(GAPUsersLocator.span_text.format(privilege))
                    sb.assertTrue(sb.is_checked(GAPUsersLocator.input_text.format(privilege), timeout=wait_render_timeout))
                    sb.sleep(2)
            sb.attach_allure_screenshot("grant the provide privilege screenshot")
            # grant the read_data privilege with attribute name
            for attribute in attributes:
                if not sb.is_checked(GAPUsersLocator.read_data_input.format(attribute), timeout=click_timeout):
                    sb.wait_for_element_clickable(GAPUsersLocator.read_data_text.format(attribute), timeout=click_timeout)
                    sb.click(GAPUsersLocator.read_data_text.format(attribute))
                    sb.assertTrue(sb.is_checked(GAPUsersLocator.read_data_input.format(attribute), timeout=wait_render_timeout))
                    sb.sleep(2)
            sb.attach_allure_screenshot("grant the read_data privilege screenshot")
            # grant the all query privileges with query name
            for name in actual_query_name:
                local_query_privilege = LocalQueryPrivilege()
                for query_priv in dir(local_query_privilege):
                    if not query_priv.startswith("__") and not callable(getattr(local_query_privilege, query_priv)):
                        query_element = getattr(local_query_privilege, query_priv)
                        if not sb.is_checked(query_element.format(name, "/input"), timeout=click_timeout):
                            sb.wait_for_element_clickable(query_element.format(name, ""), timeout=click_timeout)
                            sb.click(query_element.format(name, ""))
                            sb.assertTrue(sb.is_checked(query_element.format(name, "/input"), timeout=wait_render_timeout))
                            sb.sleep(2)
            sb.attach_allure_screenshot("Grant local role {} successfully screenshot".format(role_name))
        except Exception as e:
            sb.attach_allure_screenshot("grant role failed screenshot")
            self.intoUserManegement(sb)
            raise Exception(e)

    def getUserLocator(self, sb, username):
        """
        Get user locator dynamically based on username
        """
        sb.wait_for_element_visible(GAPUsersLocator.userlabel, timeout=wait_render_timeout)
        user_arry = sb.find_elements(GAPUsersLocator.userlabel)
        LOGGER.info("user_arry lenth: " + str(len(user_arry)))
        for el in user_arry:
            LOGGER.info("username: " + username + ", user(el.text): " + el.text)
            if username == el.text:
                user_index = user_arry.index(el)
                user_loacator = "#mat-checkbox-{} .mat-checkbox-label".format(user_index+1)
                LOGGER.info("user_index: " + str(user_index) +" ,user_loacator:" + user_loacator)
                return user_index+1, user_loacator

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=10)
    def select_role(self, sb, role):
        # select role
        sel_role = 'document.getElementsByTagName("MAT-SELECT")[1].click()'
        sb.execute_script(sel_role)
        # sb.click(GAPHomeLocators.management, timeout=click_timeout)
        # sb.attach_allure_screenshot("select " + role + " successfully screenshot")
        role_arr = sb.find_elements("span.mat-option-text")
        for g in role_arr:
            LOGGER.info("expected role:" + role + ", current role:" + str(g.text))
            if role == g.text:
                role_index = role_arr.index(g)
                role_arr[role_index].click()
                # c_role = 'document.getElementsByTagName("MAT-OPTION")[{}].click()'.format(role_index)
                # sb.execute_script(c_role)
                sb.attach_allure_screenshot("select " + role + " successfully screenshot")
                break

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=10)
    def grantRole(self, sb, username, role="globaldesigner", isglobal=True, graph="", group_name=""):
        """
        Grant different types of roles to user, each option is required
        """
        try:
            if not sb.is_element_present(GAPHomeLocators.users):
                LOGGER.info("user menu not present, click management menu")
                sb.click(GAPHomeLocators.management, timeout=click_timeout)
                sb.click(GAPHomeLocators.users, timeout=click_timeout)
            sb.wait_for_element_clickable(GAPUsersLocator.granttab, timeout=click_timeout)
            sb.click(GAPUsersLocator.granttab, timeout=click_timeout)
            sb.wait_for_element_clickable(GAPUsersLocator.drop_list_buttons, timeout=click_timeout)
            LOGGER.info("username: " + username + ", role: " + role + ", graph: " + graph + ", group_name: " + group_name)
            sb.sleep(2)
            if group_name != "":
                # select globaldesigner
                self.select_role(sb, role="globaldesigner")
                # click Proxy Group button
                sb.find_elements(GAPUsersLocator.grant_group)[3].click()
                LOGGER.info("click proxy group button ")
                sb.sleep(5)# should wait the checkbox render,or will find the User's checkbox
                sb.wait_for_element_clickable(GAPUsersLocator.checkbox, timeout=click_timeout)
                elements = sb.find_elements(GAPUsersLocator.checkbox)
                LOGGER.info("elements lenth: " + str(len(elements)))
                elements[0].click()
                LOGGER.info("click checkbox button ")
                sb.wait_for_element_clickable(GAPUsersLocator.saveGrant, timeout=click_timeout)
                sb.click(GAPUsersLocator.saveGrant)
                LOGGER.info("click saveGrant button ")
                sb.assert_text("Successfully granted globaldesigner on global to group", timeout=assertTextTimeout)
                sb.attach_allure_screenshot("granted globaldesigner on global to group successfully screenshot")
                return
            LOGGER.info("username:" + username + ", graph: " + graph + ", group_name:" +group_name)
            if "globaldesigner" == role:
                # select globaldesigner role
                self.select_role(sb, role="globaldesigner")
                # grant role to user
                user_index, user_loacator = self.getUserLocator(sb, username)
                sel_user = 'document.getElementsByTagName("INPUT")[{}].click()'.format(user_index)
                sb.execute_script(sel_user)
                sb.click(GAPUsersLocator.saveGrant)
                sb.attach_allure_screenshot("granted globaldesigner role successfully screenshot")
                return True, user_index
            elif isglobal:
                # select the role
                self.select_role(sb, role=role)
                # grant role to user
                user_index, user_loacator = self.getUserLocator(sb, username)
                sel_user = 'document.getElementsByTagName("INPUT")[{}].click()'.format(user_index)
                sb.execute_script(sel_user)
                sb.click(GAPUsersLocator.saveGrant)
                sb.attach_allure_screenshot("granted role successfully screenshot")
                return True, user_index
            elif graph != "":
                # plan A:　grant role via UI
                # local role, need select graph and role
                # sb.wait(5)
                # sel_graph = 'document.getElementsByTagName("MAT-SELECT")[0].click()'
                # sb.execute_script(sel_graph)
                # sb.wait(2)
                # graph_arr = sb.find_elements("//mat-option")
                # for g in graph_arr:
                #     if graph == g.text:
                #         graph_index = graph_arr.index(g)
                #         cli_graph = 'document.getElementsByTagName("MAT-OPTION")[{}].click()'.format(graph_index)
                #         sb.execute_script(cli_graph)
                # sb.wait(3)
                # # select the role
                # self.select_role(sb, role=role)
                # # grant role
                # sb.wait(3)
                # user_index, user_loacator = self.getUserLocator(sb, username)
                # sel_user = 'document.getElementsByTagName("INPUT")[{}].click()'.format(user_index)
                # sb.execute_script(sel_user)
                # sb.click(GAPUsersLocator.saveGrant)
                # sb.attach_allure_screenshot("granted role successfully screenshot")
                loginUtil = LoginUtils(sb)
                cloud_env_flag = loginUtil.is_oncloud()
                if cloud_env_flag:
                    version_tmp, is_three_version = self.get_cloud_tg_version(sb)
                else:
                    version_tmp, is_three_version = self.get_on_prem_tg_version(sb)
                LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))

                # plan B: grant role via API
                LOGGER.info("begin to grant role via API")
                self.grant_role_via_API(graph_name=graph, roles=role, users=username, is_three_version=is_three_version)
                return False, -1
            sb.fail("function grantRole() params error, please correct")
        except Exception as e:
            LOGGER.info("grant role failed, refresh menu and retry")
            sb.attach_allure_screenshot("granted role failed screenshot")
            self.intoUserManegement(sb)
            raise Exception(e)


    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=10)
    def grant_role_via_API(self, graph_name="", roles="", users="", is_three_version=False):
        login = Login()
        gus = GUS()
        cookie = login.login().get_cookies()
        role = [roles]
        user = [users]
        if is_three_version:
            params = {
                "roles": role,
                "usernames": user
            }
            result = gus.grant_role_to_graph_v3(cookie, graph_name, params)
        else:
            params = {
                "roles": role,
                "users": user
            }
            result = gus.grant_role_to_graph(cookie, graph_name, params)

        # check error message
        result.assert_result(200, False, "")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def login_OKTA_SSO(self, sb):
        """
            login OKTA config
        """
        try:
            sb.wait_for_element_clickable(GAPHomeLocators.okta_account, timeout=wait_render_timeout)
            sb.type(GAPHomeLocators.okta_account, "<EMAIL>")
            sb.type(GAPHomeLocators.okta_psd, "Tigerqe123")
            sb.click(GAPHomeLocators.okta_submit, timeout=click_timeout)
            LOGGER.info("login OKTA")
            sb.attach_allure_screenshot("OKTA login screenshot")

        except Exception as e:
            LOGGER.info("okta login failed, refresh and retry")
            sb.attach_allure_screenshot("okta login failed screenshot")
            sb.refresh()
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def set_OKTA_SSO_config(self, sb):
        """
            set OKTA config
        """
        try:
            sb.open("https://dev-********.okta.com/login/login.htm")
            sb.wait_for_element_clickable(GAPHomeLocators.okta_account, timeout=wait_render_timeout)
            sb.type(GAPHomeLocators.okta_account, "<EMAIL>")
            sb.type(GAPHomeLocators.okta_psd, "Tigerqe123")
            sb.click(GAPHomeLocators.okta_submit, timeout=click_timeout)
            LOGGER.info("login OKTA")
            sb.attach_allure_screenshot("OKTA login screenshot")

            sb.assert_text("<EMAIL>", timeout=wait_render_timeout)
            sb.open("https://dev-********-admin.okta.com/admin/apps/saml-wizard/edit/dev-********_tigergraphqe02_1")
            sb.wait_for_element_clickable(GAPHomeLocators.okta_next, timeout=wait_render_timeout)
            sb.click(GAPHomeLocators.okta_next, timeout=click_timeout)
            sb.attach_allure_screenshot("OKTA set config screenshot")
            # input params
            sb.wait_for_element_clickable(GAPHomeLocators.okta_acs, timeout=wait_render_timeout)
            sb.type(GAPHomeLocators.okta_acs, self.test_env + "/api/auth/saml/acs")
            # tg_3.x  /gsqlserver/gsql/saml/meta , tg_4.x  /gsql/v1/saml/meta
            sb.type(GAPHomeLocators.okta_meta, self.test_env + "/gsqlserver/gsql/saml/meta")
            sb.click(GAPHomeLocators.okta_next, timeout=click_timeout)
            sb.wait_for_element_clickable(GAPHomeLocators.okta_finish, timeout=wait_render_timeout)
            sb.click(GAPHomeLocators.okta_finish, timeout=click_timeout)
            sb.wait_for_ready_state_complete()
            sb.sleep(3) #should wait page rendered or open GAP will fail
            sb.attach_allure_screenshot("OKTA set config done screenshot")

        except Exception as e:
            LOGGER.info("okta set failed, refresh and retry")
            sb.attach_allure_screenshot("okta set failed screenshot")
            sb.refresh()
            raise Exception(e)

    def set_AZURE_SSO_config(self, sb):
        """
            set AZURE config.
            not need add retry, will pop window reload alert message
        """
        try:
            sb.open("https://entra.microsoft.com/#view/Microsoft_AAD_IAM/ManagedAppMenuBlade/~/SignOn/objectId/17e9dc90-ff0b-4a1f-89f9-8fd65803a3d6/appId/958129d6-c9c2-47df-90be-bd64ec4a59c9/preferredSingleSignOnMode/saml")
            sb.wait_for_element_clickable(GAPHomeLocators.azure_account, timeout=wait_render_timeout)
            sb.type(GAPHomeLocators.azure_account, "<EMAIL>")
            sb.click(GAPHomeLocators.azure_submit, timeout=click_timeout)

            sb.wait_for_element_clickable(GAPHomeLocators.azure_psd, timeout=wait_render_timeout)
            sb.type(GAPHomeLocators.azure_psd, "Qod70810")
            sb.click(GAPHomeLocators.azure_submit, timeout=click_timeout)

            sb.wait_for_element_clickable(GAPHomeLocators.azure_back, timeout=wait_render_timeout)
            sb.click(GAPHomeLocators.azure_back, timeout=click_timeout)
            LOGGER.info("login AZURE")
            sb.attach_allure_screenshot("AZURE login screenshot")

            sb.assert_text("<EMAIL>", timeout=wait_render_timeout)
            sb.assert_text("Set up Single Sign-On with SAML", timeout=wait_render_timeout)
            sb.wait_for_element_clickable(GAPHomeLocators.edit_saml, timeout=wait_render_timeout)
            sb.click(GAPHomeLocators.edit_saml, timeout=click_timeout)
            sb.attach_allure_screenshot("AZURE set config screenshot")
            # input meta params
            sb.wait_for_element_clickable(GAPHomeLocators.meta_cells, timeout=wait_render_timeout)
            sb.click_nth_visible_element(GAPHomeLocators.meta_cells, number=1, timeout=wait_render_timeout)
            meta_element = sb.find_elements(GAPHomeLocators.meta_inputs)[4]
            meta_element.clear()
            meta_element.send_keys(self.test_env + "/gsql/v1/saml/meta")
            # input acs params
            sb.wait_for_element_clickable(GAPHomeLocators.meta_cells, timeout=wait_render_timeout)
            sb.click_nth_visible_element(GAPHomeLocators.meta_cells, number=3, timeout=wait_render_timeout)
            meta_element = sb.find_elements(GAPHomeLocators.meta_inputs)[5]
            meta_element.clear()
            meta_element.send_keys(self.test_env + "/api/auth/saml/acs")
            # sb.sleep(300) # debug
            # save
            sb.wait_for_element_clickable(GAPHomeLocators.azure_save_button, timeout=wait_render_timeout)
            sb.click(GAPHomeLocators.azure_save_button, timeout=click_timeout)
            sb.attach_allure_screenshot("AZURE set config done screenshot")
            sb.assert_text("saved successfully", timeout=wait_click_timeout)
            sb.wait_for_ready_state_complete()
            sb.sleep(3)


        except Exception as e:
            LOGGER.info("AZURE set failed")
            sb.attach_allure_screenshot("AZURE set failed screenshot")
            LOGGER.info(str(e))

        finally:
            # sb.wait_for_element_clickable(GAPHomeLocators.azure_close_button, timeout=click_timeout)
            # sb.click_nth_visible_element(GAPHomeLocators.azure_close_button, number=2, timeout=click_timeout)
            LOGGER.info('close Azure, go on to next, login GAP')

    def login_AZURE_SSO(self, sb):
        """
            login AZURE config.
            not need add retry, will pop window reload alert message
        """
        try:
            sb.wait_for_element_clickable(GAPHomeLocators.azure_account, timeout=wait_render_timeout)
            sb.type(GAPHomeLocators.azure_account, "<EMAIL>")
            sb.click(GAPHomeLocators.azure_submit, timeout=click_timeout)

            sb.wait_for_element_clickable(GAPHomeLocators.azure_psd, timeout=wait_render_timeout)
            sb.type(GAPHomeLocators.azure_psd, "Qod70810")
            sb.click(GAPHomeLocators.azure_submit, timeout=click_timeout)

            sb.wait_for_element_clickable(GAPHomeLocators.azure_back, timeout=wait_render_timeout)
            sb.click(GAPHomeLocators.azure_back, timeout=click_timeout)
            LOGGER.info("login AZURE")
            sb.attach_allure_screenshot("AZURE login screenshot")

        except Exception as e:
            LOGGER.info("AZURE login failed")
            sb.attach_allure_screenshot("AZURE login failed screenshot")
            LOGGER.info(str(e))

        finally:
            # sb.wait_for_element_clickable(GAPHomeLocators.azure_close_button, timeout=click_timeout)
            # sb.click_nth_visible_element(GAPHomeLocators.azure_close_button, number=2, timeout=click_timeout)
            LOGGER.info('finally login AZURE')

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def change_password_for_v3(self, sb, newpw):
        sb.click(GAPProfileLocator.changepw)
        sb.type(GAPProfileLocator.dialogpw, newpw)
        sb.type(GAPProfileLocator.confirmpw, newpw)
        sb.click(GAPProfileLocator.change_for_v3)
        sb.wait_for_text_not_visible("Change Password", timeout=click_timeout)

    # this is for v4
    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def changeMypw(self, sb, oldpw, newpw):
        sb.click(GAPProfileLocator.changepw)
        # changed new UI
        sb.find_elements(GAPProfileLocator.password_input_list)[0].send_keys(oldpw)
        sb.find_elements(GAPProfileLocator.password_input_list)[1].send_keys(newpw)
        sb.find_elements(GAPProfileLocator.password_input_list)[2].send_keys(newpw)
        sb.click(GAPProfileLocator.change)
        sb.wait_for_text_not_visible("Change Password")

    # def getLicense(self, sb):
    #  need connect vpn
    #     if os.path.exists("LICENSE.txt"):
    #         os.remove("LICENSE.txt")
    #     os.system("curl -o LICENSE.txt ftp://*************/lic/license3.txt")
    #     license_path = os.path.join(os.getcwd(), "LICENSE.txt")
    #     print(license_path)
    #     if os.path.exists(license_path):
    #         LOGGER.info("LICENSE download success")
    #         return license_path
    #     else:
    #         print(os.path.exists(license_path))
    #         sb.fail("Get license fail")

    def check_27_env(self, sb, skip_reason=""):
        if "************" not in self.test_env:
            pytest.skip("Skipping this test because " + skip_reason)
        else:
            LOGGER.info("it's 27 env, continue")


    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def getLicense(self, sb):
        if "************" not in self.test_env:
            pytest.skip("Skipping this test because there is no config the s3 account in github action for download license")
        # download license from s3 later
        license_path = os.path.join(os.getcwd(), "data", "license", "LICENSE.txt")
        LOGGER.info("license_path: " + str(license_path))
        if os.path.exists(license_path):
            LOGGER.info("license_path existed, remove ")
            os.remove(license_path)
        command = "export AWS_PROFILE=qe_tigergraph && aws s3 cp s3://qe-test/license/LICENSE.txt {}".format(license_path)
        LOGGER.info("download command: " + command)
        downloaded_res = os.popen(command).readlines()
        LOGGER.info("downloaded_result: " + str(downloaded_res))
        if os.path.exists(license_path):
            LOGGER.info("download succeed in" + license_path)
            return license_path
        else:
            LOGGER.info("license_path exists: " + str(os.path.exists(license_path)))
            sb.fail("Get license fail")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def request_token(self, sb, secret_value):
        loginUtil = LoginUtils(self)
        cloud_env_flag = loginUtil.is_oncloud()
        if cloud_env_flag:
            LOGGER.info("on cloud not support to request token")
            return
        else:
            version_tmp, is_three_version = self.get_on_prem_tg_version(sb)
            LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))
            if version_tmp and version_tmp.strip():
                if is_three_version:
                    LOGGER.info("on-prem current version is: {}".format(version_tmp))

        host = self.test_env.rstrip("14240")
        data = '{"secret":"%s", "graph":"MyGraph", "lifetime":"100000"}' % secret_value
        if is_three_version:
            command = """
                    curl --location --request POST '{0}9000/requesttoken' \
                        -d '{1}' -k
                    """.format(host, data)
        else:
            command = """
            curl --location --request POST -H 'Content-Type: application/json' '{0}14240/gsql/v1/tokens' \
                -d '{1}' -k
            """.format(host, data)
        LOGGER.info("command:" + command)
        api_res = os.popen(command).readlines()
        LOGGER.info("run command result: " + str(api_res))
        if "gadmin config set RESTPP.Factory.EnableAuth" in str(api_res):
            LOGGER.info("RESTPP.Factory.EnableAuth not true, should open the config")
            return True
        elif "Generate new JWT token successfully" in str(api_res):
            LOGGER.info("Generate new JWT token successfully")
            return True
        elif "Please enter a valid one" in str(api_res):
            LOGGER.info("check secret is invalid successfully")
            return True
        else:
            LOGGER.info("Generate new token with secret fail")
            return False

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def RESTPP_config(self, sb, restore_flag=False, default_query_timeout=""):
        self.into_components_menu(sb, sub_menu="RESTPP")
        # wait input widgets clickable
        sb.wait_for_element_visible(RestPPLocators.header, timeout=wait_render_timeout)
        sb.wait_for_element_clickable(GAPHomeLocators.input_parameter, timeout=wait_render_timeout)
        # input parameters
        elements = sb.find_elements(GAPHomeLocators.input_parameter)
        sb.attach_allure_screenshot("old parameters screenshot")
        LOGGER.info("input number:" + str(len(elements)))
        for i in range(len(elements)):
            if i == 0:
                # Default query timeout (seconds)
                elements[i].clear()
                if restore_flag:
                    # request by gle_gquery regress3000
                    elements[i].send_keys('1000')
                else:
                    if default_query_timeout == "":
                        LOGGER.info("input default_query_timeout 600" + default_query_timeout)
                        elements[i].send_keys('600')
                    else:
                        LOGGER.info("input default_query_timeout " + default_query_timeout)
                        elements[i].send_keys(default_query_timeout)
            elif i == 1:
                # Maximum concurrent running "heavy" built-in queries
                elements[i].clear()
                elements[i].send_keys('200')
            elif i == 2:
                # Maximum concurrent running queries
                elements[i].clear()
                elements[i].send_keys('15000')
            elif i == 3:
                # Maximum delay queue size
                elements[i].clear()
                elements[i].send_keys('20')

        # apply
        sb.click(GAPHomeLocators.apply_button, timeout=click_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)
        sb.sleep(2)
        sb.wait_for_element_clickable(GAPHomeLocators.ok_button, timeout=wait_render_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)

        # wait progress bar disappear
        self.wait_progress_bar(sb, wait_present_time=click_timeout, wait_not_visible_time=wait_render_timeout)
        sb.assert_text("Successfully restarted the services", timeout=assertTextTimeout)
        sb.attach_allure_screenshot("apply successfully screenshot")
        sb.sleep(20)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def init_RESTPP_auth_config(self, sb, restore_flag=False):
        # Enable AUTH
        checkbox_status = sb.find_elements(GAPHomeLocators.checkbox_status)[0].get_attribute("aria-checked").lower()
        LOGGER.info("first checkbox_status: " + checkbox_status)
        if checkbox_status == "true":
            LOGGER.info("current status is enable AUTH, should disable ")
            sb.find_elements(GAPHomeLocators.enable_button)[0].click()
        else:
            LOGGER.info("current status is disable AUTH, will do nothing ")
            sb.attach_allure_screenshot("current status is disable AUTH, will do nothing screenshot")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def RESTPP_auth_config(self, sb, restore_flag=False):
        self.into_components_menu(sb, sub_menu="RESTPP")
        # wait input widgets clickable
        sb.wait_for_element_visible(RestPPLocators.header, timeout=wait_render_timeout)
        # Enable AUTH, version >= 3.9.3 support on frontend
        checkbox_status = sb.find_elements(GAPHomeLocators.checkbox_status)[0].get_attribute("aria-checked").lower()
        LOGGER.info("first checkbox_status: " + checkbox_status)
        if checkbox_status == "true":
            if restore_flag:
                LOGGER.info("current status is enable AUTH, disable it to restore")
                sb.find_elements(GAPHomeLocators.enable_button)[0].click()
            else:
                LOGGER.info("current status is enable AUTH, disable it to init env")
                # init the checkbox_status,then enable
                sb.find_elements(GAPHomeLocators.enable_button)[0].click()
                # apply
                self.wait_and_click(sb, GAPHomeLocators.apply_button)
                self.wait_and_click(sb, GAPHomeLocators.ok_button)
                self.wait_progress_bar(sb, wait_present_time=short_click_timeout, wait_not_visible_time=wait_render_timeout)
                sb.wait_for_element_clickable(GAPHomeLocators.ok_button, timeout=wait_render_timeout)
                sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)

                # wait progress bar disappear
                self.wait_progress_bar(sb, wait_present_time=click_timeout, wait_not_visible_time=wait_render_timeout)
                sb.assert_text("Successfully restarted the services", timeout=assertTextTimeout)
                sb.attach_allure_screenshot("disable auth apply successfully screenshot")
                sb.sleep(20)
                # enable it again, refresh first
                sb.refresh()
                self.into_components_menu(sb, sub_menu="RESTPP")
                sb.find_elements(GAPHomeLocators.enable_button)[0].click()
        else:
            if restore_flag:
                LOGGER.info("current status is disable AUTH, will do nothing ")
                sb.attach_allure_screenshot("current status is disable AUTH, will do nothing screenshot")
                return
            else:
                # enable auth
                LOGGER.info("current status is disable AUTH, enable it ")
                sb.find_elements(GAPHomeLocators.enable_button)[0].click()

        # apply
        self.wait_and_click(sb, GAPHomeLocators.apply_button)
        self.wait_and_click(sb, GAPHomeLocators.ok_button)
        self.wait_progress_bar(sb, wait_present_time=short_click_timeout, wait_not_visible_time=wait_render_timeout)
        sb.wait_for_element_clickable(GAPHomeLocators.ok_button, timeout=wait_render_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)

        # wait progress bar disappear
        self.wait_progress_bar(sb, wait_present_time=click_timeout, wait_not_visible_time=wait_render_timeout)
        sb.assert_text("Successfully restarted the services", timeout=assertTextTimeout)
        sb.attach_allure_screenshot("apply successfully screenshot")
        sb.sleep(20)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def Kafka_config(self, sb):
        self.into_components_menu(sb, sub_menu="Kafka")
        # wait input widgets clickable
        sb.wait_for_element_visible(GAPComponentsLocators.Kafka_title, timeout=click_timeout)
        sb.wait_for_element_clickable(GAPHomeLocators.input_parameter, timeout=wait_render_timeout)
        # input parameters
        elements = sb.find_elements(GAPHomeLocators.input_parameter)
        LOGGER.info("input elements number:" + str(len(elements)))
        for i in range(len(elements)):
            if i == 0:
                # Default query timeout (seconds)
                elements[i].clear()
                # elements[i].send_keys('100000') # TO DO, wait for fix
                elements[i].send_keys('5')
            if i == 1:
                # Default query timeout (seconds)
                elements[i].clear()
                elements[i].send_keys('10')

        # apply
        sb.click(GAPHomeLocators.apply_button, timeout=click_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)
        sb.sleep(2)
        sb.wait_for_element_clickable(GAPHomeLocators.ok_button, timeout=wait_render_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)

        # wait progress bar disappear
        self.wait_progress_bar(sb, wait_present_time=click_timeout, wait_not_visible_time=wait_render_timeout)
        sb.assert_text("Successfully restarted the services", timeout=assertTextTimeout)
        sb.attach_allure_screenshot("apply successfully screenshot")
        # wait RESTPP restart succeed
        LOGGER.info("sleep 30 to wait services restart")
        sb.sleep(30)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def SSL_config(self, sb):
        # if URL starts with https, skip this case
        if "https" in self.test_env:
            LOGGER.info("current host: " + self.test_env + ", Not need to enable SSL in GAP, skip this case. ")
            return

        self.into_components_menu(sb, sub_menu="Nginx")
        sb.wait_for_element_visible(GAPHomeLocators.nginx_icon, timeout=wait_render_timeout)
        sb.wait_for_element_visible(GAPHomeLocators.checkbox_status, timeout=wait_render_timeout)
        # open Enable SSO button
        elements = sb.find_elements(GAPHomeLocators.checkbox_status)
        LOGGER.info("elements: " + str(len(elements)))
        checkbox_status = sb.find_elements(GAPHomeLocators.checkbox_status)[0].get_attribute("aria-checked").lower()
        LOGGER.info("first checkbox_status: " + checkbox_status)
        if checkbox_status == "false":
            sb.find_elements(GAPHomeLocators.enable_button)[0].click()
        self.self_sign(sb)

        # apply
        sb.click(GAPHomeLocators.apply_button, timeout=click_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)
        sb.sleep(2)
        sb.wait_for_element_clickable(GAPHomeLocators.ok_button, timeout=wait_render_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)

        # wait progress bar disappear
        self.wait_progress_bar(sb, wait_present_time=click_timeout, wait_not_visible_time=wait_render_timeout)
        sb.assert_text("Successfully restarted the services", timeout=assertTextTimeout)
        sb.attach_allure_screenshot("apply successfully screenshot")
        # try:
        #     sb.driver.close() # can't close browser, since session id will lost
        # except Exception as e:
        #     LOGGER.info("close current browser")
        self.update_https_in_json_file(sb)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=60)
    def disable_SSL_config(self, sb):
        self.into_components_menu(sb, sub_menu="Nginx")
        sb.wait_for_element_visible(GAPHomeLocators.nginx_icon, timeout=wait_render_timeout)
        sb.wait_for_element_visible(GAPHomeLocators.checkbox_status, timeout=click_timeout)
        # close Enable SSO button
        elements = sb.find_elements(GAPHomeLocators.checkbox_status)
        LOGGER.info("elements: " + str(len(elements)))
        checkbox_status = sb.find_elements(GAPHomeLocators.checkbox_status)[0].get_attribute("aria-checked").lower()
        LOGGER.info("first checkbox_status: " + checkbox_status)
        if checkbox_status == "true":
            sb.find_elements(GAPHomeLocators.enable_button)[0].click()
        else:
            LOGGER.info("current status is disable SSL, return ")
            sb.attach_allure_screenshot("current status is disable SSL screenshot")
            self.update_https_in_json_file(sb, restore=True)
            return

        # apply
        sb.click(GAPHomeLocators.apply_button, timeout=click_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)
        sb.sleep(2)
        sb.wait_for_element_clickable(GAPHomeLocators.ok_button, timeout=wait_render_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)

        # wait progress bar disappear
        self.wait_progress_bar(sb, wait_present_time=click_timeout, wait_not_visible_time=wait_render_timeout)
        sb.assert_text("Successfully restarted the services", timeout=assertTextTimeout)
        sb.attach_allure_screenshot("apply successfully screenshot")
        self.update_https_in_json_file(sb, restore=True)


    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def update_https_in_json_file(self, sb, restore=False):
        # clear cookie and storage
        try:
            sb.delete_all_cookies()
            sb.clear_local_storage()
            LOGGER.info("clear local storage done ")
        except Exception as e:
            LOGGER.info("clear local storage exception: " + str(e))

        # update tools_test_data.json test_env
        if restore:
            LOGGER.info("restore to http, current " + self.test_env)
            if "https" in self.test_env:
                tmp_env = self.test_env.replace("https", "http")
                LOGGER.info("tmp_env: " + tmp_env)
                GPT_update_test_data(key="test_env", value=tmp_env, json_file="tools_test_data.json", )
            else:
                LOGGER.info("no need restore to http, current: " + self.test_env)
            self.GapLogin(sb)
            sb.attach_allure_screenshot("login with http successfully screenshot")
        else:
            if "https" not in self.test_env:
                tmp_env = self.test_env.replace("http", "https")
                LOGGER.info("tmp_env: " + tmp_env)
                GPT_update_test_data(key="test_env", value=tmp_env, json_file="tools_test_data.json", )
            else:
                LOGGER.info("no need update to https: " + self.test_env)
            self.GapLogin(sb)
            sb.attach_allure_screenshot("login with https successfully screenshot")


    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def LDAP_config(self, sb, is_group=False):
        self.into_security_menu(sb, sub_menu="LDAP")
        sb.wait_for_element_present(GAPHomeLocators.enable_button, timeout=wait_render_timeout)
        # open Enable LDAP button
        checkbox_status = sb.find_elements(GAPHomeLocators.checkbox_status)[0].get_attribute("aria-checked").lower()
        LOGGER.info("first checkbox_status: " + checkbox_status)
        if checkbox_status == "false":
            sb.find_elements(GAPHomeLocators.enable_button)[0].click()

        # input parameters
        elements = sb.find_elements(GAPHomeLocators.input_parameter)
        LOGGER.info("input number:" + str(len(elements)))
        for i in range(len(elements)):
            if i == 0:
                # hostname
                elements[i].clear()
                elements[i].send_keys('**************')
            if i == 1:
                # port
                elements[i].clear()
                elements[i].send_keys('389')
            elif i == 2:
                # base domain name
                elements[i].clear()
                elements[i].send_keys('dc=tigergraph,dc=com')
            elif i == 6:
                # username attribute
                elements[i].clear()
                elements[i].send_keys('uid')
            elif i == 7:
                # admin domain name
                elements[i].clear()
                elements[i].send_keys('cn=admin,dc=tigergraph,dc=com')
            elif i == 8:
                # admin psd
                elements[i].clear()
                elements[i].send_keys('admin')
        # select connection type: None
        sb.find_elements(GAPHomeLocators.select_button)[0].click()
        # select trust all button
        checkbox_status = sb.find_elements(GAPHomeLocators.checkbox_status)[1].get_attribute("aria-checked").lower()
        LOGGER.info("second checkbox_status: " + checkbox_status)
        if checkbox_status == "false":
            sb.find_elements(GAPHomeLocators.enable_button)[1].click()
        sb.attach_allure_screenshot("input parameter done screenshot")

        # apply
        sb.click(GAPHomeLocators.apply_button, timeout=click_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)
        sb.sleep(2)
        sb.wait_for_element_present(GAPHomeLocators.ok_button, timeout=wait_render_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)
        # wait progress bar disappear
        self.wait_progress_bar(sb, wait_present_time=click_timeout, wait_not_visible_time=wait_render_timeout)
        sb.assert_text("Successfully restarted the services", timeout=assertTextTimeout)
        sb.attach_allure_screenshot("apply successfully screenshot")
        sb.sleep(10)

        if is_group:
            LOGGER.info("prepare group account")
            # grant privilege to ldap user
            sb.wait_for_element_clickable(GAPHomeLocators.users, timeout=click_timeout)
            sb.click(GAPHomeLocators.users, timeout=click_timeout)
            self.createUser(sb, username="cn=group01,ou=finance,dc=tigergraph,dc=com", group_name="ldaptest_group", name_key="memberOf")
            self.grantRole(sb, username="ldaptest_group", role="globaldesigner", isglobal=True, graph="", group_name="true")

            # login by LDAP group and double check
            self.GapLogin(sb, login_way=ToolsBaseCase.LDAP_Group_Login)
            # check the login person
            self.intoUserManegement(sb)
            sb.assert_text("<EMAIL>", timeout=assertTextTimeout)
            sb.attach_allure_screenshot("LDAP group login successfully screenshot")
        else:

            # grant privilege to ldap user
            sb.wait_for_element_clickable(GAPHomeLocators.users, timeout=click_timeout)
            sb.click(GAPHomeLocators.users, timeout=click_timeout)
            self.createUser(sb, username="qatest", group_name="ldaptest", name_key="cn")
            self.grantRole(sb, username="ldaptest", role="globaldesigner", isglobal=True, graph="", group_name="true")

            # login by LDAP and double check
            self.GapLogin(sb, login_way=ToolsBaseCase.LDAP_Login)
            # check the login person
            self.intoUserManegement(sb)
            sb.assert_text("qatest", timeout=assertTextTimeout)
            sb.attach_allure_screenshot("LDAP login successfully screenshot")

    def disable_LDAP_config(self, sb):
        self.into_security_menu(sb, sub_menu="LDAP")
        sb.wait_for_element_present(GAPHomeLocators.enable_button, timeout=click_timeout)
        # open Enable LDAP button
        checkbox_status = sb.find_elements(GAPHomeLocators.checkbox_status)[0].get_attribute("aria-checked").lower()
        LOGGER.info("first checkbox_status: " + checkbox_status)
        if checkbox_status == "true":
            sb.find_elements(GAPHomeLocators.enable_button)[0].click()
        else:
            LOGGER.info("current status is disable LDAP, return ")
            sb.attach_allure_screenshot("current status is disable LDAP screenshot")
            return

        # apply
        sb.click(GAPHomeLocators.apply_button, timeout=click_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)
        sb.sleep(2)
        sb.wait_for_element_present(GAPHomeLocators.ok_button, timeout=wait_render_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)
        # wait progress bar disappear
        self.wait_progress_bar(sb, wait_present_time=click_timeout, wait_not_visible_time=wait_render_timeout)
        sb.assert_text("Successfully restarted the services", timeout=assertTextTimeout)
        sb.attach_allure_screenshot("apply successfully screenshot")

    def wait_progress_bar(self, sb, wait_present_time=30, wait_not_visible_time=30):
        try:
            sb.wait_for_element_present(GSTHomeLocators.circle_progressBar, timeout=wait_present_time)
            LOGGER.info("wait_for_element_present circle_progressBar Pass")
            sb.sleep(1)
            sb.wait_for_element_not_present(GSTHomeLocators.circle_progressBar, timeout=wait_not_visible_time)
            LOGGER.info("wait_for_element_not_visible circle_progressBar Pass")
        except Exception as e:
            LOGGER.info("wait_progress_bar exception: " + str(e))
            sb.attach_allure_screenshot("wait_progress_bar exception screenshot")

    def wait_progress_bar_when_add_secret(self, sb, wait_present_time=10, wait_not_visible_time=30):
        try:
            sb.wait_for_element_present(GSTHomeLocators.secret_circle_progressBar, timeout=wait_present_time)
            LOGGER.info("wait_for_element_present circle_progressBar Pass")
            sb.wait_for_element_not_present(GSTHomeLocators.secret_circle_progressBar, timeout=wait_not_visible_time)
            LOGGER.info("wait_for_element_not_present circle_progressBar Pass")
        except Exception as e:
            LOGGER.info("wait_progress_bar_when_add_secret exception: " + str(e))
            sb.attach_allure_screenshot("wait_progress_bar_when_add_secret exception screenshot")

    def SSO_OKTA_config(self, sb):
        if "************" not in self.test_env:
            pytest.skip("Skipping this test because the OKTA is fixed configration for ************")
        self.into_security_menu(sb, sub_menu="SSO")
        sb.wait_for_element_visible(GAPHomeLocators.enable_button, timeout=wait_render_timeout)
        # open Enable SSO button
        checkbox_status = sb.find_elements(GAPHomeLocators.checkbox_status)[0].get_attribute("aria-checked").lower()
        LOGGER.info("first checkbox_status: " + checkbox_status)
        if checkbox_status == "false":
            sb.find_elements(GAPHomeLocators.enable_button)[0].click()
        self.self_sign(sb)
        # input sso parameters
        elements = sb.find_elements(GAPHomeLocators.input_parameter)
        LOGGER.info("input number:" + str(len(elements)))
        for i in range(len(elements)):
            if i == 0:
                # hostname
                elements[i].clear()
                elements[i].send_keys(self.test_env)
            elif i == 1:
                # IDP
                elements[i].clear()
                elements[i].send_keys(self.IDP)
            elif i == 2:
                # SSO url
                elements[i].clear()
                elements[i].send_keys(self.SSOURL)
            elif i == 3:
                # admin psd
                elements[i].clear()
                elements[i].send_keys(self.CONTEXT)

        # input ID certificate
        sb.type(GAPHomeLocators.ID_certificate, self.IdentityCertificate)
        sb.attach_allure_screenshot("input parameter done screenshot")
        # apply
        sb.click(GAPHomeLocators.apply_button, timeout=click_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)
        need_restart_service = True
        version_tmp, is_three_version = self.get_on_prem_tg_version(sb)
        LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))
        if version_tmp and version_tmp.strip():
            if not is_three_version and int(version_tmp) >= 420:
                need_restart_service = False
                LOGGER.info("Current version >= 420, no need to restart services")
            else:
                LOGGER.info("Current version <420 , need to restart services")

        if need_restart_service:
            self.wait_progress_bar(sb, wait_present_time=click_timeout, wait_not_visible_time=click_timeout)
            sb.wait_for_element_visible(GAPHomeLocators.ok_button, timeout=wait_render_timeout)
            sb.wait_for_element_clickable(GAPHomeLocators.ok_button, timeout=wait_render_timeout)
            sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)
            # wait progress bar disappear
            self.wait_progress_bar(sb, wait_present_time=click_timeout, wait_not_visible_time=click_timeout)
            sb.assert_text("Successfully restarted the services", timeout=assertTextTimeout)
        else:
            # no need to restart service in tg_4.2.0
            LOGGER.info("Current version >= 420, no need to restart services")
        sb.attach_allure_screenshot("apply successfully screenshot")
        LOGGER.info("sleep 20s to restart services")
        sb.sleep(20)  # GSQL restart performances downgrade in tg_4.x, sleep 20s or will logout

        # grant privilege to OKTA user
        sb.click(GAPHomeLocators.users, timeout=click_timeout)
        self.createUser(sb, username="<EMAIL>", group_name="SSO_OKTA", name_key="nameid")
        self.grantRole(sb, username="SSO_OKTA", role="globaldesigner", isglobal=True, graph="", group_name="true")
        # config the OKTA website(skip this step because fixed configartion )
        # self.set_OKTA_SSO_config(sb)
        # login by SSO and double check
        self.GapLogin(sb, login_way=ToolsBaseCase.OKTA_Login, need_check_GAP=False)
        # config the OKTA website
        self.login_OKTA_SSO(sb)
        # check the login person
        self.intoUserManegement(sb)
        sb.assert_text("<EMAIL>", timeout=assertTextTimeout)
        sb.attach_allure_screenshot("SSO OKTA login successfully screenshot")

    def SSO_AZURE_config(self, sb):
        if "************" not in self.test_env:
            pytest.skip("Skipping this test because the AZURE is fixed configration for ************")

        if "https" not in self.test_env:
            LOGGER.info("current host: " + self.test_env)
            raise Exception("SSO Azure needs https, please open SSL in GAP firstly")

        self.into_security_menu(sb, sub_menu="SSO")
        sb.wait_for_element_present(GAPHomeLocators.enable_button, timeout=wait_render_timeout)
        # open Enable SSO button
        checkbox_status = sb.find_elements(GAPHomeLocators.checkbox_status)[0].get_attribute("aria-checked").lower()
        LOGGER.info("first checkbox_status: " + checkbox_status)
        if checkbox_status == "false":
            sb.find_elements(GAPHomeLocators.enable_button)[0].click()
        self.self_sign(sb)
        # input sso parameters
        elements = sb.find_elements(GAPHomeLocators.input_parameter)
        LOGGER.info("input number:" + str(len(elements)))
        for i in range(len(elements)):
            if i == 0:
                # hostname
                elements[i].clear()
                elements[i].send_keys(self.test_env)
            elif i == 1:
                # IDP
                elements[i].clear()
                elements[i].send_keys(self.AZURE_IDP)
            elif i == 2:
                # SSO url
                elements[i].clear()
                elements[i].send_keys(self.AZURE_SSOURL)
            elif i == 3:
                # admin psd
                elements[i].clear()
                elements[i].send_keys(self.AZURE_CONTEXT)

        # input ID certificate
        sb.type(GAPHomeLocators.ID_certificate, self.AZURE_IdentityCertificate)
        sb.attach_allure_screenshot("input parameter done screenshot")
        # apply
        sb.click(GAPHomeLocators.apply_button, timeout=click_timeout)
        sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)

        need_restart_service = True
        version_tmp, is_three_version = self.get_on_prem_tg_version(sb)
        LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))
        if version_tmp and version_tmp.strip():
            if not is_three_version and int(version_tmp) >= 420:
                need_restart_service = False
                LOGGER.info("Current version >= 420, no need to restart services")
            else:
                LOGGER.info("Current version <420 , need to restart services")

        if need_restart_service:
            self.wait_progress_bar(sb, wait_present_time=click_timeout, wait_not_visible_time=click_timeout)
            sb.wait_for_element_visible(GAPHomeLocators.ok_button, timeout=wait_render_timeout)
            sb.wait_for_element_clickable(GAPHomeLocators.ok_button, timeout=wait_render_timeout)
            sb.click(GAPHomeLocators.ok_button, timeout=click_timeout)
            # wait progress bar disappear
            self.wait_progress_bar(sb, wait_present_time=click_timeout, wait_not_visible_time=click_timeout)
            sb.assert_text("Successfully restarted the services", timeout=assertTextTimeout)
        else:
            # no need to restart service in tg_4.2.0
            LOGGER.info("Current version >= 420, no need to restart services")
        sb.attach_allure_screenshot("apply successfully screenshot")
        LOGGER.info("sleep 20s to restart services")
        sb.sleep(20) #GSQL restart performances downgrade in tg_4.x, sleep 20s or will logout

        # grant privilege to AZURE user
        sb.click(GAPHomeLocators.users, timeout=click_timeout)
        self.createUser(sb, username="<EMAIL>", group_name="SSO_AZURE", name_key="nameid")
        self.grantRole(sb, username="SSO_AZURE", role="globaldesigner", isglobal=True, graph="", group_name="true")
        # config the AZURE website(skip this step because fixed configration)
        # self.set_AZURE_SSO_config(sb)
        # open new tab to login by SSO and double check
        # sb.open_new_window()
        self.GapLogin(sb, login_way=ToolsBaseCase.AZURE_Login, need_check_GAP=False)
        self.login_AZURE_SSO(sb)
        # check the login person
        self.intoUserManegement(sb)
        sb.assert_text("<EMAIL>", timeout=assertTextTimeout)
        sb.attach_allure_screenshot("SSO AZURE login successfully screenshot")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def self_sign(self, sb):
        # self signed Service Provider's private key
        sb.click_nth_visible_element(GAPHomeLocators.upload_file_button, number=2, timeout=click_timeout)
        sb.click(GAPHomeLocators.self_signed_button, timeout=click_timeout)
        sb.wait_for_element_present(GAPHomeLocators.self_signed_windows, timeout=wait_render_timeout)
        # input params
        sb.wait_for_element_clickable(GAPHomeLocators.self_signed_input, timeout=wait_render_timeout)
        elements = sb.find_elements(GAPHomeLocators.self_signed_input)
        LOGGER.info("self_signed_input lenth:" + str(len(elements)))
        for i in range(len(elements)):
            elements[i].clear()
            elements[i].send_keys("test")
            sb.sleep(1)
        # apply
        sb.attach_allure_screenshot("input self signed params screenshot")
        sb.click_nth_visible_element(GAPHomeLocators.self_signed_apply, number=2, timeout=click_timeout)
        self.wait_progress_bar(sb, wait_present_time=click_timeout, wait_not_visible_time=wait_render_timeout)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def dashboard_overview(self, sb):
        self.assert_dependency_view(sb)
        sb.attach_allure_screenshot("assert Dependency view successfully screenshot")
        self.assert_table_view(sb)
        sb.attach_allure_screenshot("assert table view successfully screenshot")
        self.reset_services_button(sb)
        sb.attach_allure_screenshot("reset services successfully screenshot")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def assert_dependency_view(self, sb):
        self.into_dashboard_menu(sb, index=0)
        # assert Dependency view tab
        sb.wait_for_element_present(GAPHomeLocators.online_tag, timeout=wait_render_timeout)
        sb.assert_text('Service Monitor', timeout=assertTextTimeout)
        sb.assert_text('Resource Monitor', timeout=assertTextTimeout)
        sb.attach_allure_screenshot("assert Dependency view screenshot")
        elements = sb.find_elements(GAPHomeLocators.online_tag)
        LOGGER.info("Dependency view online number:" + str(len(elements)))
        # one node has 17 services online at least, otherwise the status is wrong
        if len(elements) < 17:
            sb.attach_allure_screenshot("Dependency view assert online number <17 screenshot")
            raise Exception("Dependency view online status number wrong, <17")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def assert_table_view(self, sb):
        self.into_dashboard_menu(sb, index=0)
        # assert Table view
        sb.click(GAPHomeLocators.table_view)
        sb.wait_for_element_present(GAPHomeLocators.online_tag, timeout=wait_render_timeout)
        elements = sb.find_elements(GAPHomeLocators.online_tag)
        LOGGER.info("Table view online number:" + str(len(elements)))
        if len(elements) < 17:
            sb.attach_allure_screenshot("Table view assert online number screenshot")
            raise Exception("Table view online status number wrong")

    # no need to retry by @decorators.retry_on_exception, just let it fail and rerun the case
    def reset_services_button(self, sb):
        self.into_dashboard_menu(sb, index=0)
        # check start all button
        sb.click(GAPHomeLocators.start_all, timeout=click_timeout)
        sb.assert_text("Successfully started services", timeout=assertTextTimeout)
        sb.attach_allure_screenshot("Successfully started services screenshot")

        # check restart all button
        sb.sleep(5)  # improve success
        sb.click(GAPHomeLocators.restart_all, timeout=click_timeout)
        sb.click(GAPHomeLocators.confirm_button, timeout=click_timeout)
        sb.assert_text("Successfully restarted services", timeout=assertTextTimeout)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def dashboard_historic_cluster_monitoring(self, sb):
        self.into_dashboard_menu(sb, index=1)
        # skip V3.9.3 assert
        # check the database version, switch use different query roles
        loginUtil = LoginUtils(self)
        cloud_env_flag = loginUtil.is_oncloud()
        if not cloud_env_flag:
            version_tmp, is_three_version = self.get_on_prem_tg_version(sb)
            LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))
            if version_tmp and version_tmp.strip():
                if int(version_tmp) == 393:
                    LOGGER.info("Skipping this test case assert because random issue in V3.9.3")
                    pytest.skip("Skipping this test case assert because random issue in V3.9.3")

        # assert Cluster Monitoring
        sb.wait_for_element_present(GAPHomeLocators.m1_tag, timeout=wait_render_timeout)
        sb.assert_text('Cluster Monitoring', timeout=assertTextTimeout)
        sb.attach_allure_screenshot("Historic Cluster Monitoring screenshot")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def gsql_output(self, sb):
        self.upload_gsql_file_in_Gshell(sb)
        loginUtil = LoginUtils(self)
        if loginUtil.is_oncloud():
            version_tmp, is_three_version = self.get_cloud_tg_version(sb)
            LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))
            if version_tmp and version_tmp.strip():
                if int(version_tmp) == 3111:
                    # skip since APPS-3500
                    LOGGER.info("Skipping this test because APPS-3500")
                    # pytest.skip("Skipping this test because APPS-3500")
            clusterToolsPage = ClusterToolsPage(sb)
            clusterToolsPage.back_to_cluster_homepage()
            tool_name = CloudClusterAccessToolsLocators.cloud_tools_url[ToolsBaseCase.AdminPortal_URL]
            clusterToolsPage.access_cluster_tool(loginUtil.cloud_cluster, tool_name, loginUtil.cloud_platform, "false")
        else:
            self.GapLogin(sb)
        self.download_query_output_file(sb)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def upload_gsql_file_in_Gshell(self, sb):
        # set chrome download path One time
        download_path = "/tmp/GAP"
        self.setChromeDownloadPath(sb, download_path)
        uploadgsqlfile = os.path.join(os.getcwd(), "data", "gapdata", "query_for_output.gsql")
        LOGGER.info("upload gsql file:" + uploadgsqlfile)
        gshellPage = GshellPage(sb)
        gshellPage.login_gshell()
        gshellPage.run_gsqlfile_verifyText(uploadgsqlfile, '"error": false,', 300)
        sb.attach_allure_screenshot("run query result screenshot")

    def wait_and_click(self, sb, css, timeout=click_timeout ):
        LOGGER.info("wait and click: " + css)
        sb.wait_for_element_clickable(css, timeout=timeout)
        sb.click(css, timeout=timeout)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def download_query_output_file(self, sb):
        self.intoGsqlOutput(sb)
        download_path = "/tmp/GAP"
        sb.type(GAPOutputLocators.filepath, "/home/<USER>/tigergraph/data/gsql_output/query_output.csv")
        # init the env
        self.delete_files_under_chrome_download_path(path=download_path, fileName="query_output", contain_file_name=True)
        self.wait_and_click(sb, GAPOutputLocators.preview)
        sb.assert_text("query_output_test", timeout=assertTextTimeout)
        self.wait_and_click(sb, GAPOutputLocators.download)
        sb.attach_allure_screenshot("after click download button result screenshot")

        # Check download file and file size
        download_res, download_file = self.judge_file_exist(10, download_path, "query_output", "zipOrgz")
        if download_res:
            file_size = os.path.getsize("{}/{}".format(download_path, download_file))
            sb.assert_true(file_size > 10)
            sb.attach_allure_screenshot("Download output file success screenshot")
        else:
            sb.attach_allure_screenshot("Download output file failed screenshot")
            sb.fail(msg="Download file fail")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def gui_config(self, sb):
        self.into_components_menu(sb, sub_menu="GUI")
        sb.wait_for_element_clickable(GAPHomeLocators.input_widgets, timeout=click_timeout)
        sb.find_elements(GAPHomeLocators.input_widgets)[0].clear()
        sb.find_elements(GAPHomeLocators.input_widgets)[0].send_keys("123")

    def getText_if_element_present(self,sb,selector):
        if(sb.assert_element_present(selector, timeout=wait_render_timeout)):
            res = sb.get_text_content(selector)
            LOGGER.info(f"return result: {res}")
            return res

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def manual_replace_tigergraph_suite(self, sb, file_path):
        """
        Manual upload/Downgrade tools package and downgrade
        """
        self.intoUpgradeTGSuite(sb)  
        self.upload_tools_package(sb, file_path)
        # wait for uploading package end
        sb.wait_for_element_present(UpgradeToolsLocators.current_ver_p, timeout=uploading_timeout)
        sb.assert_element(UpgradeToolsLocators.old_ver_p)
        sb.attach_allure_screenshot("Manual downgrade window")
        # click "Downgrade" button
        sb.click(UpgradeToolsLocators.downgrade_btn)

    def manual_upgrade_invalid_package(self,sb, file_path):
        """
        Upload invalid format/unsigned package
        """
        # Access upgrade page (Adminportal -> Management -> Upgade TigerGraph Suite)
        self.intoUpgradeTGSuite(sb)
        # Upload a invalid package
        self.upload_tools_package(sb, file_path)
        # wait for error message present
        try:
            sb.wait_for_element_present(UpgradeToolsLocators.invalid_package_error_p, timeout=click_timeout)
            sb.attach_allure_screenshot("InvalidPackageErrorMessage")
        except Exception as e:
            sb.wait_for_element_present(UpgradeToolsLocators.sign_missing_error_p, timeout=assertTextTimeout)
            sb.attach_allure_screenshot("SignMissingErrorMessage")
        sb.click(UpgradeToolsLocators.close_error_window)
        sb.wait_for_element_present(UpgradeToolsLocators.upgrade_tools_span, timeout=assertTextTimeout)

    def manual_upgrade_valid_package(self,sb, file_path):
        """
        Upload valid format/unsigned package
        """
        # Access upgrade page (Adminportal -> Management -> Upgade TigerGraph Suite)
        self.intoUpgradeTGSuite(sb)
        # Upload a valid package
        self.upload_tools_package(sb, file_path)
        # wait for uploading message present
        sb.wait_for_element_present(UpgradeToolsLocators.uploading_package_p, timeout=wait_render_timeout)
        sb.attach_allure_screenshot("UploadingToolsPackage")
        sb.wait_for_element_present(UpgradeToolsLocators.proceed_package_p, timeout=uploading_timeout)
        sb.attach_allure_screenshot("ProceedToolsPackage")

        if sb.is_element_present(UpgradeToolsLocators.downgrade_btn):
            # Will cancel the upgrade if the valid opration is downgrade, which means the tools package already updated.
            LOGGER.info(f"Will cancel the upgrade if the valid opration is downgrade, which means the tools package already updated.")
            return True
        sb.wait_for_element_present(UpgradeToolsLocators.upgrade_btn, timeout=assertTextTimeout)
        if sb.is_element_present(UpgradeToolsLocators.upgrade_btn):    
            sb.click(UpgradeToolsLocators.upgrade_btn)
        sb.attach_allure_screenshot("UpgradingToolsPackage")
        sb.wait_for_element_present(UpgradeToolsLocators.upgrade_succeed_p, timeout=assertTextTimeout)
        LOGGER.info(f"The Tools Upgrade Successfully")
        sb.attach_allure_screenshot("FinishUpgradeToolsPackage")
        #wait 5s to become active
        sb.sleep(5)

    def upload_tools_package(self, sb, file_path):
        """
        Upload tools package
        """
        # click "Manual upgrade" button to upload tools package 
        LOGGER.info(f"Start to uplad file form {file_path}")
        sb.upload_file("xpath", UpgradeToolsLocators.manual_upgrade_input, file_path)  
        sb.attach_allure_screenshot("UploadFiles")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def download_query_output_large_file(self, sb, file_path):
        """Download large query output file"""
        if "************" not in self.test_env:
            pytest.skip("Skipping test - large file is only set up for environment ************")
        version_tmp, is_three_version = self.get_on_prem_tg_version(sb)
        LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))
        if version_tmp and version_tmp.strip():
            if not is_three_version and int(version_tmp) >= 420:
                LOGGER.info("Current version is 420 or higher, file download is supported")
            else:
                LOGGER.info("Current version is below 420, files larger than 2GB cannot be downloaded")
                pytest.skip("Skipping this test because the OKTA is fixed configration for ************")
        try:
            # Set download path
            download_path = "/tmp/download_test"
            LOGGER.info(f"Setting up download path: {download_path}")
            
            # Ensure directory exists

            if not os.path.exists(download_path):
                os.makedirs(download_path, exist_ok=True)
                os.chmod(download_path, 0o777)
            
            # Set Chrome download path
            self.setChromeDownloadPath(sb, download_path)
            
            # Get file name and set file path in input
            file_name = os.path.basename(file_path)
            LOGGER.info(f"Target file name: {file_name}")
            
            sb.type(GAPOutputLocators.filepath, file_path)  #
            # Clean up any existing old files
            self.delete_files_under_chrome_download_path(path=download_path, fileName=file_name)
            
            # Click preview and download buttons
            self.wait_and_click(sb, GAPOutputLocators.preview)    
            self.wait_and_click(sb, GAPOutputLocators.download)
            sb.attach_allure_screenshot("after click download button result screenshot")
            
            # Check file download
            LOGGER.info(f"Checking file download in path: {download_path}")
            if not self.judge_large_file_exist(200, download_path, file_name, sleep_sec=60):
                raise Exception(f"Failed to download file: {file_name}")
                
            LOGGER.info(f"Successfully downloaded file to {download_path}/{file_name}")
            return True
            
        except Exception as e:
            LOGGER.error(f"Error downloading large file: {str(e)}")
            raise e

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=10)
    def judge_large_file_exist(self, check_times, check_path, file_name, sleep_sec=6):
        """
        Check if large file exists (specifically for checking offline packages and other large files)
        Args:
            check_times: Number of times to check
            check_path: Path to check
            file_name: Complete file name (including extension)
            sleep_sec: Sleep interval between checks
        Returns:
            bool: Whether the file exists
        """
        if os.path.exists(check_path) is False:
            raise Exception("path error...")
        if str.isdigit(str(check_times)) is False:
            raise Exception("param error...")
        
        LOGGER.info("check_path= " + check_path)
        
        for number in range(1, int(check_times)):
            LOGGER.info("check file time: {}".format(str(number)))
            LOGGER.info("check file_name: {}".format(file_name))
            
            try:
                files = os.listdir(check_path)
                LOGGER.info("read files in path: " + str(files))
                
                if file_name in files or f'{file_name}.tar.gz' in files:
                    LOGGER.info(f"Found target file: {file_name}")
                    return True
                    
                time.sleep(sleep_sec)
                
            except Exception as e:
                LOGGER.error(f"Error checking file: {str(e)}")
                
        return False

    def check_all_services_status(self, result):
        """
        Check status of all TigerGraph services
        Args:
            result: Output from gadmin status command
        Returns:
            bool: True if all services are normal, False otherwise
        """
        for line in result.split('\n'):
            if '|' not in line or 'Service Name' in line or '+--' in line:
                continue
                
            columns = [col.strip() for col in line.split('|')]
            if len(columns) >= 3:
                service_name = columns[1].strip()
                service_status = columns[2].strip()
                process_state = columns[3].strip()
                
                if service_status != 'Online' or process_state != 'Running':
                    LOGGER.error(f"Service {service_name} status abnormal: Status={service_status}, State={process_state}")
                    return False
        return True

    def setup_download_path(self, sb, base_path="/tmp/download_test"):
        """
        Setup and verify download path for Chrome
        Args:
            sb: Selenium Base object
            base_path: Base download path
        Returns:
            str: Verified download path
        """
        try:
            LOGGER.info(f"Setting up download path: {base_path}")
            if not os.path.exists(base_path):
                LOGGER.info(f"Creating download directory: {base_path}")
                os.makedirs(base_path, exist_ok=True)
                os.chmod(base_path, 0o777)
            
            # Verify path exists and is writable
            if not os.path.exists(base_path):
                raise Exception(f"Failed to create download path: {base_path}")
            if not os.access(base_path, os.W_OK):
                raise Exception(f"Download path not writable: {base_path}")
                
            # Set Chrome download path
            self.setChromeDownloadPath(sb, base_path)
            return base_path
            
        except Exception as e:
            LOGGER.error(f"Error setting up download path: {str(e)}")
            raise e

    
