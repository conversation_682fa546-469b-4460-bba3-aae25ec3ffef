import logging
import time
import allure
from base.tools_basepage import ToolsBasePage
from locators.tools.graph_studio_locators import (
    DialogLocators,
    GSTHomeLocators,
    GSTLoadDataLocators,
    GSTActionsLocators
)
from common import tools_settings

LOGGER = logging.getLogger(__name__)


class GSTLoadDataPage(ToolsBasePage):
    """Graph Studio Load Data Page objects"""

    @allure.step("Navigate to Load Data")
    def navigate_to_load_data(self):
        LOGGER.info("Navigate to Load Data page.")
        self.sb.click(
            GSTLoadDataLocators.nav_load_data,
            timeout=tools_settings.CLICK_TIME_OUT,
        )
        self.sb.assert_element_visible(
            GSTLoadDataLocators.start_resume_load_btn, timeout=60
        )

    def load_all_data(self, graph_name="", load_timeout=300):
        """Load all data"""
        LOGGER.info("load data page: graph_name= " + graph_name + ", timeout= " + str(load_timeout))
        self.sb.wait_for_element(GSTHomeLocators.nav_load_data, timeout=60)
        self.sb.click(GSTHomeLocators.nav_load_data, timeout=60)
        LOGGER.info(f"Turned to load data")
        self.wait_progress_bar(10, 60)
        LOGGER.info(f"save the schema first before loading")
        self.sb.wait_for_element_clickable(GSTLoadDataLocators.save_schema, timeout=120)
        self.sb.click(GSTLoadDataLocators.save_schema, timeout=60)
        self.sb.wait_for_element_clickable(GSTLoadDataLocators.start_resume_load_btn, timeout=120)
        self.sb.click(GSTLoadDataLocators.start_resume_load_btn, timeout=60)
        self.sb.wait_for_element_clickable(DialogLocators.continue_btn, timeout=60)
        self.sb.click(DialogLocators.continue_btn, timeout=60)
        self.wait_progress_bar(30,300)
        self.sb.assert_true(
            self.wait_for_loading(graph_name=graph_name, timeout=load_timeout),
            f"Data load failed to complete within expected time {load_timeout}",
        )
        self.sb.wait_for_ready_state_complete()
        self.sb.attach_allure_screenshot("Data loading finished screenshot")

    def switch_to_graph(self, graph_name):
        """switch to a graph"""
        self.sb.wait_for_element(GSTHomeLocators.current_graph, timeout=60)
        if (
            self.sb.find_element(GSTHomeLocators.current_graph).text.strip()
            == graph_name
        ):
            return
        self.sb.slow_click(GSTHomeLocators.graph_menu_btn)
        self.sb.attach_allure_screenshot("graph list screenshot")
        self.sb.slow_click(
            "//span[contains(@class,'graph-name-in-list') and normalize-space(text())='"
            + graph_name
            + "']",
            timeout=20,
        )

    '''
        before check statistics number, should rebuild force
    '''
    def force_rebuild(self, graph_name="", wait_present_time=30, wait_not_visible_time=300):
        try:
            # before switch, save schema
            LOGGER.info(f"save the schema first, before force re_build")
            self.sb.wait_for_element_clickable(GSTLoadDataLocators.save_schema, timeout=120)
            self.sb.click(GSTLoadDataLocators.save_schema, timeout=60)
            LOGGER.info("force_rebuild graph: " + graph_name)
            self.switch_to_graph("Global View")
            self.sb.wait_for_element_clickable(GSTHomeLocators.nav_actions, timeout=60)
            self.sb.click(GSTHomeLocators.nav_actions, timeout=20)
            # click force rebuild
            self.sb.wait_for_element_clickable(GSTActionsLocators.force_option, timeout=20)
            self.sb.click(GSTActionsLocators.force_option, timeout=20)
            self.sb.click(GSTActionsLocators.rebuild_btn, timeout=20)

            self.sb.wait_for_element_present(GSTHomeLocators.progressBar, timeout=wait_present_time)
            LOGGER.info("wait_horizontal_progress_bar present PASS")
            self.sb.sleep(1)
            self.sb.wait_for_element_not_present(GSTHomeLocators.progressBar, timeout=wait_not_visible_time)
            LOGGER.info("wait_horizontal_progress_bar not present PASS")
            self.sb.assert_text("RebuildNow finished", timeout=120)
            self.sb.attach_allure_screenshot("force rebuild successfully screenshot")
        except Exception as e:
            LOGGER.info("force rebuild exception: " + str(e))
            self.sb.attach_allure_screenshot("force rebuild exception screenshot")
            if self.sb.is_element_visible(GSTActionsLocators.ok_btn_in_popup):
                LOGGER.info("click OK button in pop window")
                self.sb.click(GSTActionsLocators.ok_btn_in_popup)
        finally:
            self.switch_to_graph(graph_name)
            self.sb.click(GSTHomeLocators.nav_load_data)
            self.wait_progress_bar()
            self.sb.wait_for_text_visible('Vertex "', timeout=60)
            self.sb.sleep(10)


    def wait_progress_bar(self, wait_present_time=30, wait_not_visible_time=30):
        try:
            self.sb.wait_for_element_present(GSTHomeLocators.circle_progressBar, timeout=wait_present_time)
            LOGGER.info("wait_circle_progress_bar present PASS")
            self.sb.sleep(1)
            self.sb.wait_for_element_not_visible(GSTHomeLocators.circle_progressBar, timeout=wait_not_visible_time)
            LOGGER.info("wait_circle_progress_bar not_visible PASS")
        except Exception as e:
            LOGGER.info("wait_progress_bar exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_progress_bar exception screenshot")

    def wait_for_pause_button_clickable(self):
        clickable_flag = False
        for i in range(3):
            try:
                LOGGER.info("wait_for_pause_button_clickable {} time ".format(str(i+1)))
                self.sb.wait_for_element_clickable(GSTLoadDataLocators.pause_load_btn, timeout=300)
                self.sb.attach_allure_screenshot("Pause button clickable screenshot")
                clickable_flag = True
                break
            except Exception as e:
                self.sb.attach_allure_screenshot("Wait pause button clickable exception")
                LOGGER.info("wait_for_pause_button_clickable exception: " + str(e))
                self.sb.click(GSTHomeLocators.nav_load_data)

        if not clickable_flag:
            self.sb.attach_allure_screenshot("Wait pause button clickable 3 times exception")
            raise Exception("wait_for_pause_button_clickable 3 times failed ")

    def wait_for_loading(self, graph_name="", timeout=7200):
        """wait for the loading process finish by checking the pause button"""
        LOGGER.debug("wait_for_loading graph_name:{} , timeout:{}".format(graph_name, str(timeout)))
        self.wait_for_pause_button_clickable()
        LOGGER.debug(f"Pause button clickable")
        start_ms = time.time() * 1000.0
        stop_ms = start_ms + (float(timeout) * 1000.0)
        finished = False
        while time.time() * 1000.0 <= stop_ms:
            pause_button_clickable = self.sb.is_element_clickable(GSTLoadDataLocators.pause_load_btn)
            stop_button_clickable = self.sb.is_element_clickable(GSTLoadDataLocators.stop_load_btn)
            start_button_clickable = self.sb.is_element_clickable(GSTLoadDataLocators.start_resume_load_btn)
            LOGGER.info("load button Status: pause:{},stop:{},start:{}".format(str(pause_button_clickable), str(stop_button_clickable), str(start_button_clickable)))
            if not pause_button_clickable and not stop_button_clickable and start_button_clickable:
                LOGGER.debug(f"Pause and stop button not clickable, start button clickable")
                self.sb.attach_allure_screenshot("Pause stop button not clickable,start button clickable screenshot")

                # double check loading finished
                self.sb.click(GSTLoadDataLocators.show_left_chart_visual_info)
                if self.sb.is_text_visible("Status: RUNNING"):
                    LOGGER.info("still has job Status: RUNNING")
                elif self.sb.is_text_visible("Status: PAUSED"):
                    LOGGER.info("still has job Status: PAUSED")
                elif self.sb.is_text_visible("Status: STOPPED"):
                    LOGGER.info("still has job Status: STOPPED")
                elif self.sb.is_text_visible("Status: NOT STARTED"):
                    LOGGER.info("still has job Status: NOT STARTED")
                elif self.sb.is_text_visible("Status: Data not mapped"):
                    LOGGER.info("still has job Status: Data not mapped")
                else:
                    LOGGER.info("Statistics update OK")
                    if self.sb.is_element_visible(GSTLoadDataLocators.close_left_chart_visual_info):
                        LOGGER.info("Statistics update OK, close_left_chart_visual_info ")
                        self.sb.click(GSTLoadDataLocators.close_left_chart_visual_info)
                    finished = True
                    break
                if self.sb.is_element_visible(GSTLoadDataLocators.close_left_chart_visual_info):
                    LOGGER.info("Statistics not finish update, close_left_chart_visual_info ")
                    self.sb.click(GSTLoadDataLocators.close_left_chart_visual_info)
            self.sb.sleep(tools_settings.LOAD_CHECK_DELAY)
            self.sb.click(GSTHomeLocators.nav_load_data)
            self.sb.sleep(15)
        LOGGER.info("last step to force build once")
        self.force_rebuild(graph_name=graph_name)
        return finished


    def get_statistics(self):
        """get the statistics and print to log"""
        if self.sb.is_element_visible(GSTLoadDataLocators.close_left_chart_visual_info):
            LOGGER.info("close_left_chart_visual_info ")
            self.sb.click(GSTLoadDataLocators.close_left_chart_visual_info)

        self.sb.wait_for_element_visible(
            GSTLoadDataLocators.statistics_rows, timeout=60
        )
        cell_texts = self.sb.find_visible_elements("//mat-table")[0].text
        LOGGER.info(f"Data load Statistics:\n{cell_texts}")
        self.sb.attach_allure_screenshot("GraphStatistics")
        statistics = dict(x.rsplit(" ", 1) for x in cell_texts.split("\n"))
        return statistics
