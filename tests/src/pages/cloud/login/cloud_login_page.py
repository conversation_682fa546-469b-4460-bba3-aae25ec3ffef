"""
Access or manipulate elements of the TGCloud Login page
"""
import allure
import logging
from locators.cloud.cloud_locators import CloudClusterLocators, LogInLocators, CloudClusterAccessToolsLocators,LogOutLocators
from base.cloud_basepage import CloudBasePage


LOGGER = logging.getLogger(__name__)


class CloudLoginPage(CloudBasePage):
    @allure.step("Opening login page")
    def open_login_page(self, tgc_env):
        self.sb.open(tgc_env)

    @allure.step("Login with username/password succssfully")
    def login(self, org, user, password):
        # Click "Login with organization" button
        self.sb.click_if_visible(LogInLocators.login_with_org_btn, timeout=90)
        self.sb.wait_for_element_present(LogInLocators.org_title_h1, timeout=20)
        self.sb.type(LogInLocators.org_input, org)
        self.sb.click(LogInLocators.continue_btn)
        self.sb.type(LogInLocators.user_input, user)
        self.sb.type(LogInLocators.password_input, password)
        self.sb.click(LogInLocators.continue_login_btn)
        self.sb.wait_for_element_visible(CloudClusterLocators.clusters_btn, timeout=60)
        self.sb.attach_allure_screenshot(f"Login to {org} with user {user} successfully!")


    @allure.step("Logout org succssfully")
    def logout(self):
        self.sb.wait_for_element_clickable(LogOutLocators.account_icon, timeout=30)
        self.sb.click(LogOutLocators.account_icon)
        self.sb.click(LogOutLocators.logout_span)
        self.sb.wait_for_element_visible(LogInLocators.org_login_btn, timeout=60)
        self.sb.attach_allure_screenshot(f"Logout successfully!")

    
    def switch_tools_in_header(self, tool):
        LOGGER.info(f"Switch to {tool}")
        self.sb.wait_for_element_present(CloudClusterAccessToolsLocators.header_app_icon, timeout=90)
        # click "tools" btn in header and select one tools
        self.sb.click(CloudClusterAccessToolsLocators.header_app_icon)
        self.sb.attach_allure_screenshot("ToolsSelector")
        self.sb.click(CloudClusterAccessToolsLocators.header_tool_icon.format(
            CloudClusterAccessToolsLocators.tools_icon_in_header[tool]
        ))
        self.sb.sleep(10)
        self.sb.refresh()