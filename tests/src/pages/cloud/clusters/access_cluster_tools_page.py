"""
Access or manipulate elements of the TGCloud Cluster Tools page
"""

import random
import allure
import logging
from locators.cloud.cloud_locators import CloudClusterLocators
from locators.cloud.cloud_locators import CloudClusterAccessToolsLocators
from base.cloud_basepage import CloudBasePage
from pages.cloud.clusters.clusters_page import ClustersPage
from seleniumbase import decorators
from selenium.common.exceptions import NoSuchElementException


LOGGER = logging.getLogger(__name__)

click_time_out = 60

class ClusterToolsPage(CloudBasePage):
    @decorators.print_runtime(description="Access Cluster Tools")
    @allure.step("Access Cluster Tools")
    def access_cluster_tool(
        self,
        cluster_name,
        tool_name,
        platform,
        is_mlwb
    ):
        # clusters page
        self.sb.click(CloudClusterLocators.clusters_btn, timeout=30)
        LOGGER.info(f"Try to access tool `{tool_name}` of cluster `{cluster_name}`")

        # click Tools button
        continue_to_validate = self.click_cluster_tool_btn(cluster_name, tool_name, platform, is_mlwb)

        # verify the accessed tools as expected       
        if continue_to_validate:
            self.sb.sleep(10)
            self.validate_cluster_tool_accessed(cluster_name, tool_name)
        else:
            LOGGER.info(f"Skip to validate for access tool `{tool_name}` of cluster `{cluster_name}` as expectation")
    @decorators.print_runtime(description="Access Cluster tabs")
    @allure.step("Access Cluster tabs")
    def access_cluster_tabs(self,cluster_name, tab_name):
        # clusters page
        self.sb.click(CloudClusterLocators.clusters_btn, timeout=30)
        LOGGER.info(f"Try to access tabs `{tab_name}` of cluster `{cluster_name}`")

        # click Tools button
        continue_to_validate = self.click_cluster_tool_btn(cluster_name, tool_name, platform, is_mlwb)

        # verify the accessed tools as expected
        if continue_to_validate:
            self.sb.sleep(10)
            self.validate_cluster_tool_accessed(cluster_name, tool_name)
        else:
            LOGGER.info(f"Skip to validate for access tool `{tool_name}` of cluster `{cluster_name}` as expectation")

    
    # assert the specified tool can be accessed
    def assert_tool_card_div_accessable(self, tool_name, timeout = 60):
        LOGGER.info(f"Expected `{tool_name}` can be clicked and accessed`")
        # assert the target tool can't be accessed
        assert_tool_div = self.get_accessable_tool_card_div_for(tool_name)

        self.sb.assert_element_present(assert_tool_div, timeout = timeout)
        
    # assert the specified tool can't be accessed
    def assert_tool_card_div_inaccessible(self, tool_name, timeout = 5):
        LOGGER.info(f"Expected `{tool_name}` can't be clicked and accessed`")
        # assert the target tool can't be accessed
        assert_tool_div = self.get_accessable_tool_card_div_for(tool_name)
        self.sb.assert_element_absent(assert_tool_div, timeout = timeout)

    # get the class attribute of gst tool card div
    def get_tool_card_div_accessable_class_attr(self):
        return self.sb.get_attribute("//section/ancestor:: div[@role='tabpanel']/div/div/div[1]", "class")

    # get the div for the specified tool
    def get_accessable_tool_card_div_for(self, tool_name):
        # get the class value for div which related to gst
        class_for_accessable = self.get_tool_card_div_accessable_class_attr()
        return CloudClusterAccessToolsLocators.accessable_tools_card_div_selector_for.format(
            class_for_accessable, CloudClusterAccessToolsLocators.match_pattern_tools[tool_name])


    def wait_and_click(self, css):
        LOGGER.info("wait and click: " + css)
        self.sb.wait_for_element_clickable(css, timeout=click_time_out)
        self.sb.click(css, timeout=click_time_out)


    def wait_progress_bar(self, wait_present_time=30, wait_not_visible_time=30):
        try:
            self.sb.wait_for_element_present(CloudClusterLocators.circle_progressbar, timeout=wait_present_time)
            LOGGER.info("wait_circle_progress_bar present PASS")
            self.sb.sleep(1)
            self.sb.wait_for_element_not_visible(CloudClusterLocators.circle_progressbar, timeout=wait_not_visible_time)
            LOGGER.info("wait_circle_progress_bar not_visible PASS")
        except Exception as e:
            LOGGER.info("wait_progress_bar exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_progress_bar exception screenshot")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def click_cluster_access_managerment_btn(self, cluster_name):
        # wait the loading complete
        self.sb.wait_for_element_absent(CloudClusterLocators.cluster_loader_div, timeout=360) 
        # click the cluster name
        self.wait_and_click(CloudClusterLocators.cluster_name_link.format(cluster_name))
        self.wait_and_click(CloudClusterLocators.access_management_btn)
        user_list =["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        add_list =[]
        for i in range(len(user_list)):
            try:
                if self.sb.assert_text(user_list[i]):
                    LOGGER.info(user_list[i] + " user existed")
                    continue
            except Exception as e:
                LOGGER.info("add user " + user_list[i])
                add_list.append(user_list[i])
        self.sb.attach_allure_screenshot("check user existed done")
        LOGGER.info("prepare to add user: " + str(add_list))
        if len(add_list) > 0:
            self.wait_and_click(CloudClusterLocators.common_button.format("Add Users"))
            self.wait_progress_bar(wait_present_time=10)
            for i in range(len(add_list)):
                self.sb.find_elements(CloudClusterLocators.input_user_name)[1].send_keys(add_list[i])
                self.wait_and_click(CloudClusterLocators.select_user_name)
            self.sb.find_elements(CloudClusterLocators.common_button.format("Add Users"))[1].click()
            self.wait_progress_bar(wait_present_time=10)
        else:
            LOGGER.info("no need to add user")
        self.sb.attach_allure_screenshot("user is added done")


    @decorators.retry_on_exception(tries=10, delay=1, backoff=2, max_delay=32)
    def click_cluster_tool_btn(self, cluster_name, tool_name, platform, is_mlwb):
        # refersh the page
        #self.sb.refresh()
        # wait the loading complete
        self.sb.wait_for_element_absent(CloudClusterLocators.cluster_loader_div, timeout=360)

        # click the Tools button related to the cluster
        self.sb.slow_click(self.get_cluster_tools_btn(cluster_name), timeout=30)

        # gsql shell is not supported on azure
        if str.lower(platform) == 'azure' and tool_name == CloudClusterAccessToolsLocators.gsql:
            self.assert_azure_not_support_gsql_shell(cluster_name)
            # screen shot
            self.sb.attach_allure_screenshot("AzureNotSupportGSQLShell")
            return False
        elif str.lower(is_mlwb) == 'false' and tool_name == CloudClusterAccessToolsLocators.mlwb:
            self.cluster_not_enable_mlwb(cluster_name)
            # screen shot
            self.sb.attach_allure_screenshot("NotEnableMLWB")
            return False
        else:
            # click the specified tool div
            self.sb.slow_click(
                CloudClusterAccessToolsLocators.tool_div_selector_for.format(tool_name))
            return True
    
    # assert gsql shell not supported on azure
    def assert_azure_not_support_gsql_shell(self, cluster_name):
        tool_name = CloudClusterAccessToolsLocators.gsql
        LOGGER.info(f"Expected `{tool_name}` is not supported for cluster `{cluster_name}` on `azure`")
        self.sb.assert_element_absent(CloudClusterAccessToolsLocators.tool_div_selector_for.format(
            tool_name), timeout = 5)

    # assert mlwb is not supported if the cluster not enable it
    def cluster_not_enable_mlwb(self, cluster_name):
        tool_name = CloudClusterAccessToolsLocators.mlwb
        LOGGER.info(f"Expected `{tool_name}` is not accessed for cluster `{cluster_name}` since it doesn't enable the feature")
        self.sb.assert_element_absent(CloudClusterAccessToolsLocators.tool_div_selector_for.format(
            tool_name), timeout = 5)

    def get_cluster_tools_btn(self, cluster_name):
        return (
            "//div[text()='"
            + cluster_name
            + "']/ancestor::section//button[text()='Tools']"
        )

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def validate_cluster_tool_accessed(self, cluster_name, tool_name):
        # refresh the page
        self.sb.refresh()

        # wait the loading complete
        self.sb.wait_for_element_absent(CloudClusterLocators.cluster_loader_div, timeout=60)
        self.sb.maximize_window()

        LOGGER.info(f"Try to validate tool `{tool_name}` accessing of cluster `{cluster_name}`")

        # commond verifications
        # verify the tools accessed as expected
        # verify the tool logo
        self.sb.assert_element_present(
            CloudClusterAccessToolsLocators.tool_logo_svg_selector_for.format(
                CloudClusterAccessToolsLocators.convert[tool_name]), timeout=60
        )

        # verify the cluster name
        self.sb.assert_element_present(
            "//span[text()='{}']".format(cluster_name), timeout=60
        )

        # dedicated verification
        if tool_name == CloudClusterAccessToolsLocators.gst:
            self.validate_graphstudio()
        if tool_name == CloudClusterAccessToolsLocators.ins:
            self.validate_insights()
        if tool_name == CloudClusterAccessToolsLocators.gsql:
            self.validate_gsqlshell()
        if tool_name == CloudClusterAccessToolsLocators.adp:
            self.validate_adminportal()
        if tool_name == CloudClusterAccessToolsLocators.gql:
            self.validate_graphql()
        else:
            pass

    def validate_graphstudio(self):
            #self.sb.switch_to_frame(0, timeout=None)
            # assert the menu items
            self.sb.assert_element_present(
                CloudClusterAccessToolsLocators.gst_menu_panel_load_data_p, timeout = 30
            )
            self.sb.assert_element_present(
                CloudClusterAccessToolsLocators.gst_menu_panel_write_queries_p, timeout = 30
            )
            # assert the button
            self.sb.assert_element_present(
                CloudClusterAccessToolsLocators.gst_home_design_schema_btn, timeout = 60
            )
            # screen shot
            self.sb.attach_allure_screenshot("Open Graphstudio")

    def validate_insights(self):
            #self.sb.switch_to_frame(0, timeout=None)
            # assert the tab buttons
            self.sb.assert_element_present(
                CloudClusterAccessToolsLocators.ins_my_apps_tab_btn, timeout = 20
            )
            # self.sb.assert_element_present(
            #     CloudClusterAccessToolsLocators.ins_other_apps_tab_btn, timeout = 20
            # )
            # assert the span for new application
            self.sb.assert_element_present(
                CloudClusterAccessToolsLocators.ins_new_app_span, timeout = 60
            )
             # screen shot
            self.sb.attach_allure_screenshot("Open Insight")

    def validate_gsqlshell(self):
            #self.sb.switch_to_frame(0, timeout=None)
            # assert the shortcuts
            self.sb.assert_element_present(
                CloudClusterAccessToolsLocators.gsql_help_shortcuts_h3, timeout = 20
            )
            # screen shot
            self.sb.attach_allure_screenshot("Open GSQLShell")

    def validate_adminportal(self):
            #self.sb.switch_to_frame(0, timeout=None)
            # assert the menu
            # assert monitor 
            self.sb.assert_element_present(
                CloudClusterAccessToolsLocators.adp_menu_panel_monitor_p, timeout = 30
            )
            # assert backup and restore
            self.sb.assert_element_present(
                CloudClusterAccessToolsLocators.adp_menu_panel_backup_restore_p, timeout = 60
            )

            # screen shot
            self.sb.attach_allure_screenshot("Open AdminPortalHome")


    def validate_graphql(self):
            #self.sb.switch_to_frame(0, timeout=None)
            try:
                LOGGER.info("Try to assert the operation buttons in GraphQL")
                # assert the buttons
                self.sb.assert_element_present(
                    CloudClusterAccessToolsLocators.gql_exec_query_btn, timeout = 30
                )
                self.sb.assert_element_present(
                    CloudClusterAccessToolsLocators.gql_Prettify_btn, timeout = 30
                )
                self.sb.assert_element_present(
                    CloudClusterAccessToolsLocators.gql_Merge_btn, timeout = 60
                )
                self.sb.assert_element_present(
                    CloudClusterAccessToolsLocators.gql_Copy_btn, timeout = 60
                )
                self.sb.assert_element_present(
                    CloudClusterAccessToolsLocators.gql_History_btn, timeout = 60
                )
            except NoSuchElementException as err:
                LOGGER.info("Seem assertion fail, try to assert if it's caused by no existing graphs")
                # assert the missing graphs message
                self.sb.assert_element_present(
                    CloudClusterAccessToolsLocators.gql_no_existing_graph_msg_p, timeout = 20
                )
            finally:
                # screen shot
                self.sb.attach_allure_screenshot("Open GraphQL")


    @decorators.retry_on_exception(tries=10, delay=1, backoff=2, max_delay=32)
    def validate_mlwb(self, cluster_name):
        LOGGER.info(f"Try to validate tool `Machine Learning Workbench` accessing of cluster `{cluster_name}`")
        # assert the logo svg
        self.sb.assert_element_present(
            CloudClusterAccessToolsLocators.mlwb_logo_svg, timeout = 60
        )

        # assert and  double click the README.md
        self.sb.assert_element_present(
            CloudClusterAccessToolsLocators.mlwb_readme_span, timeout = 60
        )

        self.sb.double_click(
            CloudClusterAccessToolsLocators.mlwb_readme_span
        )

        self.sb.sleep(2)
        # check the main pannel
        self.sb.assert_element_present(
            CloudClusterAccessToolsLocators.mlwb_main_dock_pannel, timeout = 60
        )
        # check the readme.md message
        self.sb.assert_element_present(
            CloudClusterAccessToolsLocators.mlwb_title_h1, timeout = 60
        )

        # screen shot
        self.sb.attach_allure_screenshot("MLWBReadMe")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def back_to_cluster_homepage(self):
        LOGGER.info("Try to back to cluster home page")
        # click header menu
        self.wait_and_click(CloudClusterAccessToolsLocators.cloud_head_new_menu)
        # click cluster
        self.wait_and_click(CloudClusterAccessToolsLocators.cloud_cluster_menu)
        # assert the page is rendered
        self.sb.assert_text("Version", timeout=60)

    def switch_tools(self, cluster_name,target_tools,platform,is_mlwb):
        LOGGER.info(f"Try to switch to {target_tools}")
        # check if need back to homepage
        self.sb.wait_for_element_present(CloudClusterAccessToolsLocators.cloud_head_new_menu, timeout=30)
        if self.sb.is_element_present(CloudClusterAccessToolsLocators.cloud_head_new_menu):
            # turn back to homepage if using tools now
            self.back_to_cluster_homepage()
        else:
            LOGGER.info("cloud_head_new_menu is not present in page")
        self.access_cluster_tool(cluster_name,target_tools,platform,is_mlwb)