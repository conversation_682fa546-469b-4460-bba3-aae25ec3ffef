import re

from locators.tools.insights_locators import InsightLocators
from pages.cloud.clusters.access_cluster_tools_page import ClusterToolsPage
from pages.cloud.login.cloud_login_page import CloudLoginPage
from locators.cloud.cloud_locators import CloudClusterAccessToolsLocators
from seleniumbase.common import decorators
from utils.data_util.data_resolver import read_test_data
from base.tools_basecase import ToolsBaseCase
import logging
from locators.tools.graph_studio_locators import GSTHomeLocators

LOGGER = logging.getLogger(__name__)

wait_render_timeout = 60
click_timeout = 20

class LoginUtils():
    test_data = read_test_data(file="tools_test_data.json")
    test_env = test_data.get("test_env")
    login_way = test_data.get("login_way")
    login_user_test_data = test_data.get("login_onprem")
    cloud_org = test_data.get("org")
    cloud_cluster = test_data.get("cluster_name")
    cloud_username = test_data.get("user")
    cloud_password = test_data.get("password")
    cloud_platform = test_data.get("platform")

    def __init__(self, sb):
        self.sb = sb

    def is_oncloud(self):
        if "tgcloud" in self.test_env:
            return True
        else:
            return False

    def login(self, module=ToolsBaseCase.GraphStudio_URL, loginway=login_way):
        if self.is_oncloud():
            self.login_cloud(module)
        else:
            self.login_onprem(module, loginway)

    def login_cloud(self, tool_name=ToolsBaseCase.GraphStudio_URL):
        # login the cloud 
        cloudLoginPage = CloudLoginPage(self.sb)
        cloudLoginPage.open_login_page(self.test_env)
        cloudLoginPage.login(self.cloud_org, self.cloud_username, self.cloud_password)

        # cluster tools page
        clusterToolsPage = ClusterToolsPage(self.sb)
        tool_name = CloudClusterAccessToolsLocators.cloud_tools_url[tool_name]
        clusterToolsPage.access_cluster_tool(self.cloud_cluster, tool_name, self.cloud_platform, "false")

    def login_cloud_with_name_and_passwd(self, tool_name=ToolsBaseCase.Insights_URL, username="", passwd=""):
        # login the cloud
        cloudLoginPage = CloudLoginPage(self.sb)
        cloudLoginPage.open_login_page(self.test_env)
        cloudLoginPage.login(self.cloud_org, username, passwd)

        # cluster tools page
        clusterToolsPage = ClusterToolsPage(self.sb)
        tool_name = CloudClusterAccessToolsLocators.cloud_tools_url[tool_name]
        clusterToolsPage.access_cluster_tool(self.cloud_cluster, tool_name, self.cloud_platform, "false")


    def login_cloud_cluster_and_add_users(self):
        # login the cloud
        cloudLoginPage = CloudLoginPage(self.sb)
        cloudLoginPage.open_login_page(self.test_env)
        cloudLoginPage.login(self.cloud_org, self.cloud_username, self.cloud_password)

        # cluster tab
        clusterToolsPage = ClusterToolsPage(self.sb)
        clusterToolsPage.click_cluster_access_managerment_btn(self.cloud_cluster)

    def logout_cloud(self):
        # logout the cloud
        cloudLoginPage = CloudLoginPage(self.sb)
        cloudLoginPage.logout()
        LOGGER.info("logout_cloud")

    def login_onprem(self, module, LoginWay):
        self.open_module(module)
        if (LoginWay == "undefined"):
            LoginWay = self.login_way
        LOGGER.info(" The current login way is:{0}".format(LoginWay))

        if (LoginWay == ToolsBaseCase.LDAP_Login):
            self.login_with_username("qatest", "qatest")
        elif (LoginWay == ToolsBaseCase.LDAP_Group_Login):
            self.login_with_username("<EMAIL>", "bob")
        elif (LoginWay == ToolsBaseCase.OKTA_Login):
            self.SSO_login_send_anyway(open_moudle=module)
            self.sb.sleep(3)
        elif (LoginWay == ToolsBaseCase.AZURE_Login):
            self.SSO_login_send_anyway(open_moudle=module)
            self.sb.sleep(3)
        else:
            self.login_with_credential(LoginWay)

    def login_with_credential(self, LoginWay):
        login_username = "tigergraph"
        login_password = "tigergraph"
        for i in self.login_user_test_data:
            if i.get("type") == LoginWay:
                login_username = i.get("username")
                login_password = i.get("password")
        LOGGER.info(LoginWay + " login, login_username:" + login_username + ", login_password:" + login_password)
        self.login_with_username(login_username, login_password)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def open_module(self, module):
        try:
            env = read_test_data(file="tools_test_data.json").get("test_env")
            url = env + "/#/login?returnURL=/" + module + "/&loggedOut=true"
            LOGGER.info("login URL:" + url)
            self.sb.open(url)
            self.sb.wait_for_element_visible(InsightLocators.input_username, timeout=click_timeout)
            self.sb.assert_text("Username", timeout=click_timeout)
            self.sb.assert_text("Password", timeout=click_timeout)
        except Exception as e:
            LOGGER.info(str(e))
            self.sb.refresh()
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def SSO_login_send_anyway(self, open_moudle="studio"):
        try:
            self.sb.wait_for_element_clickable(InsightLocators.sso_login_button, timeout=wait_render_timeout)
            self.sb.click(InsightLocators.sso_login_button, timeout=click_timeout)
            if self.sb.is_element_present(InsightLocators.sso_login_send_anyway):
                self.sb.click(InsightLocators.sso_login_send_anyway, timeout=click_timeout)
        except Exception as e:
            LOGGER.info(str(e))
            self.sb.refresh()
            self.open_module(open_moudle)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def login_with_username(self, username="tigergraph", password="tigergraph"):
        # type method will take 20s, send_keys method will take 10s
        # self.sb.send_keys(InsightLocators.input_username, username, timeout=7)
        self.sb.type(InsightLocators.input_username, username, timeout=20)
        LOGGER.info("input_username done")
        # self.sb.send_keys(InsightLocators.input_password, password, timeout=7)
        self.sb.type(InsightLocators.input_password, password, timeout=20)
        LOGGER.info("input_password done")
        self.sb.click(InsightLocators.submit_button)
        self.sb.wait_for_ready_state_complete()
        self.sb.sleep(3)
        self.sb.wait_for_element_not_visible(InsightLocators.input_password, timeout=40)
        LOGGER.info("input_password not visible")
        self.close_security_float_windows()

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=5)
    def get_cloud_tg_version(self):
        version_text = self.sb.get_text(GSTHomeLocators.cloud_version_new_header, timeout=wait_render_timeout)
        LOGGER.info(version_text)
        if "version" not in version_text:
            LOGGER.info("Version not in version_text Exception")
            # raise Exception("version not in version_text")
        str_tmp = version_text.strip('"')
        LOGGER.info(str_tmp)
        match = re.search(r"\d+\.\d+\.\d+", str_tmp)  # find version number
        LOGGER.info(match)
        is_three_version = True
        if match:
            version_nums = match.group().split(".")
            LOGGER.info(version_nums)
            version = version_nums[0] + version_nums[1] + version_nums[2]
            if version_nums[0] != "3":
                is_three_version = False
                LOGGER.info("is not V3 serious, " + version_nums[0] + ", is_three_version: " + str(is_three_version))
            LOGGER.info("splice version: " + version)
            return version, is_three_version

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=5)
    def get_on_prem_tg_version(self):
        version_text = self.sb.get_text(GSTHomeLocators.on_prem_version_new_header, timeout=wait_render_timeout)
        LOGGER.info(version_text)
        # in tg_3.11.1, it shows v3.11.1
        if "Version" not in version_text:
            LOGGER.info("Version not in version_text Exception")
            # raise Exception("Version not in version_text")
        str_tmp = version_text.strip('"')
        LOGGER.info(str_tmp)
        match = re.search(r"\d+\.\d+\.\d+", str_tmp)  # find version number
        LOGGER.info(match)
        is_three_version = True
        if match:
            version_nums = match.group().split(".")
            LOGGER.info(version_nums)
            version = version_nums[0] + version_nums[1] + version_nums[2]
            if version_nums[0] != "3":
                is_three_version = False
                LOGGER.info("is not V3 serious, " + version_nums[0] + ", is_three_version: " + str(is_three_version))
            LOGGER.info("splice version: " + version)
            return version, is_three_version

    def close_security_float_windows(self):
        try:
            self.sb.wait_for_text_visible("user's password", timeout=60)
            LOGGER.info("wait_for_text_visible done")
            if self.sb.is_text_visible("user's password"):
                if self.sb.is_element_present(GSTHomeLocators.close_security_button):
                    self.sb.execute_script(GSTHomeLocators.close_security_icon_JS_3x)
                    LOGGER.info("close_security_float_windows done")
                else:
                    self.sb.execute_script(GSTHomeLocators.close_security_icon_JS_4x)
                    LOGGER.info("close_security_float_windows done")
                self.sb.attach_allure_screenshot("close_security_float_windows done screenshot")
            else:
                LOGGER.info("security_float_windows not appear, didn't find user's password")
                self.sb.attach_allure_screenshot("close_security_float_windows failed screenshot")
        except Exception as e:
            LOGGER.info("close_security_float_windows Exception:" + str(e))
            self.sb.attach_allure_screenshot("close_security_float_windows failed screenshot")

    def switch_to_other_tools(self, tool_name=ToolsBaseCase.GraphStudio_URL):
        # switch tools for cloud cluster
        clusterToolsPage = ClusterToolsPage(self.sb)
        tool_name = CloudClusterAccessToolsLocators.cloud_tools_url[tool_name]
        clusterToolsPage.switch_tools(self.cloud_cluster, tool_name, self.cloud_platform, "false")