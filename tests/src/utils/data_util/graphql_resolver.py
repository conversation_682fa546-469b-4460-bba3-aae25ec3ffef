import json
from pathlib import Path
import os


PROJECT_PATH = Path(__file__).resolve().parents[4]


class GraphqlTestDataResolver():
    def __init__(self, file):
        self.graphql_paths_title = "graphql_paths"
        self.upload_data_path_title = "upload_data_path"
        self.query_txtfile_title ="txtfile"

        self.graphql_test_data = self.read_graphql_testData(file)
        


    def read_graphql_testData(self, file):
        file = str(PROJECT_PATH.joinpath("./tests/config").joinpath(file))
        if Path(file).exists():
            with open(file) as f:
                raw_data = json.load(f)

            self.update_graphql_absPath(raw_data)
            return raw_data
        else:
            return None


    def update_graphql_absPath(self, graphql_raw_data):
        
        graph_paths = graphql_raw_data.get(self.graphql_paths_title)

        for path_key, path_value in graph_paths.items():
            path_value =path_value[1:] if path_value[0]=='/' else path_value
            path_value = PROJECT_PATH.joinpath(path_value)
            graphql_raw_data[self.graphql_paths_title][path_key] = path_value
        
        self.update_graphql_queryAbsPath(graphql_raw_data)
        
 


    def update_graphql_queryAbsPath(self, graphql_raw_data):
        
        upload_data_absPath = graphql_raw_data[self.graphql_paths_title][self.upload_data_path_title]
        for keys_1th, values_1th in graphql_raw_data.items():
            if isinstance(values_1th, dict):
                for keys_2nd, values_2nd in values_1th.items():
                    if isinstance(values_2nd, dict) and self.query_txtfile_title in values_2nd.keys():
                        testcase_queryPath = graphql_raw_data[keys_1th][keys_2nd][self.query_txtfile_title]
                        graphql_raw_data[keys_1th][keys_2nd][self.query_txtfile_title] = os.path.join(upload_data_absPath, testcase_queryPath)

    def get_projectPath(self):
        return PROJECT_PATH



