import allure
import logging

import pytest
from base.tools_baseaction import ToolsBaseAction
from pages.tools.graphstudio.graph_studio_write_queries_page import GSTWriteQueriesPage
from utils.run_query.query_parameter_filler import QueryParameterFiller
from utils.run_query.query_parameter_parser import QueryParameterParser
from locators.tools.graph_studio_locators import (
    DialogLocators,
    GSTHomeLocators,
    GSTLoadDataLocators,
    GSTWriteQueriesLocators
)
from common import tools_settings
from locators.tools.graph_studio_locators import GSTWriteQueriesLocators
from utils.pop_window.graph_studio_pop_window import GraphStudioWindows

from utils.data_util.login import LoginUtils


LOGGER = logging.getLogger(__name__)
assertTextTimeout = 30
wait_timeout = 60
assertTextTimeout = 60
clickTimeout = 60
short_clickTimeout = 7
wait_render_time = 120

class WriteQueriesAction(ToolsBaseAction):
    """Write, install and run query actions"""

    def __init__(self, sb):
        """Init all used pages"""
        super().__init__(sb)
        self.query_page = GSTWriteQueriesPage(sb)

    @allure.step("Install all queries")
    def install_all_queries(self, graphName='MyGraph', install_all_query_timeout=1800):
        page = self.query_page
        page.navigate_to_write_queries(graphName)
        self.sb.sleep(3)  # wait queries to render
        # check if already installed successfully
        try:
            self.sb.wait_for_element_clickable(GSTWriteQueriesLocators.download_query_btn, timeout=tools_settings.CLICK_TIME_OUT)
            el = self.sb.find_element(GSTWriteQueriesLocators.install_all_queries_btn).get_attribute("disabled")
            LOGGER.info("get disable attribute : " + str(el))
            if el:
                LOGGER.info("get disable attribute true, double check the not_installed_query_icon number")
                self.sb.attach_allure_screenshot("install_all_queries_btn disable attribute true screenshot")
                elements = self.sb.find_elements(GSTWriteQueriesLocators.not_installed_query_icon)
                installed_elements = self.sb.find_elements(GSTWriteQueriesLocators.installed_query_icon)
                LOGGER.info("not_installed_query_icon number:" + str(len(elements)))
                LOGGER.info("installed_query_icon number:" + str(len(installed_elements)))
                if len(elements) == 0 and len(installed_elements) > 0:
                    self.sb.attach_allure_screenshot("already installed screenshot")
                    return True
                else:
                    LOGGER.info("not_installed_query_icon exist, continue to install")
            else:
                LOGGER.info("not installed, continue to install")
        except Exception as e:
            LOGGER.info("get install_all_queries_btn disable attribute exception: " + str(e))

        if page.is_ready_install():
            self.sb.wait_for_element_clickable(GSTWriteQueriesLocators.install_all_queries_btn, timeout=tools_settings.CLICK_TIME_OUT)
            page.click_install_all_queries_btn()
            page.click_install_all_queries_btn_in_popup()
            page.wait_for_query_installation_complete(not_visiable_timeout=install_all_query_timeout)
            page.assert_install_all_queries_result_in_log()
            #page.assert_install_all_queries_btn_disabled()

    @allure.step("Run query based on a query parameter data file")
    def run_query_with_parameter_file(
        self,
        query_parameter_data_file_path,
        solution,
        graphName='MyGraph',
        run_single_query_timeout=3600,
    ):
        """ Run query based on a query parameter data file """
        page = self.query_page
        filler = QueryParameterFiller(self.sb)
        parser = QueryParameterParser(query_parameter_data_file_path)
        page.navigate_to_write_queries(graphName)
        query_count = parser.get_query_count(solution)
        for query_index in range(query_count):
            query_name = parser.get_nth_query_name(solution, query_index)
            page.search_select_query(query_name)
            page.click_run_query_btn_in_toolbar()
            parameter_count = parser.get_parameter_count(solution, query_name)
            if parameter_count > 0:
                LOGGER.info(f"Start to fill parameters for query `{query_name}`")
                for parameter_index in range(parameter_count):
                    filler.fill(
                        parser.get_nth_parameter(solution, query_name, parameter_index)
                    )
                page.click_run_query_btn()
            LOGGER.info(f"Run query `{query_name}`")
            query_execution_time=page.wait_for_run_query_complete(run_single_query_timeout)
            LOGGER.info(f"Query execution time:{query_name}={query_execution_time}")
            page.check_run_query_log()


    """ wait the circle progress bar """
    def wait_progress_bar(self, wait_present_time=30, wait_not_visible_time=90):
        try:
            self.sb.wait_for_element_present(GSTHomeLocators.circle_progressBar, timeout=wait_present_time)
            LOGGER.info("wait_progress_bar present PASS: " + str(wait_present_time))
            self.sb.wait_for_element_not_present(GSTHomeLocators.circle_progressBar, timeout=wait_not_visible_time)
            LOGGER.info("wait_progress_bar not present PASS: " + str(wait_not_visible_time))
        except Exception as e:
            LOGGER.info("wait_progress_bar exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_progress_bar exception screenshot")

    """ wait the horizon progress bar """
    def wait_horizon_progress_bar(self, wait_present_time=30, wait_not_visible_time=30):
        try:
            self.sb.wait_for_element_present(GSTHomeLocators.progressBar, timeout=wait_present_time)
            LOGGER.info("wait_horizon_progress_bar present PASS")
            # this method not worked well sometimes, so used the paired method
            # self.sb.wait_for_element_not_visible(GSTHomeLocators.progressBar, timeout=wait_not_visible_time)
            # LOGGER.info("wait_horizon_progress_bar not visible PASS")
            self.sb.wait_for_element_not_present(GSTHomeLocators.progressBar, timeout=wait_not_visible_time)
            LOGGER.info("wait_horizon_progress_bar not present PASS")
        except Exception as e:
            LOGGER.info("wait_horizon_progress_bar exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_horizon_progress_bar exception screenshot")

    def wait_and_click(self, css):
        LOGGER.info("wait and click: " + css)
        self.sb.wait_for_element_clickable(css, timeout=clickTimeout)
        self.sb.click(css, timeout=clickTimeout)

    def my_execute_JS(self, js):
        try:
            LOGGER.info("js: " + js)
            self.sb.execute_script(js)
        except Exception as e:
            LOGGER.info("JS exception: " + str(e))
            self.sb.attach_allure_screenshot("JS failed exception screenshot")

    def check_query_is_installed(self, query_name=""):
        LOGGER.info("need install query : " + query_name)
        # select the query
        self.sb.type(GSTHomeLocators.searchButton, query_name)
        self.wait_progress_bar(wait_present_time=short_clickTimeout)
        queryList = self.sb.find_elements(GSTHomeLocators.queryList)
        LOGGER.info("query list =" + str(len(queryList)))
        if len(queryList) > 0:
            for i in range(len(queryList)):
                self.sb.find_elements(GSTHomeLocators.queryList)[0].click()
        # install query
        if self.sb.is_element_clickable(GSTHomeLocators.installCurrentQueryButton):
            LOGGER.info("begin to install query : " + query_name)
            self.wait_and_click(GSTHomeLocators.installCurrentQueryButton)
            if self.sb.is_element_visible(GSTHomeLocators.common_span_button.format("OK")):
                self.wait_and_click(GSTHomeLocators.common_span_button.format("OK"))
                LOGGER.info("installed : " + query_name)
                self.sb.attach_allure_screenshot("already install query screenshot")
            else:
                self.wait_horizon_progress_bar(wait_present_time=wait_render_time, wait_not_visible_time=300)
        else:
            LOGGER.info("query was installed : " + query_name)
        self.sb.attach_allure_screenshot("install query done screenshot")
        # double check the save and install query buttons were disable
        if self.sb.is_element_clickable(GSTHomeLocators.installCurrentQueryButton) or self.sb.is_element_clickable(GSTHomeLocators.save_query_button):
            LOGGER.info("save and install query buttons are still clickable after installing query")
            raise Exception("save and install query buttons are still clickable after installing query")
        else:
            LOGGER.info("save and install query buttons are not clickable after installing query")

    def create_new_query_with_gsql(self, query_name="", need_install=False):
        loginUtil = LoginUtils(self.sb)
        cloud_env_flag = loginUtil.is_oncloud()
        if cloud_env_flag:
            version_tmp, is_three_version = loginUtil.get_cloud_tg_version()
        else:
            version_tmp, is_three_version = loginUtil.get_on_prem_tg_version()
        LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))

        if query_name == "run_interpreted_query_with_list" or query_name == "run_installed_query_with_list" or query_name == "run_installed_query_with_map":
            if not is_three_version and int(version_tmp) >= 420:
                # need GLE and engin PR to run in tg_4.2.0
                LOGGER.info("db version match >= tg_4.2.0, continue")
            else:
                pytest.skip("Skip this test case because DB version should >= 4.2.0")

        self.delete_old_query(query_name=query_name)
        # create new gsql query
        self.wait_and_click(GSTHomeLocators.createNewQueryButton)
        self.sb.type(GSTHomeLocators.inputNewQueryName, query_name, timeout=clickTimeout)
        self.wait_and_click(DialogLocators.create_btn)
        self.wait_progress_bar()
        self.sb.wait_for_text_not_visible("No results found", timeout=clickTimeout)

        # input query, save and run
        if query_name == "run_interpreted_query_with_list":
            myGsql = "CREATE OR REPLACE DISTRIBUTED QUERY run_interpreted_query_with_list(list<int> a) FOR GRAPH MyGraph {\n" + \
                     "PRINT  a;" + \
                     "}"
        elif query_name == "run_installed_query_with_list":
            myGsql = "CREATE OR REPLACE DISTRIBUTED QUERY run_installed_query_with_list(list<string> a) FOR GRAPH MyGraph {\n" + \
                     "PRINT  a;" + \
                     "}"
        elif query_name == "run_installed_query_with_map":
            myGsql = "CREATE OR REPLACE DISTRIBUTED QUERY run_installed_query_with_map(map<string,double> a) FOR GRAPH MyGraph {\n" + \
                     "PRINT  a;" + \
                     "}"
        cmd = "queryEditorChart.textEditor.setValue(`" + myGsql + "`)"
        LOGGER.info("create query cmd: " + cmd)
        self.my_execute_JS(cmd)
        self.sb.attach_allure_screenshot("create query done screenshot")

        # if not save, save query first
        self.wait_progress_bar(wait_present_time=short_clickTimeout)  # wait the page rendering
        if self.sb.is_element_clickable(GSTHomeLocators.save_query_button):
            LOGGER.info("click save query button")
            self.wait_and_click(GSTHomeLocators.save_query_button)
        else:
            self.sb.attach_allure_screenshot("query is saved, save query button not clickable screenshot")

        if need_install:
            LOGGER.info("need install query : " + query_name)
            # install query
            self.sb.wait_for_element_clickable(GSTHomeLocators.installCurrentQueryButton, timeout=clickTimeout)
            self.sb.click(GSTHomeLocators.installCurrentQueryButton, timeout=clickTimeout)
            self.wait_horizon_progress_bar(wait_present_time=wait_render_time, wait_not_visible_time=300)
            self.sb.attach_allure_screenshot("install query done screenshot")

    def delete_old_query(self, query_name=""):
        LOGGER.info("delete_old_query: " + query_name)
        gsw = GraphStudioWindows(self.sb)
        # delete old query before creating a query
        self.sb.type(GSTHomeLocators.searchButton, query_name)
        self.wait_progress_bar(wait_present_time=short_clickTimeout)
        queryList = self.sb.find_elements(GSTHomeLocators.queryList)
        LOGGER.info("query list =" + str(len(queryList)))
        if len(queryList) > 0:
            for i in range(len(queryList)):
                self.sb.find_elements(GSTHomeLocators.queryList)[0].click()
                if self.sb.is_element_clickable(GSTHomeLocators.deleteQueryButton):
                    self.sb.click(GSTHomeLocators.deleteQueryButton, timeout=clickTimeout)
                else:
                    self.sb.click(GSTHomeLocators.discardDraftButton, timeout=clickTimeout)
                gsw.getDialogContinueButton().click()
                self.wait_progress_bar()

        self.sb.attach_allure_screenshot("delete query done screenshot")


    @allure.step("Run query based on a query parameter data file")
    def run_query_with_name(
        self,
        query_name="",
        parameters="",
        assert_text="",
        graphName='MyGraph',
        expect_res="",
        is_globalUDF_role=False
    ):
        page = self.query_page
        page.navigate_to_write_queries(graphName)
        if query_name == "run_interpreted_query_with_list":
            self.create_new_query_with_gsql(query_name=query_name)
        elif query_name == "run_installed_query_with_list" or query_name == "run_installed_query_with_map":
            self.create_new_query_with_gsql(query_name=query_name, need_install=True)
        if query_name == "run_installed_query_with_limit_parameter_tg393":
            self.check_query_is_installed(query_name=query_name)
        if page.search_select_query(query_name, is_global_UDF=is_globalUDF_role):
            LOGGER.info("return True, assert True, return ")
            assert True
            return
        page.click_run_installed_query_btn(query_name=query_name)

        # wait result
        LOGGER.info(f"Run query `{query_name}`")
        page.wait_for_run_query_complete(run_query_timeout=wait_timeout)
        page.wait_for_install_query_run_complete()

        # assert result
        page.click_view_json_result_btn()
        if assert_text != "":
            if query_name == "run_installed_query_with_limit_parameter_tg393":
                loginUtil = LoginUtils(self.sb)
                cloud_env_flag = loginUtil.is_oncloud()
                if cloud_env_flag:
                    version_tmp, is_three_version = loginUtil.get_cloud_tg_version()
                else:
                    version_tmp, is_three_version = loginUtil.get_on_prem_tg_version()
                LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))

                if is_three_version and int(version_tmp) >= 3102:
                    LOGGER.info("db version match >= tg_3.10.2, assert json result")
                    self.sb.assert_text(assert_text, timeout=assertTextTimeout)
                if not is_three_version and int(version_tmp) >= 410:
                    LOGGER.info("db version match >= tg_4.1.0, assert json result")
                    self.sb.assert_text(assert_text, timeout=assertTextTimeout)
                self.sb.attach_allure_screenshot("assert json result done")
            else:
                self.sb.assert_text(assert_text, timeout=assertTextTimeout)
        page.check_run_query_log(expect_res, is_globalUDF_role=is_globalUDF_role)



    @allure.step("Install all queries")
    def install_all_queries_with_partial_success(self, graphName='mysql_yuling', install_all_query_timeout=900):
        # check tg version >= 420
        loginUtil = LoginUtils(self.sb)
        cloud_env_flag = loginUtil.is_oncloud()
        if cloud_env_flag:
            version_tmp, is_three_version = loginUtil.get_cloud_tg_version()
        else:
            version_tmp, is_three_version = loginUtil.get_on_prem_tg_version()
        LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))

        if not is_three_version and int(version_tmp) >= 420:
            LOGGER.info("db version match >= tg_4.2.0, continue")
        else:
            pytest.skip("Skip this test case because DB version should >= 4.2.0 to support list function")

        page = self.query_page
        page.navigate_to_write_queries(graphName)
        self.sb.sleep(3)  # wait queries to render
        # check if already installed successfully
        try:
            self.sb.wait_for_element_clickable(GSTWriteQueriesLocators.download_query_btn, timeout=tools_settings.CLICK_TIME_OUT)
            el = self.sb.find_element(GSTWriteQueriesLocators.install_all_queries_btn).get_attribute("disabled")
            LOGGER.info("get disable attribute : " + str(el))
            if el:
                LOGGER.info("get disable attribute true, double check the not_installed_query_icon number")
                self.sb.attach_allure_screenshot("install_all_queries_btn disable attribute true screenshot")
                elements = self.sb.find_elements(GSTWriteQueriesLocators.not_installed_query_icon)
                installed_elements = self.sb.find_elements(GSTWriteQueriesLocators.installed_query_icon)
                LOGGER.info("not_installed_query_icon number:" + str(len(elements)))
                LOGGER.info("installed_query_icon number:" + str(len(installed_elements)))
                if len(elements) == 1 and len(installed_elements) > 0:
                    self.sb.attach_allure_screenshot("already installed screenshot")
                    return True
                else:
                    LOGGER.info("not_installed_query_icon exist, continue to install")
            else:
                LOGGER.info("not installed, continue to install")
        except Exception as e:
            LOGGER.info("get install_all_queries_btn disable attribute exception: " + str(e))

        if page.is_ready_install():
            self.sb.wait_for_element_clickable(GSTWriteQueriesLocators.install_all_queries_btn, timeout=tools_settings.CLICK_TIME_OUT)
            page.click_install_all_queries_btn()
            page.click_install_all_queries_btn_in_popup()
            page.wait_for_query_installation_complete(not_visiable_timeout=install_all_query_timeout)
            # just assert in >= tg_4.1.3
            loginUtil = LoginUtils(self.sb)
            cloud_env_flag = loginUtil.is_oncloud()
            if cloud_env_flag:
                version_tmp, is_three_version = loginUtil.get_cloud_tg_version()
            else:
                version_tmp, is_three_version = loginUtil.get_on_prem_tg_version()
            LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))
            self.sb.attach_allure_screenshot("click install all query button")

            if not is_three_version and int(version_tmp) >= 413:
                LOGGER.info("db version match >= tg_4.1.3, assert the result")
                self.sb.assert_text("Failed to install", timeout=60)
                self.sb.attach_allure_screenshot("assert Failed to install query result")
            else:
                pytest.skip("Skip this test case because DB version should >= 4.1.3")


