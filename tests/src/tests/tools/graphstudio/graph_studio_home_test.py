import time
import pytest
import allure

from pages.tools.graphstudio import GSTHomePage
from base.tools_basecase import ToolsBaseCase


class TestGST_Home(ToolsBaseCase):

    @allure.title("import solution GST  - smoke test")
    @allure.description("Check import solution GST")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.preexec
    @pytest.mark.import_solution
    @pytest.mark.run(order=0.1)
    def test_import_solution_GST(self):
        super().setUp()
        # self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.importSolution()


    @allure.title("RDBMS_MySQL - smoke test")
    @allure.description("RDBMS_MySQL")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.SQLDB
    @pytest.mark.preexec
    @pytest.mark.run(order=22)
    def test_RDBMS_MySQL(self):
        super().setUp()
        #self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.RDBMS_MySQL()


    @allure.title("RDBMS_PostgreSQL - smoke test")
    @allure.description("RDBMS_PostgreSQL")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.SQLDB
    @pytest.mark.preexec
    @pytest.mark.run(order=11)
    def test_RDBMS_PostgreSQL(self):
        super().setUp()
        #self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.RDBMS_PostgreSQL()


    @allure.title("global designer privillege check - smoke test")
    @allure.description("global designer privillege check")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.onprem
    # @pytest.mark.dependency(depends=["test_add_globaldesigner"])
    @pytest.mark.run(order=4007)
    def test_global_designer_privillege(self):
        super().setUp()
        #self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login(login_way="global_designer")
        gst.global_designer_privillege()

    @allure.title("local admin privillege check - smoke test")
    @allure.description("local admin privillege check")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.onprem
    # @pytest.mark.dependency(depends=["test_add_localadmin"])
    @pytest.mark.run(order=4027)
    def test_local_admin_privillege(self):
        super().setUp()
        #self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login(login_way="local_admin")
        gst.local_admin_privillege()

    @allure.title("action page check - smoke test")
    @allure.description("action page check")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.accessAction
    @pytest.mark.run(order=4028)
    def test_action_page_check(self):
        super().setUp()
        #self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.action_page_check()

    @allure.title("export solution with default - smoke test")
    @allure.description("export solution with default  check")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.basecases
    @pytest.mark.export_solution
    @pytest.mark.run(order=4.01)
    def test_export_solution_with_default_check(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.export_solution_check(all_graph=False, single_graph=False, with_data=False)

    @allure.title("export all graph without data - smoke test")
    @allure.description("export all graph without data check")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.export_solution
    @pytest.mark.offline
    @pytest.mark.run(order=29)
    def test_export_all_without_data_check(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.export_solution_check(all_graph=True, single_graph=False, with_data=False)

    @allure.title("export all graph with data - smoke test")
    @allure.description("export all graph with data check")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.export_solution
    @pytest.mark.offline
    @pytest.mark.run(order=29)
    def test_export_all_with_data_check(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.export_solution_check(all_graph=True, single_graph=False, with_data=True)

    @allure.title("export single graph with data - smoke test")
    @allure.description("export single graph with data check")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.export_solution
    @pytest.mark.offline
    @pytest.mark.run(order=29)
    def test_export_single_graph_with_data_check(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.export_solution_check(all_graph=False, single_graph=True, with_data=True)

    @allure.title("export single graph without data - smoke test")
    @allure.description("export single graph without data check")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.export_solution
    @pytest.mark.offline
    @pytest.mark.run(order=29)
    def test_export_single_graph_without_data_check(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.export_solution_check(all_graph=False, single_graph=True, with_data=False)