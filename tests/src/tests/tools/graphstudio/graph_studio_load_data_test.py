import pytest
import allure

from pages.tools.graphstudio import GSTHomePage
from base.tools_basecase import ToolsBaseCase


class TestGST_LoadData(ToolsBaseCase):

    @allure.title("Load data via local files in GST")
    @allure.description(
        "TestType: Positive \n"
        "Target: Load data via local files in GST \n"
        "Description: Load data via local files in GST \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-05-01 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gapnew
    @pytest.mark.preexec
    @pytest.mark.run(order=0.3)
    def test_load_data_GST(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.load_data()

    @allure.title("Add snowflake datasource in GST when TG version >= 4.2.0")
    @allure.description(
        "TestType: Positive \n"
        "Target: Add snowflake datasource in GST when TG version >= 4.2.0 \n"
        "Description: Add snowflake datasource in GST when TG version >= 4.2.0 \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-04-01 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.snowflake
    @pytest.mark.run(order=26)
    def test_add_snowflake_in_GST(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.add_snowflake_in_load_data()