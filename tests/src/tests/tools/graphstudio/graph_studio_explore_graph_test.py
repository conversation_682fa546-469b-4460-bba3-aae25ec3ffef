import pytest
import allure

from pages.tools.graphstudio import GSTHomePage
from base.tools_basecase import ToolsBaseCase


class TestGST_ExploreGraph(ToolsBaseCase):


    @allure.title("check open old and new explore graph in GST")
    @allure.description(
        "TestType: positive \n"
        "Target: Check open old and new explore graph in GST \n"
        "Description: open old and new explore graph in GST \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-03-31 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.explore_graph
    @pytest.mark.run(order=1)
    def test_open_old_and_new_explore_graph_GST(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.open_old_explore_graph()
        gst.open_new_explore_graph()


    @allure.title("check run interpreted page_rank in new explore graph in GST")
    @allure.description(
        "TestType: positive \n"
        "Target: check run interpreted page_rank in new explore graph in GST \n"
        "Description: check run interpreted page_rank in new explore graph in GST \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-03-31 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.explore_graph
    @pytest.mark.run(order=2)
    def test_run_interpreted_page_rank_query_in_new_explore_graph_GST(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.add_my_page_rank_query_in_write_queries(query_name="interpreted_page_rank")
        gst.nave_to_explore_graph()
        gst.open_new_explore_graph(query_name="interpreted_page_rank")

    @allure.title("check run installed page_rank in new explore graph in GST")
    @allure.description(
        "TestType: positive \n"
        "Target: check run installed page_rank in new explore graph in GST \n"
        "Description: check run installed page_rank in new explore graph in GST \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-03-31 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.explore_graph
    @pytest.mark.run(order=3)
    def test_run_installed_page_rank_query_in_new_explore_graph_GST(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.add_my_page_rank_query_in_write_queries(query_name="installed_page_rank")
        gst.nave_to_explore_graph()
        gst.open_new_explore_graph(query_name="installed_page_rank")


