import logging
import unittest
import pytest
import allure
import os
import time
import shutil
from collections import OrderedDict
import logging

from base.tools_basecase import ToolsBaseCase
from pages.tools.gshell.gshell_page import GshellPage
from pages.tools.graphstudio.graph_studio_home_page import GSTHomePage
from utils.data_util.gshell_resolver import GshellTestDataResolver
LOGGER = logging.getLogger(__name__)


class TestQueries(ToolsBaseCase):
    gshell_test_data = GshellTestDataResolver("gshell_test_data.json").gshell_test_data


    @allure.title("Gshell Queries Options - smoke test")
    @allure.description("Check gshell queries ")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gshell
    @pytest.mark.preexec  # create & install & run query, so move preexec
    @pytest.mark.gshell_upload
    @pytest.mark.lock_catalog
    @pytest.mark.opencypher
    @pytest.mark.run(order=96)
    def test_queries(self):
        #define tescase data
        testcase_queries = OrderedDict(self.gshell_test_data.get("testcase_queries"))

        #execute tescase
        gshellPage = GshellPage(self)
        gstPage = GSTHomePage(self)
        gshellPage.login_gshell()  
        gshellPage.execute_OrderedDictQueries(testcase_queries, gstPage.is_cluster_available(393))


    @allure.title("Gshell Queries Options - smoke test")
    @allure.description("Check gshell queries ")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gshell
    @pytest.mark.preexec  # create & install & run query, so move preexec
    @pytest.mark.gshell_upload
    @pytest.mark.lock_catalog
    @pytest.mark.opencypher
    @pytest.mark.run(order=96)
    def test_queries(self):
        #define tescase data
        testcase_queries = OrderedDict(self.gshell_test_data.get("testcase_prepare_insights_queries"))

        #execute tescase
        gshellPage = GshellPage(self)
        gstPage = GSTHomePage(self)
        gshellPage.login_gshell()
        gshellPage.execute_OrderedDictQueries(testcase_queries, gstPage.is_cluster_available(393))



    @allure.title("Gshell Upload File for primary key vertex - smoke test")
    @allure.description(
        "TestType: positive \n"
        "Target: Check if we can support primary key showing in GST and insights \n"
        "Description: Check if we can support primary key showing in GST and insights \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-04-14 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gshell
    @pytest.mark.primary_key
    @pytest.mark.preexec
    @pytest.mark.run(order=97)
    def test_show_primary_key_in_GST_and_insights(self):
        # create primary key vertex
        testcase_queries = OrderedDict(self.gshell_test_data.get("testcase_primary_key"))

        # execute gsql command for primary key
        gshellPage = GshellPage(self)
        gstPage = GSTHomePage(self)
        gshellPage.login_gshell()
        gshellPage.execute_OrderedDictQueries(testcase_queries)


    @allure.title("Prepare the queries for input widgets regression test - smoke test")
    @allure.description(
        "TestType: positive \n"
        "Target: Prepare the queries for input widgets regression test \n"
        "Description: Prepare the queries for input widgets regression test \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-04-14 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gshell
    @pytest.mark.prepare_queries
    @pytest.mark.preexec
    @pytest.mark.run(order=98)
    def test_prepare_queries_for_input_widgets_regression_in_insights(self):
        # create queries
        testcase_queries = OrderedDict(self.gshell_test_data.get("testcase_prepare_insights_queries"))
        # execute gsql command for input widgets queries
        gshellPage = GshellPage(self)
        gshellPage.login_gshell()
        gshellPage.execute_OrderedDictQueries(testcase_queries)


    @allure.title("Prepare the queries for negative cases - smoke test")
    @allure.description(
        "TestType: negative \n"
        "Target: Prepare the queries for negative cases \n"
        "Description: Prepare the queries for negative cases \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-04-21 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3352, https://graphsql.atlassian.net/browse/GLE-6742"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gshell
    @pytest.mark.prepare_queries
    @pytest.mark.preexec
    @pytest.mark.run(order=99)
    def test_prepare_queries_for_negative_cases(self):
        # create queries
        testcase_queries = OrderedDict(self.gshell_test_data.get("testcase_prepare_negative_queries"))
        # execute gsql command for input widgets queries
        gshellPage = GshellPage(self)
        gshellPage.login_gshell()
        gshellPage.execute_OrderedDictQueries(testcase_queries)



    @allure.title("Gshell Download CSV file  - smoke test")
    @allure.description("Download CSV file ")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gshell
    @pytest.mark.gshellDownloadCSV
    @pytest.mark.opencypher_cloud
    @pytest.mark.lock_catalog
    @pytest.mark.preexec  # create & install & run query, so move preexec
    @pytest.mark.run(order=57)
    def test_downloadJson(self):
        #define tescase data
        testcase_queries = OrderedDict(self.gshell_test_data.get("testcase_queries"))
        specifyDownPath = "testcase_downloadCSV-" + time.strftime('%Y-%m-%d', time.localtime())
        specifyDownPath = os.path.join(self.gshell_test_data["gshell_paths"]["download_data_path"], specifyDownPath)
        #execute tescase
        gshellPage = GshellPage(self)
        gstPage = GSTHomePage(self)
        path = gshellPage.setChromeDownloadPath(specifyDownPath)
        gshellPage.login_gshell()
        for _, queryInfo in testcase_queries.items():
            #define different setting according to queryOption
            isJsonType = True if 'jsonOutput' in queryInfo.keys() else False
            isSkip = True if not gstPage.is_cluster_available(393) and 'opencypher' in queryInfo["gsqlfile"] else False

            if not isSkip:
                #run gsqlfile and verify results according to queryInfo
                gshellPage.run_gsqlfile_verifyText(queryInfo["gsqlfile"], queryInfo["output"], queryInfo["assertTimeout"], isJsonType)
                LOGGER.info("run gsql file done ----------- " + path)
                if isJsonType:
                    specifyFilename = os.path.split(queryInfo["gsqlfile"])[-1].split('.')[0] + '-download.csv'
                    gshellPage.download_lastJsonFile(path, specifyFilename)
            else:
                LOGGER.info("gstPage.is_cluster_available(393) and 'opencypher', so skip " + path)
        #remove downloadfile to void data accumulation
        shutil.rmtree(path)