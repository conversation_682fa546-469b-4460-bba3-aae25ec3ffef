import pytest
import allure
from pages.tools.adminportal.adminportal_page import GAPPage
from base.tools_basecase import ToolsBaseCase
from locators.tools.admin_portal_locators import GAPUsersLocator
from utils.data_util.data_resolver import read_test_data


class TestUserManagement(ToolsBaseCase):

    test_data = read_test_data(file="tools_test_data.json")
    login_user_test_data = test_data.get("login_onprem")

    @allure.title("GAP add user and grant global role - smoke test")
    @allure.description("GAP add globaldesigner user")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.adduser
    @pytest.mark.preexec
    # @pytest.mark.dependency(name="test_add_globaldesigner")
    @pytest.mark.run(order=6200)
    @pytest.mark.order(after="test_grant_localrole_user")
    def test_add_globaldesigner(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        gap.createUser(self, "u_globaldesigner", "globaldesigner")

        # Grant role
        status, index = gap.grantRole(self, username="u_globaldesigner")
        if status:
            # Wait save finish
            self.assert_text("Successfully granted globaldesigner on global to users u_globaldesigner", timeout=20)
            self.wait_for_element_not_visible(GAPUsersLocator.grantprompt)
            self.attach_allure_screenshot("Grant global designer successfully screenshot")

            # # Dynamic Search for user
            # user_checkbox = self.find_element("//mat-checkbox[@id='mat-checkbox-{}']/label/div/input".format(index))
            # # Check user checked and save button status
            # self.assert_true(user_checkbox.get_attribute("ariaChecked"), "Check user checkbox checked")
            el = self.find_element(GAPUsersLocator.saveGrant)
            self.assert_equal("false", el.get_attribute("draggable"), "Check save button status")
            self.attach_allure_screenshot("Check save button status successfully screenshot")

    @allure.title("GAP add user and grant localadmin - smoke test")
    @allure.description("GAP grant local role to user, role is admin and belongs MyGraph")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.adduser
    @pytest.mark.basecases
    @pytest.mark.run(order=6201)
    # @pytest.mark.dependency(name="test_add_localadmin")
    @pytest.mark.order(after="test_add_globaldesigner")
    def test_add_localadmin(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        gap.createUser(self, "u_localadmin", "localadmin")

        # Grant role
        status, index = gap.grantRole(self, "u_localadmin", "admin", False, "MyGraph")
        if status:
            # Wait save finish
            self.assert_text("Successfully granted admin on graph MyGraph to users u_localadmin", timeout=20)
            self.wait_for_element_not_visible(GAPUsersLocator.grantprompt)
            self.attach_allure_screenshot("Grant local admin successfully")
            el = self.find_element(GAPUsersLocator.saveGrant)
            self.assert_equal("false", el.get_attribute("draggable"), "Check save button status")
            self.attach_allure_screenshot("Check save button status successfully screenshot")

    @allure.title("Add a new user for user-defined global role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check add a new user for user-defined global role \n"
        "Description: Add a new user for user-defined global role in admin portal \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-08-28 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.basecases
    @pytest.mark.adduser1
    @pytest.mark.run(order=0.50)
    def test_create_user_for_globalrole(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        for i in self.login_user_test_data:
            if i.get("type") == "global_UDF_role":
                gap.createUser(self, i.get("username"), i.get("password"))

    @allure.title("Add a new user for user-defined local role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check add a new user for user-defined local role \n"
        "Description: Add a new user for user-defined local role in admin portal \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-08-28 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.basecases
    @pytest.mark.adduser1
    @pytest.mark.run(order=0.52)
    def test_create_user_for_localrole(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        for i in self.login_user_test_data:
            if i.get("type") == "local_UDF_role":
                gap.createUser(self, i.get("username"), i.get("password"))

    @allure.title("Verify global designer user exists with correct role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if global designer user exists and has correct role assignment \n"
        "Description: Navigate to user management page and verify u_globaldesigner exists with global/globaldesigner role \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2025-03-06 \n"
        "Link: https://graphsql.atlassian.net/browse/QA-7918"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.adduser
    @pytest.mark.run(order=6202)
    def test_verify_global_designer_user_role(self):
        super().setUp()
        gap = GAPPage()
        # Login to system
        gap.GapLogin(self)
        
        # Enter user management interface
        gap.intoUserManegement(self)
        self.attach_allure_screenshot("Entered user management page")
        
        # Switch to All Users tab
        self.wait_for_element_clickable(GAPUsersLocator.usertab, timeout=20)
        self.click(GAPUsersLocator.usertab)
        self.wait_for_ready_state_complete()
        self.attach_allure_screenshot("Switched to All Users tab")
        # Verify username element exists
        self.assert_element(GAPUsersLocator.user_name_cell, timeout=20)
        # Get user row
        user_row = self.find_element(GAPUsersLocator.user_row)
        # Verify role information
        role_cell = self.find_element(GAPUsersLocator.role_cell, timeout=20)
        role_text = role_cell.text
        # Verify role is correct (global/globaldesigner)
        self.assert_true("global" in role_text, "Global role not found")
        self.assert_true("globaldesigner" in role_text, "Globaldesigner role not found")
        
        self.attach_allure_screenshot("Verified global designer user and role")

