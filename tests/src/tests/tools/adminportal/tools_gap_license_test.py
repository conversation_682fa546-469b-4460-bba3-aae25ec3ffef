import pytest
import allure
from pages.tools.adminportal.adminportal_page import GAPPage
from base.tools_basecase import ToolsBaseCase
from locators.tools.admin_portal_locators import GAPLicenseLocator
from selenium.webdriver.remote.file_detector import UselessFileDetector


class TestLicense(ToolsBaseCase):

    @allure.title("GAP License update- smoke test")
    @allure.description("License update")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.preexec
    @pytest.mark.gaplicense
    @pytest.mark.offline
    @pytest.mark.run(order=1101)
    def test_license(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        license_path = gap.getLicense(self)
        gap.intoLicenseManegement(self)
        self.attach_allure_screenshot("turn into License Manegement screenshot")
        self.wait_for_element_clickable(GAPLicenseLocator.selectfile, timeout=60)
        self.choose_file(GAPLicenseLocator.filepath, license_path)

        # check upload success
        self.assert_text("LICENSE.txt")
        self.click(GAPLicenseLocator.update)
        self.attach_allure_screenshot("Update license screenshot")

        # check update success
        self.assert_text("License has been updated")
        self.assert_text("Valid")
        self.wait(30)


