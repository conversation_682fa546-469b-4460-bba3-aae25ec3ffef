import pytest
import allure
from pages.tools.adminportal.adminportal_page import GAPPage
from base.tools_basecase import ToolsBaseCase
from locators.tools.admin_portal_locators import GAPProfileLocator, GAPUsersLocator
import logging
from utils.data_util.login import LoginUtils
from locators.tools.graph_studio_locators import GSTHomeLocators
from locators.tools.admin_portal_locators import GAPHomeLocators

LOGGER = logging.getLogger(__name__)

class TestMyProfile(ToolsBaseCase):

    @allure.title("GAP MyProfile password change- smoke test")
    @allure.description("MyProfile password change")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.gapchangepw
    @pytest.mark.preexec
    @pytest.mark.offline  # failed in wip/mit job but succeed in dailyrun, so disable in tools_e2e
    @pytest.mark.run(order=1)
    def test_profile_password(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        gap.change_password_in_profile(self)





