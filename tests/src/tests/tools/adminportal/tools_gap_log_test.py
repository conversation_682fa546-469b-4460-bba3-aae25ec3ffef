import os.path
import pytest
import allure
import time
from pages.tools import ToolsHomePage
from pages.tools.adminportal.adminportal_page import GAPPage
from base.tools_basecase import ToolsBaseCase
from utils.data_util.data_resolver import read_test_data
from locators.tools.admin_portal_locators import GAPLogLocators
from pages.tools.adminportal.adminportal_page import GAPHomeLocators
from actions.tools.graph_studio.write_queries_action import WriteQueriesAction
from pages.tools.graphstudio.graph_studio_home_page import GSTHomePage


class TestLogManagement(ToolsBaseCase):

    tools_cdn = read_test_data(file="tools_test_data.json").get("tools_cdn")

    @allure.title("GAP Log Management test_search_log - smoke test")
    @allure.description("Search Log via pattern with node and component")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gaplog
    @pytest.mark.offline
    @pytest.mark.run(order=1001)
    def test_search_log(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoLogManagement(self)
        self.attach_allure_screenshot("Open log management page screenshot")
        # select component
        gap.selectComponent(self, "GSQL")
        # Search log via pattern
        gap.searchLogWithPattern(self, "GSQL#1")
        el = self.find_element(GAPLogLocators.searchlistdrag, timeout=60)
        self.execute_script("arguments[0].scrollTop=10000", el)
        self.attach_allure_screenshot("Rearch result screenshot")
        self.assert_text("/gsql/", timeout=20)

    @allure.title("GAP Log Management test_display_log - smoke test")
    @allure.description("Display Log via click folder")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gaplog
    @pytest.mark.offline
    @pytest.mark.run(order=1021)
    def test_display_log(self):
        super().setUp()
        # set chrome download path One time
        Browser = os.getenv("Browser", "")
        # Browser = "edge"
        # LOGGER.info("Browser= " + Browser)
        if Browser == "firefox":
            download_path = self.get_downloads_folder()
        else:
            download_path = "/tmp/GAP"
        gap = GAPPage()
        gap.setChromeDownloadPath(self, download_path)
        gap.GapLogin(self)
        gap.intoLogManagement(self)
        self.attach_allure_screenshot("Opened log management page screenshot")
        # Select admin log to view
        gap.displayAdminLog(self)
        el = self.find_element(GAPLogLocators.logfolderdrag)
        gap.drag(self, el, 0, 45)
        self.click(GAPLogLocators.adminlog)
        self.attach_allure_screenshot("Admin log result")
        # Determining Log Content
        self.wait(3)
        self.assert_text("m1:/admin/ADMIN.INFO", timeout=20)

        el = self.find_element(GAPLogLocators.logveiewerdrag)
        self.execute_script("arguments[0].scrollTop=0", el)
        self.assert_element(GAPLogLocators.logveiewer)  # AdminService is initialized
        el = self.find_element(GAPLogLocators.logveiewer)
        el_size = el.size
        self.assert_true(el_size["height"] > 200)
        # Download Log File
        self.wait_for_element_clickable(GAPLogLocators.downloadlog, timeout=20)
        self.double_click(GAPLogLocators.downloadlog, timeout=20)
        self.attach_allure_screenshot("Download search file result screenshot")
        # Check download file and file size
        download_res, download_file = gap.judge_file_exist(5, download_path, "ADMIN")
        if download_res:
            file_size = os.path.getsize("{}/{}".format(download_path, download_file))
            self.assert_true(file_size > 10)
        else:
            self.attach_allure_screenshot("Download search file result-Failed")
            self.fail(msg="Download file fail")

    @allure.title("Login with global designer and access monitor logs")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if user with global designer role can access monitor logs \n"
        "Description: Login with global designer user and navigate to monitor logs page \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2025-03-06 \n"
        "Link: https://graphsql.atlassian.net/browse/QA-7918"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.tools
    @pytest.mark.onprem  
    @pytest.mark.gaplog
    @pytest.mark.run(order=5102)
    def test_global_designer_access_monitor_logs(self):
        super().setUp()
        gap = GAPPage()
        # Login with global designer role user
        gap.GapLogin(self, login_way="global_designer")

        # Navigate to Monitor page 
        gap.intoMonitor(self)
        self.attach_allure_screenshot("Successfully entered Monitor page")

        # Navigate to Log Management page
        gap.intoLogManagement(self)
        self.attach_allure_screenshot("Successfully entered Log Management page")

    @allure.title("GAP Access Monitor and Queries - smoke test")
    @allure.description(
        "TestType: Positive \n"
        "Target: Check if user can access Monitor and Queries pages \n"
        "Description: Login and navigate to Monitor and Queries pages to verify access \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2025-03-06 \n"
        "Link: https://graphsql.atlassian.net/browse/QA-7918"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.gaplog
    @pytest.mark.run(order=5102)
    def test_access_monitor_and_queries(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        
        # Create and install query
        writeQueriesAction = WriteQueriesAction(self)
        # Execute query
        writeQueriesAction.run_query_with_name(
            query_name="use_map",  # Use query that will be auto-created
            graphName="MyGraph"
        )
        self.attach_allure_screenshot("Successfully executed query with map parameter")
        gap = GAPPage()
        gap.GapLogin(self)
        # Navigate to Monitor page
        gap.intoMonitor(self)
        self.attach_allure_screenshot("Successfully entered Monitor page")

        # Navigate to Queries page
        self.wait_for_element_clickable(GAPHomeLocators.wlm, timeout=20)
        self.click(GAPHomeLocators.wlm)
        self.wait_for_ready_state_complete()
        self.attach_allure_screenshot("Successfully entered Queries page")
        # Wait 6 minutes before checking element
        time.sleep(360)  # Wait 6 minutes
        # Check if MyGraph element exists in table
        self.wait_for_element_present(GAPHomeLocators.wlm_query_path, timeout=20)
        # Verify element exists and content is correct
        graph_cell = self.find_element(GAPHomeLocators.wlm_query_path)
        assert "GET /query/MyGraph/use_map" in graph_cell.text.strip(), f"Expected query path not found in '{graph_cell.text.strip()}'"
        self.attach_allure_screenshot("Successfully verified MyGraph element after 6 minutes")


