import pytest
import allure
from pages.tools.adminportal.adminportal_page import GAPPage
from base.tools_basecase import ToolsBaseCase
from utils.data_util.data_resolver import read_test_data
from parameterized import parameterized
import logging
from pathlib import Path

LOGGER = logging.getLogger(__name__)
DATA_PATH = Path(__file__).resolve().parents[4].joinpath("data")

class TestGapUpgradeTools(ToolsBaseCase):

    test_data = read_test_data(file="tools_test_data.json")
    login_user_test_data = test_data.get("login_onprem")
    user_test_data = []
    for i in login_user_test_data:
        user_test_data.append((i.get("type")))

    @allure.title("GAP Upgrade with customed tools package - upgrade test")
    @allure.description(
        "TestType: Positive \n"
        "Target: Check manual upload valid format package \n"
        "Description: Manual upload valid format packagein AdminPortal \n"
        "TestDesigner: <PERSON><PERSON> \n"
        "Date: 2024-09-13 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2551 \n")
    @pytest.mark.high
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.toolspackage
    @pytest.mark.toolsupgrade
    @pytest.mark.run(order=100)
    def test_upload_upgrade_tools_package(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)

        folder_path = DATA_PATH.joinpath("gapdata")
        prefix = "tools-all-signed"
        # List all files in the "gapdata" folder
        files_in_folder = [file.name for file in folder_path.iterdir() if file.is_file()]
        # Filter files that start with the specified prefix
        tools_package = [file for file in files_in_folder if file.startswith(prefix)]

        tools_package_folder =DATA_PATH.joinpath("gapdata").joinpath(tools_package[0])
        gap.manual_upgrade_valid_package(self, tools_package_folder)    

    @allure.title("GAP Upgrade with invalid format package - smoke test")
    @allure.description(
        "TestType: Negative \n"
        "Target: Check manual upload invalid format package \n"
        "Description: Manual upload invalid format package and check error message in AdminPortal \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-08-29 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2551 \n")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.toolspackage
    @pytest.mark.run(order=100)
    def test_upload_invalid_tools_package(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        invalid_format_package=DATA_PATH.joinpath("gapdata").joinpath("query_for_output.gsql")
        gap.manual_upgrade_invalid_package(self,invalid_format_package)

    @allure.title("GAP Upgrade with unsigned package - smoke test")
    @allure.description(
        "TestType: Negative \n"
        "Target: Check manual upload unsigned package \n"
        "Description: Manual upload unsigned package and check error message in AdminPortal \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-09-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2551 \n")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.toolspackage
    @pytest.mark.run(order=100)
    def test_upload_unsigned_package(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        invalid_format_package=DATA_PATH.joinpath("solution").joinpath("covid19.tar.gz")
        gap.manual_upgrade_invalid_package(self,invalid_format_package)

    @allure.title("GAP Upgrade TigerGraph suite - Check privilige")
    @allure.description(
        "TestType: Negative \n"
        "Target: Check privilege for Tools upgrade \n"
        "Description: Only superuser can view [Upgrade TigerGraph Suites] page \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-09-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2551 \n")
    @parameterized.expand(user_test_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.toolspackage
    @pytest.mark.run(order=100)
    def test_privilege_for_upgrading_page(self, loginway):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self,loginway)
        if loginway == "default_user":
            assert gap.intoUpgradeTGSuite(self) == True
        else:
            assert gap.intoUpgradeTGSuite(self) == False