import pytest
import allure
import os
import logging
import paramiko
from pages.tools.adminportal.adminportal_page import GAPPage
from base.tools_basecase import ToolsBaseCase
LOGGER = logging.getLogger(__name__)

class TestLogManagement(ToolsBaseCase):
    
    def __init__(self, sb):
        """Init all used pages"""
        super().__init__(sb)
        self.sb = sb  

    def setUp(self):
        """Set up test environment"""
        super().setUp()
        if not hasattr(self, 'sb'):
            self.sb = self.get_new_driver()  

    @allure.title("GAP GSQL Output file preview and download - smoke test")
    @allure.description("GSQL Output file preview and download")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gapoutput
    @pytest.mark.gshell_upload
    @pytest.mark.run(order=1000)
    @pytest.mark.offline
    # the file path on k8s changed "/home/<USER>/tigergraph/data/gui/loading_data/"
    def test_GSQL_output_file_preview(self):
        super().setUp()
        gap = GAPPage()
        gap.gsql_output(self)


    @allure.title("Preview and download large files and check service status")
    @allure.description(
        "TestType: Positive \n"
        "Target: Check if user can preview and download large files and verify service status \n"
        "Description: Preview and download query output large files, and check TigerGraph service status \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3651"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.gapoutput1
    @pytest.mark.order
    @pytest.mark.run(order=5102)
    def test_preview_download_and_check_services(self):

        try:
            super().setUp()
            if not hasattr(self, 'sb') or not self.sb:
                self.sb = self.get_new_driver()
            gap = GAPPage()
            file_path = '/home/<USER>/large_file_test.csv'
            gap.GapLogin(self)
            gap.intoGsqlOutput(self)
            gap.download_query_output_large_file(self, file_path)
            
            # Check service status
            cmd = "echo 'tigergraph' | sudo -S gadmin status"
            result = os.popen(cmd).read()
            
            if gap.check_all_services_status(result):
                LOGGER.info("All TigerGraph services are running normally") 
            else:
                LOGGER.error(f"TigerGraph services status abnormal:\n{result}")
                raise Exception("Found TigerGraph services not in Online status")
                
        except Exception as e:
            LOGGER.error(f"Preview and download large files or check service status error: {str(e)}")
            if hasattr(self, 'sb') and self.sb:
                self.attach_allure_screenshot("Preview and download large files and check service status error")
            raise e

