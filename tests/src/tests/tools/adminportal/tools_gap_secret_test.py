import pytest
import allure
from pages.tools.adminportal.adminportal_page import GAPPage
from base.tools_basecase import ToolsBaseCase
from utils.data_util.data_resolver import read_test_data
from locators.tools.admin_portal_locators import GAPSecretLocator
import logging

LOGGER = logging.getLogger(__name__)


class TestGapSecret(ToolsBaseCase):

    tools_cdn = read_test_data(file="tools_test_data.json").get("tools_cdn")

    @allure.title("GAP add secret and request token - smoke test")
    @allure.description("GAP add secret and request token")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gapsecret
    @pytest.mark.onprem
    @pytest.mark.offline
    @pytest.mark.run(order=1011)
    def test_secret_add(self):
        super().setUp()
        gap = GAPPage()
        gap.check_27_env(self, skip_reason="waiting to debug network request on github action")
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        self.wait_for_element_clickable(GAPSecretLocator.newsecret, timeout=60)
        # enlarge the items per page
        gap.enlarge_items_per_page(self)
        # check not created the same secret
        secretlist = self.find_elements("//tr")  # get row of table
        for s in range(len(secretlist)):
            # must get element list again, or report error
            secretlist = self.find_elements("//tr")
            if "test_secret" in secretlist[s].text:
                LOGGER.info("test_secret exits, will remove the secret first")
                self.click("(//button[@id='delete-secret'])[{}]".format(s), timeout=20)
                self.click(GAPSecretLocator.ok, timeout=20)
                gap.wait_progress_bar_when_add_secret(self)
                self.wait_for_element_clickable(GAPSecretLocator.newsecret, timeout=60)  # wait delete
                break
        self.attach_allure_screenshot("clear existed secret screenshot")
        self.type(GAPSecretLocator.newsecret, "test_secret", timeout=20)
        self.wait_for_element_clickable(GAPSecretLocator.addbtn, timeout=60)
        self.click(GAPSecretLocator.addbtn)
        gap.wait_progress_bar_when_add_secret(self)
        self.wait_for_element_clickable(GAPSecretLocator.newsecret, timeout=60) # waiting for generate secret
        gap.enlarge_items_per_page(self)
        self.attach_allure_screenshot("add new secret screenshot")
        secret_val = ""
        s_index = 0
        res = False
        if self.assert_element(GAPSecretLocator.testsecret):
            for s in range(len(secretlist)):
                # must get element list again, or report error
                secretlist = self.find_elements("//tr")
                if "test_secret" in secretlist[s].text:
                    LOGGER.info("get new secret index, and get the secret value")
                    s_index = s
                    el = self.find_elements(GAPSecretLocator.secretvalue)[s_index-1]
                    secret_val = el.text.split(" ")[0]
                    break
            res = gap.request_token(self, secret_val)
        LOGGER.info("gap.request_token:" + str(res))
        if res:
            LOGGER.info("secret test succeed")
            self.attach_allure_screenshot("get token succeed screenshot")
        else:
            self.attach_allure_screenshot("get token failed screenshot")
            self.fail(msg="secret test fail")


