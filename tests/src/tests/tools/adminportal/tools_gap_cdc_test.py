import pytest
import allure
from pages.tools.adminportal.adminportal_page import GAPPage
from base.tools_basecase import ToolsBaseCase

import logging

LOGGER = logging.getLogger(__name__)


class TestGapCdc(ToolsBaseCase):

    @allure.title("GAP CDC config - smoke test")
    @allure.description("GAP set CDC")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gapcdc
    @pytest.mark.preexec
    @pytest.mark.run(order=1)
    def test_cdc_config(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoCDCManegement(self)

    @allure.title("GAP disable CDC config - smoke test")
    @allure.description("GAP disable CDC")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gapcdc
    @pytest.mark.teardown
    @pytest.mark.run(order=1)
    def test_disable_cdc_config(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoCDCManegement(self, disable=True)

