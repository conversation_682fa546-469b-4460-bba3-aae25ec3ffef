import { encodeURLSearchParams, getContrastTextColor, hexToRGBA, stringToColor } from './utils';

test.each([
  {
    hex: '#000000',
    alpha: 1,
    expected: 'rgba(0,0,0,1)',
  },
  {
    hex: '#ffffff',
    alpha: 0.1,
    expected: 'rgba(255,255,255,0.1)',
  },
])('hexToRGBA($hex, $alpha) -> $expected', ({ hex, alpha, expected }) => {
  expect(hexToRGBA(hex, alpha)).toBe(expected);
});

test.each([
  {
    str: 'hello',
    expected: 'rgba(210,24,233,1)',
  },
  {
    str: 'world',
    expected: 'rgba(146,27,193,1)',
  },
])('stringToColor($str) -> $expected', ({ str, expected }) => {
  expect(stringToColor(str)).toBe(expected);
});

test.each([
  {
    color: '#000000',
    expected: 'rgb(255,255,255)',
  },
  {
    color: '#ffffff',
    expected: 'rgb(0,0,0)',
  },
  {
    color: 'error',
    expected: '',
  },
])('getContrastTextColor($color) -> $expected', ({ color, expected }) => {
  expect(getContrastTextColor(color)).toBe(expected);
});

test.each([
  {
    params: new URLSearchParams([
      ['a', '1'],
      ['b', '2'],
    ]),
    expected: 'a=1&b=2',
  },
  {
    params: new URLSearchParams([
      ['a', '2 3'],
      ['b', '2'],
    ]),
    expected: 'a=2%203&b=2',
  },
])('encodeURISearchParams($params) -> expected', ({ params, expected }) => {
  expect(encodeURLSearchParams(params)).toBe(expected);
});
