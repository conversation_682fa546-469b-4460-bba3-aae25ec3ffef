import { Provider as StyletronProvider } from 'styletron-react';
import { Client as Styletron } from 'styletron-engine-atomic';
import React, { FC, PropsWithChildren } from 'react';
import { RenderOptions, render as rtl } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from 'react-query';
import ComponentProvider from '@tigergraph/app-ui-lib/Provider';
import { BrowserRouter } from 'react-router-dom';

const engine = new Styletron();
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 0,
    },
  },
});

const StyletronWrapper: FC<PropsWithChildren> = ({ children }) => (
  <StyletronProvider value={engine}>
    <QueryClientProvider client={queryClient}>
      <ComponentProvider>
        <BrowserRouter>{children}</BrowserRouter>
      </ComponentProvider>
    </QueryClientProvider>
  </StyletronProvider>
);
export const render = (ui: React.ReactNode, options?: Omit<RenderOptions, 'queries'> | undefined) => {
  return rtl(ui, { wrapper: StyletronWrapper, ...options });
};
