import type { ExternalGraph } from '@tigergraph/tools-models/gvis/insights';
import type { VertexType, EdgeType } from '../graph/type';
import { convertSchemaToGraph, getSchemaAttributes } from '../graph/data';
import { Schema } from '../graph/type';
import styleJSO<PERSON> from '../stories/data/style.json';
import schemaJSON from '../stories/data/schema.json';

describe('test for getSchemaAttributes function', () => {
  let vertexTypes: VertexType[], edgeTypes: EdgeType[];
  beforeAll(() => {
    const schemaData = schemaJSON.results;
    vertexTypes = schemaData.VertexTypes as VertexType[];
    edgeTypes = schemaData.EdgeTypes as EdgeType[];
  });

  test('with vertices', () => {
    // Vertex
    // BankAccount
    let attrs = getSchemaAttributes(vertexTypes[0].Attributes);
    expect(Object.keys(attrs).length).toBe(1);
    expect(attrs['open_time']).toBe('DATETIME');

    // CreditCard
    attrs = getSchemaAttributes(vertexTypes[1].Attributes);
    expect(Object.keys(attrs).length).toBe(2);
    expect(attrs['applied_time']).toBe('DATETIME');
    expect(attrs['credit_line']).toBe('DOUBLE');

    // SSN
    attrs = getSchemaAttributes(vertexTypes[2].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // ID
    attrs = getSchemaAttributes(vertexTypes[3].Attributes);
    expect(Object.keys(attrs).length).toBe(1);
    expect(attrs['id_type']).toBe('STRING');

    // Address
    attrs = getSchemaAttributes(vertexTypes[4].Attributes);
    expect(Object.keys(attrs).length).toBe(3);
    expect(attrs['zipcode']).toBe('STRING');
    expect(attrs['latitude']).toBe('DOUBLE');
    expect(attrs['longitude']).toBe('DOUBLE');

    // UnsecuredLoan
    attrs = getSchemaAttributes(vertexTypes[5].Attributes);
    expect(Object.keys(attrs).length).toBe(2);
    expect(attrs['applied_time']).toBe('DATETIME');
    expect(attrs['amount']).toBe('DOUBLE');

    // PhoneNumber
    attrs = getSchemaAttributes(vertexTypes[6].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // MoneyTransfer
    attrs = getSchemaAttributes(vertexTypes[7].Attributes);
    expect(Object.keys(attrs).length).toBe(2);
    expect(attrs['amount']).toBe('DOUBLE');
    expect(attrs['transfer_time']).toBe('DATETIME');

    // FinancialInstitute
    attrs = getSchemaAttributes(vertexTypes[8].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // Purchase
    attrs = getSchemaAttributes(vertexTypes[9].Attributes);
    expect(Object.keys(attrs).length).toBe(3);
    expect(attrs['purchase_time']).toBe('DATETIME');
    expect(attrs['purchase_amount']).toBe('DOUBLE');
    expect(attrs['purchase_time_epoch']).toBe('INT');

    // IP
    attrs = getSchemaAttributes(vertexTypes[10].Attributes);
    expect(Object.keys(attrs).length).toBe(2);
    expect(attrs['latitude']).toBe('DOUBLE');
    expect(attrs['longitude']).toBe('DOUBLE');

    // State
    attrs = getSchemaAttributes(vertexTypes[11].Attributes);
    expect(Object.keys(attrs).length).toBe(2);
    expect(attrs['latitude']).toBe('DOUBLE');
    expect(attrs['longitude']).toBe('DOUBLE');

    // Login
    attrs = getSchemaAttributes(vertexTypes[12].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // Shop
    attrs = getSchemaAttributes(vertexTypes[13].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // Person
    attrs = getSchemaAttributes(vertexTypes[14].Attributes);
    expect(Object.keys(attrs).length).toBe(3);
    expect(attrs['age']).toBe('UINT');
    expect(attrs['gender']).toBe('STRING');
    expect(attrs['annual_salary']).toBe('DOUBLE');
  });

  test('with edges', () => {
    // SEND
    let attrs = getSchemaAttributes(edgeTypes[0].Attributes);
    expect(Object.keys(attrs).length).toBe(2);
    expect(attrs['send_time']).toBe('DATETIME');
    expect(attrs['amount']).toBe('DOUBLE');

    // RECEIVE
    attrs = getSchemaAttributes(edgeTypes[1].Attributes);
    expect(Object.keys(attrs).length).toBe(2);
    expect(attrs['receive_time']).toBe('DATETIME');
    expect(attrs['amount']).toBe('DOUBLE');

    // ISSUED_BY
    attrs = getSchemaAttributes(edgeTypes[2].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // FROM_IP
    attrs = getSchemaAttributes(edgeTypes[3].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // WITH_LOGIN
    attrs = getSchemaAttributes(edgeTypes[4].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // DELIVERED_AT
    attrs = getSchemaAttributes(edgeTypes[5].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // PAID_BY_CARD;
    attrs = getSchemaAttributes(edgeTypes[6].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // LOCATED_IN
    attrs = getSchemaAttributes(edgeTypes[7].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // FOR_SHOP
    attrs = getSchemaAttributes(edgeTypes[8].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // HAS_SSN
    attrs = getSchemaAttributes(edgeTypes[9].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // HAS_CREDIT_CARD
    attrs = getSchemaAttributes(edgeTypes[10].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // HAS_BANK_ACCOUNT
    attrs = getSchemaAttributes(edgeTypes[11].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // HAS_UNSECURED_LOAN
    attrs = getSchemaAttributes(edgeTypes[12].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // HAS_PHONE_NUMBER
    attrs = getSchemaAttributes(edgeTypes[13].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // HAS_ADDRESS
    attrs = getSchemaAttributes(edgeTypes[14].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // HAS_ID
    attrs = getSchemaAttributes(edgeTypes[15].Attributes);
    expect(Object.keys(attrs).length).toBe(0);

    // WITH_PHONE
    attrs = getSchemaAttributes(edgeTypes[16].Attributes);
    expect(Object.keys(attrs).length).toBe(0);
  });
});

describe('test for convertSchemaToGraph function', () => {
  let schemaData = schemaJSON.results;
  let style = styleJSON.results;

  test('with no vertex or edge', () => {
    const emptySchema: Schema = {
      GraphName: 'SchemaGraph',
      VertexTypes: [],
      EdgeTypes: [],
    };
    const graph: ExternalGraph = convertSchemaToGraph(emptySchema);
    expect(graph.nodes.length).toBe(0);
    expect(graph.links.length).toBe(0);
  });

  test('with vertices only', () => {
    const { vertexStyles } = style;
    const { VertexTypes: vertexTypes } = schemaData;
    const schema = {
      GraphName: 'SchemaGraph',
      VertexTypes: vertexTypes.map((v) => ({
        ...v,
        style: vertexStyles[v.Name],
      })),
      EdgeTypes: [],
    };
    const graph: ExternalGraph = convertSchemaToGraph(schema);
    expect(graph.nodes.length).toBe(15);
    expect(graph.links.length).toBe(0);

    // BankAccount
    expect(graph.nodes[0].type).toBe('BankAccount');
    expect(graph.nodes[0].id).toBe('BankAccount');
    expect(Object.keys(graph.nodes[0].attrs).length).toBe(1);
    expect(graph.nodes[0].attrs['open_time']).toBe('DATETIME');

    // CreditCard
    expect(graph.nodes[1].type).toBe('CreditCard');
    expect(graph.nodes[1].id).toBe('CreditCard');
    expect(Object.keys(graph.nodes[1].attrs).length).toBe(2);
    expect(graph.nodes[1].attrs['applied_time']).toBe('DATETIME');
    expect(graph.nodes[1].attrs['credit_line']).toBe('DOUBLE');

    // SSN
    expect(graph.nodes[2].type).toBe('SSN');
    expect(graph.nodes[2].id).toBe('SSN');
    expect(Object.keys(graph.nodes[2].attrs).length).toBe(0);

    // ID
    expect(graph.nodes[3].type).toBe('ID');
    expect(graph.nodes[3].id).toBe('ID');
    expect(Object.keys(graph.nodes[3].attrs).length).toBe(1);
    expect(graph.nodes[3].attrs['id_type']).toBe('STRING');

    // Address
    expect(graph.nodes[4].type).toBe('Address');
    expect(graph.nodes[4].id).toBe('Address');
    expect(Object.keys(graph.nodes[4].attrs).length).toBe(3);
    expect(graph.nodes[4].attrs['zipcode']).toBe('STRING');
    expect(graph.nodes[4].attrs['latitude']).toBe('DOUBLE');
    expect(graph.nodes[4].attrs['longitude']).toBe('DOUBLE');

    // UnsecuredLoan
    expect(graph.nodes[5].type).toBe('UnsecuredLoan');
    expect(graph.nodes[5].id).toBe('UnsecuredLoan');
    expect(Object.keys(graph.nodes[5].attrs).length).toBe(2);
    expect(graph.nodes[5].attrs['applied_time']).toBe('DATETIME');
    expect(graph.nodes[5].attrs['amount']).toBe('DOUBLE');

    // PhoneNumber
    expect(graph.nodes[6].type).toBe('PhoneNumber');
    expect(graph.nodes[6].id).toBe('PhoneNumber');
    expect(Object.keys(graph.nodes[6].attrs).length).toBe(0);

    // MoneyTransfer
    expect(graph.nodes[7].type).toBe('MoneyTransfer');
    expect(graph.nodes[7].id).toBe('MoneyTransfer');
    expect(Object.keys(graph.nodes[7].attrs).length).toBe(2);
    expect(graph.nodes[7].attrs['amount']).toBe('DOUBLE');
    expect(graph.nodes[7].attrs['transfer_time']).toBe('DATETIME');

    // FinancialInstitute
    expect(graph.nodes[8].type).toBe('FinancialInstitute');
    expect(graph.nodes[8].id).toBe('FinancialInstitute');
    expect(Object.keys(graph.nodes[8].attrs).length).toBe(0);

    // Purchase
    expect(graph.nodes[9].type).toBe('Purchase');
    expect(graph.nodes[9].id).toBe('Purchase');
    expect(Object.keys(graph.nodes[9].attrs).length).toBe(3);
    expect(graph.nodes[9].attrs['purchase_time']).toBe('DATETIME');
    expect(graph.nodes[9].attrs['purchase_amount']).toBe('DOUBLE');
    expect(graph.nodes[9].attrs['purchase_time_epoch']).toBe('INT');

    // IP
    expect(graph.nodes[10].type).toBe('IP');
    expect(graph.nodes[10].id).toBe('IP');
    expect(Object.keys(graph.nodes[10].attrs).length).toBe(2);
    expect(graph.nodes[10].attrs['latitude']).toBe('DOUBLE');
    expect(graph.nodes[10].attrs['longitude']).toBe('DOUBLE');

    // State
    expect(graph.nodes[11].type).toBe('State');
    expect(graph.nodes[11].id).toBe('State');
    expect(Object.keys(graph.nodes[11].attrs).length).toBe(2);
    expect(graph.nodes[11].attrs['latitude']).toBe('DOUBLE');
    expect(graph.nodes[11].attrs['longitude']).toBe('DOUBLE');

    // Login
    expect(graph.nodes[12].type).toBe('Login');
    expect(graph.nodes[12].id).toBe('Login');
    expect(Object.keys(graph.nodes[12].attrs).length).toBe(0);

    // Shop
    expect(graph.nodes[13].type).toBe('Shop');
    expect(graph.nodes[13].id).toBe('Shop');
    expect(Object.keys(graph.nodes[13].attrs).length).toBe(0);

    // Person
    expect(graph.nodes[14].type).toBe('Person');
    expect(graph.nodes[14].id).toBe('Person');
    expect(Object.keys(graph.nodes[14].attrs).length).toBe(3);
    expect(graph.nodes[14].attrs['age']).toBe('UINT');
    expect(graph.nodes[14].attrs['gender']).toBe('STRING');
    expect(graph.nodes[14].attrs['annual_salary']).toBe('DOUBLE');
  });

  test('with vertices and edges', () => {
    const { vertexStyles, edgeStyles } = style;
    const { VertexTypes: vertexTypes, EdgeTypes: edegeTypes } = schemaData;
    const schema = {
      GraphName: 'SchemaGraph',
      VertexTypes: vertexTypes.map((v) => ({
        ...v,
        style: vertexStyles[v.Name],
      })),
      EdgeTypes: edegeTypes.map((e) => ({
        ...e,
        style: edgeStyles[e.Name],
      })),
    };
    const graph = convertSchemaToGraph(schema);
    expect(graph.nodes.length).toBe(15);
    expect(graph.links.length).toBe(18);

    // Vertex
    // BankAccount
    expect(graph.nodes[0].type).toBe('BankAccount');
    expect(graph.nodes[0].id).toBe('BankAccount');
    expect(Object.keys(graph.nodes[0].attrs).length).toBe(1);
    expect(graph.nodes[0].attrs['open_time']).toBe('DATETIME');

    // CreditCard
    expect(graph.nodes[1].type).toBe('CreditCard');
    expect(graph.nodes[1].id).toBe('CreditCard');
    expect(Object.keys(graph.nodes[1].attrs).length).toBe(2);
    expect(graph.nodes[1].attrs['applied_time']).toBe('DATETIME');
    expect(graph.nodes[1].attrs['credit_line']).toBe('DOUBLE');

    // SSN
    expect(graph.nodes[2].type).toBe('SSN');
    expect(graph.nodes[2].id).toBe('SSN');
    expect(Object.keys(graph.nodes[2].attrs).length).toBe(0);

    // ID
    expect(graph.nodes[3].type).toBe('ID');
    expect(graph.nodes[3].id).toBe('ID');
    expect(Object.keys(graph.nodes[3].attrs).length).toBe(1);
    expect(graph.nodes[3].attrs['id_type']).toBe('STRING');

    // Address
    expect(graph.nodes[4].type).toBe('Address');
    expect(graph.nodes[4].id).toBe('Address');
    expect(Object.keys(graph.nodes[4].attrs).length).toBe(3);
    expect(graph.nodes[4].attrs['zipcode']).toBe('STRING');
    expect(graph.nodes[4].attrs['latitude']).toBe('DOUBLE');
    expect(graph.nodes[4].attrs['longitude']).toBe('DOUBLE');

    // UnsecuredLoan
    expect(graph.nodes[5].type).toBe('UnsecuredLoan');
    expect(graph.nodes[5].id).toBe('UnsecuredLoan');
    expect(Object.keys(graph.nodes[5].attrs).length).toBe(2);
    expect(graph.nodes[5].attrs['applied_time']).toBe('DATETIME');
    expect(graph.nodes[5].attrs['amount']).toBe('DOUBLE');

    // PhoneNumber
    expect(graph.nodes[6].type).toBe('PhoneNumber');
    expect(graph.nodes[6].id).toBe('PhoneNumber');
    expect(Object.keys(graph.nodes[6].attrs).length).toBe(0);

    // MoneyTransfer
    expect(graph.nodes[7].type).toBe('MoneyTransfer');
    expect(graph.nodes[7].id).toBe('MoneyTransfer');
    expect(Object.keys(graph.nodes[7].attrs).length).toBe(2);
    expect(graph.nodes[7].attrs['amount']).toBe('DOUBLE');
    expect(graph.nodes[7].attrs['transfer_time']).toBe('DATETIME');

    // FinancialInstitute
    expect(graph.nodes[8].type).toBe('FinancialInstitute');
    expect(graph.nodes[8].id).toBe('FinancialInstitute');
    expect(Object.keys(graph.nodes[8].attrs).length).toBe(0);

    // Purchase
    expect(graph.nodes[9].type).toBe('Purchase');
    expect(graph.nodes[9].id).toBe('Purchase');
    expect(Object.keys(graph.nodes[9].attrs).length).toBe(3);
    expect(graph.nodes[9].attrs['purchase_time']).toBe('DATETIME');
    expect(graph.nodes[9].attrs['purchase_amount']).toBe('DOUBLE');
    expect(graph.nodes[9].attrs['purchase_time_epoch']).toBe('INT');

    // IP
    expect(graph.nodes[10].type).toBe('IP');
    expect(graph.nodes[10].id).toBe('IP');
    expect(Object.keys(graph.nodes[10].attrs).length).toBe(2);
    expect(graph.nodes[10].attrs['latitude']).toBe('DOUBLE');
    expect(graph.nodes[10].attrs['longitude']).toBe('DOUBLE');

    // State
    expect(graph.nodes[11].type).toBe('State');
    expect(graph.nodes[11].id).toBe('State');
    expect(Object.keys(graph.nodes[11].attrs).length).toBe(2);
    expect(graph.nodes[11].attrs['latitude']).toBe('DOUBLE');
    expect(graph.nodes[11].attrs['longitude']).toBe('DOUBLE');

    // Login
    expect(graph.nodes[12].type).toBe('Login');
    expect(graph.nodes[12].id).toBe('Login');
    expect(Object.keys(graph.nodes[12].attrs).length).toBe(0);

    // Shop
    expect(graph.nodes[13].type).toBe('Shop');
    expect(graph.nodes[13].id).toBe('Shop');
    expect(Object.keys(graph.nodes[13].attrs).length).toBe(0);

    // Person
    expect(graph.nodes[14].type).toBe('Person');
    expect(graph.nodes[14].id).toBe('Person');
    expect(Object.keys(graph.nodes[14].attrs).length).toBe(3);
    expect(graph.nodes[14].attrs['age']).toBe('UINT');
    expect(graph.nodes[14].attrs['gender']).toBe('STRING');
    expect(graph.nodes[14].attrs['annual_salary']).toBe('DOUBLE');

    // Edge
    // SEND
    expect(graph.links[0].type).toBe('SEND');
    expect(graph.links[0].directed).toBeTruthy();
    expect(graph.links[0].source.type).toBe('BankAccount');
    expect(graph.links[0].source.id).toBe('BankAccount');
    expect(graph.links[0].target.type).toBe('MoneyTransfer');
    expect(graph.links[0].target.id).toBe('MoneyTransfer');
    expect(Object.keys(graph.links[0].attrs).length).toBe(4);
    expect(graph.links[0].attrs['send_time']).toBe('DATETIME');
    expect(graph.links[0].attrs['amount']).toBe('DOUBLE');

    // RECEIVE
    expect(graph.links[1].type).toBe('RECEIVE');
    expect(graph.links[1].directed).toBeTruthy();
    expect(graph.links[1].source.type).toBe('MoneyTransfer');
    expect(graph.links[1].source.id).toBe('MoneyTransfer');
    expect(graph.links[1].target.type).toBe('BankAccount');
    expect(graph.links[1].target.id).toBe('BankAccount');
    expect(Object.keys(graph.links[1].attrs).length).toBe(5);
    expect(graph.links[1].attrs['receive_time']).toBe('DATETIME');
    expect(graph.links[1].attrs['amount']).toBe('DOUBLE');

    // ISSUED_BY
    expect(graph.links[2].type).toBe('ISSUED_BY');
    expect(graph.links[2].directed).toBeTruthy();
    expect(graph.links[2].source.type).toBe('BankAccount');
    expect(graph.links[2].source.id).toBe('BankAccount');
    expect(graph.links[2].target.type).toBe('FinancialInstitute');
    expect(graph.links[2].target.id).toBe('FinancialInstitute');
    expect(Object.keys(graph.links[2].attrs).length).toBe(3);

    // FROM_IP
    expect(graph.links[3].type).toBe('FROM_IP');
    expect(graph.links[3].directed).toBeTruthy();
    expect(graph.links[3].source.type).toBe('Purchase');
    expect(graph.links[3].source.id).toBe('Purchase');
    expect(graph.links[3].target.type).toBe('IP');
    expect(graph.links[3].target.id).toBe('IP');
    expect(Object.keys(graph.links[3].attrs).length).toBe(3);

    // WITH_LOGIN
    expect(graph.links[4].type).toBe('WITH_LOGIN');
    expect(graph.links[4].directed).toBeTruthy();
    expect(graph.links[4].source.type).toBe('Purchase');
    expect(graph.links[4].source.id).toBe('Purchase');
    expect(graph.links[4].target.type).toBe('Login');
    expect(graph.links[4].target.id).toBe('Login');
    expect(Object.keys(graph.links[4].attrs).length).toBe(3);

    // DELIVERED_AT
    expect(graph.links[5].type).toBe('DELIVERED_AT');
    expect(graph.links[5].directed).toBeTruthy();
    expect(graph.links[5].source.type).toBe('Purchase');
    expect(graph.links[5].source.id).toBe('Purchase');
    expect(graph.links[5].target.type).toBe('Address');
    expect(graph.links[5].target.id).toBe('Address');
    expect(Object.keys(graph.links[5].attrs).length).toBe(3);

    // PAID_BY_CARD
    expect(graph.links[6].type).toBe('PAID_BY_CARD');
    expect(graph.links[6].directed).toBeTruthy();
    expect(graph.links[6].source.type).toBe('Purchase');
    expect(graph.links[6].source.id).toBe('Purchase');
    expect(graph.links[6].target.type).toBe('CreditCard');
    expect(graph.links[6].target.id).toBe('CreditCard');
    expect(Object.keys(graph.links[6].attrs).length).toBe(3);

    // LOCATED_IN
    expect(graph.links[7].type).toBe('LOCATED_IN');
    expect(graph.links[7].directed).toBeTruthy();
    expect(graph.links[7].source.type).toBe('Address');
    expect(graph.links[7].source.id).toBe('Address');
    expect(graph.links[7].target.type).toBe('State');
    expect(graph.links[7].target.id).toBe('State');
    expect(Object.keys(graph.links[7].attrs).length).toBe(3);

    // LOCATED_IN
    expect(graph.links[8].type).toBe('LOCATED_IN');
    expect(graph.links[8].directed).toBeTruthy();
    expect(graph.links[8].source.type).toBe('IP');
    expect(graph.links[8].source.id).toBe('IP');
    expect(graph.links[8].target.type).toBe('State');
    expect(graph.links[8].target.id).toBe('State');
    expect(Object.keys(graph.links[8].attrs).length).toBe(3);

    // FOR_SHOP
    expect(graph.links[9].type).toBe('FOR_SHOP');
    expect(graph.links[9].directed).toBeTruthy();
    expect(graph.links[9].source.type).toBe('Login');
    expect(graph.links[9].source.id).toBe('Login');
    expect(graph.links[9].target.type).toBe('Shop');
    expect(graph.links[9].target.id).toBe('Shop');
    expect(Object.keys(graph.links[9].attrs).length).toBe(3);

    // HAS_SSN
    expect(graph.links[10].type).toBe('HAS_SSN');
    expect(graph.links[10].directed).toBeTruthy();
    expect(graph.links[10].source.type).toBe('Person');
    expect(graph.links[10].source.id).toBe('Person');
    expect(graph.links[10].target.type).toBe('SSN');
    expect(graph.links[10].target.id).toBe('SSN');
    expect(Object.keys(graph.links[10].attrs).length).toBe(3);

    // HAS_CREDIT_CARD
    expect(graph.links[11].type).toBe('HAS_CREDIT_CARD');
    expect(graph.links[11].directed).toBeTruthy();
    expect(graph.links[11].source.type).toBe('Person');
    expect(graph.links[11].source.id).toBe('Person');
    expect(graph.links[11].target.type).toBe('CreditCard');
    expect(graph.links[11].target.id).toBe('CreditCard');
    expect(Object.keys(graph.links[11].attrs).length).toBe(3);

    // HAS_BANK_ACCOUNT
    expect(graph.links[12].type).toBe('HAS_BANK_ACCOUNT');
    expect(graph.links[12].directed).toBeTruthy();
    expect(graph.links[12].source.type).toBe('Person');
    expect(graph.links[12].source.id).toBe('Person');
    expect(graph.links[12].target.type).toBe('BankAccount');
    expect(graph.links[12].target.id).toBe('BankAccount');
    expect(Object.keys(graph.links[12].attrs).length).toBe(3);

    // HAS_UNSECURED_LOAN
    expect(graph.links[13].type).toBe('HAS_UNSECURED_LOAN');
    expect(graph.links[13].directed).toBeTruthy();
    expect(graph.links[13].source.type).toBe('Person');
    expect(graph.links[13].source.id).toBe('Person');
    expect(graph.links[13].target.type).toBe('UnsecuredLoan');
    expect(graph.links[13].target.id).toBe('UnsecuredLoan');
    expect(Object.keys(graph.links[13].attrs).length).toBe(3);

    // HAS_PHONE_NUMBER
    expect(graph.links[14].type).toBe('HAS_PHONE_NUMBER');
    expect(graph.links[14].directed).toBeTruthy();
    expect(graph.links[14].source.type).toBe('Person');
    expect(graph.links[14].source.id).toBe('Person');
    expect(graph.links[14].target.type).toBe('PhoneNumber');
    expect(graph.links[14].target.id).toBe('PhoneNumber');
    expect(Object.keys(graph.links[14].attrs).length).toBe(3);

    // HAS_ADDRESS
    expect(graph.links[15].type).toBe('HAS_ADDRESS');
    expect(graph.links[15].directed).toBeTruthy();
    expect(graph.links[15].source.type).toBe('Person');
    expect(graph.links[15].source.id).toBe('Person');
    expect(graph.links[15].target.type).toBe('Address');
    expect(graph.links[15].target.id).toBe('Address');
    expect(Object.keys(graph.links[15].attrs).length).toBe(3);

    // HAS_ID
    expect(graph.links[16].type).toBe('HAS_ID');
    expect(graph.links[16].directed).toBeTruthy();
    expect(graph.links[16].source.type).toBe('Person');
    expect(graph.links[16].source.id).toBe('Person');
    expect(graph.links[16].target.type).toBe('ID');
    expect(graph.links[16].target.id).toBe('ID');
    expect(Object.keys(graph.links[16].attrs).length).toBe(3);

    // WITH_PHONE
    expect(graph.links[17].type).toBe('WITH_PHONE');
    expect(graph.links[17].directed).toBeTruthy();
    expect(graph.links[17].source.type).toBe('MoneyTransfer');
    expect(graph.links[17].source.id).toBe('MoneyTransfer');
    expect(graph.links[17].target.type).toBe('PhoneNumber');
    expect(graph.links[17].target.id).toBe('PhoneNumber');
    expect(Object.keys(graph.links[17].attrs).length).toBe(3);
  });
});
