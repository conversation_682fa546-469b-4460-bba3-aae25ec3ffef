import { GlobalInputProps } from '../type';
import { DatetimePickerInput } from '@src/insights/components/DatetimePicker';

export default function GlobalDatePicker(props: GlobalInputProps) {
  const { value: globalValue, inputState, onChangeInputValue } = props;

  const handleChange = (value: string) => {
    onChangeInputValue({
      ...globalValue,
      type: 'DATETIME',
      value,
    });
  };

  return (
    <div>
      <DatetimePickerInput placeholder={inputState.placeholder} showTimeSelect value={globalValue.value as string} onChange={handleChange} />
    </div>
  );
}
