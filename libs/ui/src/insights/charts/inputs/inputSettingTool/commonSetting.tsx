import { useMemo } from 'react';
import { StyledInput } from '../../../components';
import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import { DataType, InputState, InputWidgetType } from '../type';
import { Controller, useFormContext, useWatch } from 'react-hook-form';
import { GlobalParams } from '../../../chart/globalParams';
import { VariableNameSelector } from './components/VariableNameSelector';
import { GlobalVariables } from '../../../chart/globalVariables';
import { InputTypeSelector } from './components/InputTypeSelector';
import { DataTypeSelector } from './components/DataTypeSelector';

export interface SettingProps {
  variableID: string;
  inputState: InputState;
  globalParameters: GlobalParams;
  globalVariables: GlobalVariables;
  onChangeInputState: (input: InputState) => void;
}

export default function CommonSetting(props: SettingProps) {
  const { inputState, variableID, globalParameters, globalVariables, onChangeInputState } = props;
  const form = useFormContext<{ variableName: string; inputType: string; dataType: string; globalVariableID: string; label: string }>();
  const { control } = form;
  const inputType = useWatch({ control, name: 'inputType' });
  const dataType = useWatch({ control, name: 'dataType' });

  const needPlaceholder = useMemo(() => {
    if (inputType === InputWidgetType.MAP || inputType === InputWidgetType.LIST) {
      return false;
    }
    if (dataType === DataType.Bool) {
      return false;
    }
    return true;
  }, [inputType, dataType]);

  return (
    <>
      {/* FIXME form type */}
      <VariableNameSelector
        form={form as any}
        variableID={variableID}
        globalParameters={globalParameters}
        globalVariables={globalVariables}
      />
      <ChartSettingItemContainer label="Input Label (Optional)">
        <Controller name="label" control={control} render={({ field: { value, ref, ...field } }) => (
          <StyledInput
            inputRef={ref}
            {...field}
            value={value ?? ''}
            placeholder="Enter input label"
          />
        )} />
      </ChartSettingItemContainer>
      {/* FIXME form type */}
      <InputTypeSelector
        form={form as any}
        globalVariables={globalVariables}
      />
      {/* FIXME form type */}
      <DataTypeSelector form={form as any} />
      {needPlaceholder && (
        <ChartSettingItemContainer label="Placeholder">
          <StyledInput
            value={inputState.placeholder ?? ''}
            onChange={(e) => {
              const newInputState = {
                ...inputState,
                placeholder: e.target['value'],
              };
              onChangeInputState(newInputState);
            }}
            placeholder="Enter placeholder"
          />
        </ChartSettingItemContainer>
      )}
    </>
  );
}
