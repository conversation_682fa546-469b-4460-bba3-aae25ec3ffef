import { clone, isUndefined, omitBy, pick, upperCase } from 'lodash';
import { DataType, InputState, InputWidgetType } from '../../type';
import { GlobalParam } from '../../../../chart/globalParams';
import { InputFormDatatype } from '../types';
import {
  HybridFormMapItemType,
  HybridFormPrimitiveItemType,
  HybridFormVertexItemType,
} from '../../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/types';

const compareDataType = (src: string, tar: string) => upperCase(src) === upperCase(tar);

export const handleForm2InputStateGlobalParam = (
  inputState: InputState,
  globalParam: GlobalParam,
  values: InputFormDatatype
) => {
  const draftInputState = clone(inputState);
  const globalVariableID = values.globalVariableID;
  let draftGlobalParams = clone(globalParam);

  draftInputState.name = values.variableName;
  draftInputState.label = values.label;
  draftInputState.widgetType = values.inputType;
  draftInputState.dataType = values.dataType as DataType;

  draftGlobalParams.name = values.variableName;
  draftGlobalParams.type = upperCase(values.dataType) as GlobalParam['type'];

  switch (draftInputState.widgetType) {
    case InputWidgetType.SLIDER:
    case InputWidgetType.INPUT: {
      // for input vertex
      if (compareDataType(draftInputState.dataType, DataType.Vertex)) {
        draftInputState.selectedGraph = values.selectedGraph;
        draftInputState.settings.fixVertexType = values.fixVertexType;
        draftInputState.vertexType = values.vertexType;
        Object.assign(draftGlobalParams, {
          value: { vertexType: values.item?.type, vertexID: values.item?.id },
        });
      } else if (compareDataType(draftInputState.dataType, DataType.Number)) {
        Object.assign(
          draftInputState.settings,
          omitBy(
            pick(values, ['min', 'max', 'step']),
            isUndefined
          )
        );
        Object.assign(draftGlobalParams, {
          type: 'NUMBER',
          value: values.defaultInputValue === '' ? '' : Number(values.defaultInputValue),
        });
      } else {
        draftGlobalParams.value = String(values.defaultInputValue);
      }

      break;
    }
    case InputWidgetType.DROPDOWN: {
      // for dropdown
      // for dropdown vertex
      if (compareDataType(draftInputState.dataType, DataType.Vertex)) {
        // TODO default dropdown vertex
        draftInputState.selectedGraph = values.selectedGraph;
      }

      if (values?.fixVertexType && values?.vertexType) {
        draftInputState.vertexType = values.vertexType;
      } else {
        draftInputState.vertexType = undefined;
      }

      // user custom options
      if (!draftInputState.settings?.useQuery && Array.isArray(values.settings?.options)) {
        draftInputState.settings.options = values.settings.options.map((o) => ({
          // isCreatable -> compatible with old data
          isCreatable: true,
          label: o.label,
          value: o.value,
        }));
      }

      if (draftInputState.settings.multi) {
        Object.assign(draftGlobalParams, {
          type: 'LIST',
          elementType: upperCase(values.dataType),
        });
      }

      draftGlobalParams.value = values.defaultDropdownValue;

      break;
    }
    case InputWidgetType.LIST: {
      draftGlobalParams.type = 'LIST';
      if (values?.selectedGraph) {
        draftInputState.selectedGraph = values.selectedGraph;
      }

      if (values?.fixVertexType && values?.vertexType) {
        draftInputState.vertexType = values.vertexType;
      } else {
        draftInputState.vertexType = undefined;
      }
      const elementType = draftInputState.dataType;

      if (compareDataType(elementType, DataType.Vertex)) {
        Object.assign(draftGlobalParams, {
          elementType,
          value: ((values.items || []) as HybridFormVertexItemType[]).map((i) => ({
            vertexType: i.type,
            vertexID: i.id,
          })),
        });
      } else {
        Object.assign(draftGlobalParams, {
          elementType,
          value: ((values.items || []) as HybridFormPrimitiveItemType[]).map((i) => i.value ?? ''),
        });
      }

      break;
    }
    case InputWidgetType.MAP: {
      if (values.keyType && values.valueType) {
        draftInputState.keyType = upperCase(values.keyType);
        draftInputState.valueType = upperCase(values.valueType);
      }

      Object.assign(draftGlobalParams, {
        type: 'MAP',
        keyType: draftInputState.keyType,
        valueType: draftInputState.valueType,
        value: (values.items as HybridFormMapItemType[]).map((v) => ({ key: v.key ?? '', value: v.value ?? '' })),
      });

      break;
    }
    default:
      break;
  }

  if (globalVariableID) {
    draftInputState.globalVariableID = globalVariableID;
    draftGlobalParams = undefined;
  } else {
    draftInputState.globalVariableID = undefined;
  }

  return {
    draftInputState,
    draftGlobalParams,
  };
};
