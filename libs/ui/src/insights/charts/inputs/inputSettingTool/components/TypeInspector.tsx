import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { InputWidgetType, DataType } from '../../type';
import { lowerCase, map, upperCase } from 'lodash';
import { useMemo } from 'react';

const getTypeFromDataType = (dataType: string, labelMapper?: Record<string, string>) => {
  switch (lowerCase(dataType)) {
    case DataType.Datetime:
      return labelMapper?.[DataType.Datetime] ?? 'DATETIME';
    case DataType.Number:
      return labelMapper?.[DataType.Number] ?? 'INT/INT64/UINT/DOUBLE/FLOAT';
    case DataType.Vertex:
      return labelMapper?.[DataType.Vertex] ?? 'VERTEX';
    case DataType.Bool:
    case 'boolean':
      return labelMapper?.[DataType.Bool] ?? 'BOOL';
    case DataType.String:
    default:
      return labelMapper?.[DataType.String] ?? 'STRING';
  }
};

export const getTypeLabel = ({
  dataType,
  keyType,
  valueType,
  elementType,
  vertexType,
  labelMapper = {},
}: {
  // dataType  list/map/string/number/bool/string
  dataType: string;

  // map
  keyType?: string;
  valueType?: string;

  // list
  elementType?: string;

  // list or single vertex
  vertexType?: string;

  // labelMapper
  labelMapper?: Record<string, string>;
}) => {
  const mainTypeLabel = labelMapper[upperCase(dataType)];

  switch (upperCase(dataType)) {
    case 'MAP':
      return `${mainTypeLabel ?? 'MapAccum'}<${upperCase(keyType)}, ${upperCase(valueType)}>`;
    case 'LIST': {
      const mapLabel = mainTypeLabel ?? 'ListAccum';
      const vertexLabel = labelMapper['VERTEX'] ?? 'Vertex';
      const isNumberList = lowerCase(elementType) === DataType.Number;
      if (elementType === 'VERTEX') {
        if (vertexType) {
          return `${mapLabel}<${vertexLabel}<${vertexType}>>`;
        }
        return `${mapLabel}<${vertexLabel}>`;
      }
      return `${mapLabel}<${getTypeFromDataType(
        elementType,
        isNumberList ? { [DataType.Number]: 'INT/UINT/DOUBLE/FLOAT', ...labelMapper } : labelMapper
      )}>`;
    }
    case 'VERTEX': {
      const vertexLabel = labelMapper['VERTEX'] ?? 'Vertex';
      if (vertexType) {
        return `${vertexLabel}<${vertexType}>`;
      }
      return vertexLabel;
    }
    default:
      return getTypeFromDataType(dataType, labelMapper);
  }
};

const getTypeLabelFromInput = ({ inputType, keyType, valueType, dataType, vertexType, isMultiple }) => {
  switch (inputType) {
    case InputWidgetType.INPUT: {
      return getTypeFromDataType(dataType);
    }
    case InputWidgetType.MAP: {
      return getTypeLabel({ dataType: 'MAP', keyType, valueType });
    }
    case InputWidgetType.LIST: {
      return getTypeLabel({ dataType: 'LIST', elementType: dataType, vertexType });
    }
    case InputWidgetType.DROPDOWN: {
      if (isMultiple) {
        return getTypeLabel({ dataType: 'LIST', elementType: dataType, vertexType });
      }
      return getTypeFromDataType(dataType);
    }
    case InputWidgetType.SLIDER:
      return 'INT/INT64/UINT';
    case InputWidgetType.DATEPICKER:
      return 'DATETIME';
    case InputWidgetType.TEXT:
    default:
      return 'STRING';
  }
};

const TypeDisplay = ({ type }: { type: string }) => {
  const [css] = useStyletron();
  return (
    <div
      className={css({
        padding: '8px 0',
        fontSize: '12px',
        fontWeight: 500,
        lineHeight: '16px',
        color: '#656565',
        borderTop: '1px solid #DDDDDE',
      })}
    >
      Corresponding Type in GSQL Query: <span className={css({ color: '#2C3237' })}>{type}</span>
      <span></span>
    </div>
  );
};

export const TypeInspectorForInput = ({
  // dataType
  dataType,

  inputType,
  // map
  keyType,
  valueType,

  // list or single vertex
  vertexType,

  // list
  isMultiple,
}) => {
  const type = getTypeLabelFromInput({ inputType, keyType, valueType, dataType, vertexType, isMultiple });

  return <TypeDisplay type={type} />;
};

export const TypeInspector = ({
  // dataType  list/map/string/number/bool
  dataType,

  // map
  keyType,
  valueType,

  // list
  elementType,

  // list or single vertex
  vertexType,
}) => {
  const type = useMemo(() => {
    return getTypeLabel({ dataType, keyType, valueType, elementType, vertexType });
  }, [dataType, keyType, valueType, elementType, vertexType]);

  return <TypeDisplay type={type} />;
};
