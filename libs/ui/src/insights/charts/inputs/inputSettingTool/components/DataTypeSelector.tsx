import { Controller, UseFormReturn, useWatch } from 'react-hook-form';
import { DataType, InputWidgetType } from '../../type';
import { ChartSettingItemContainer } from '../../../chartTools/chartSetting/chartSettingItemContainer';
import { StyledSelect } from '../../../../components';
import { lowerCase } from 'lodash';
import { useMemo } from 'react';

export const DataTypeSelector = ({
  form,
}: {
  form: UseFormReturn<{ globalVariableID: string; inputType: string; dataType: string }>;
}) => {
  const { control } = form;
  const globalVariableID = useWatch({ control, name: 'globalVariableID' });
  const inputType = useWatch({ control, name: 'inputType' });

  const options = useMemo(() => {
    let res = Object.keys(DataType).map((type) => ({ id: DataType[type], label: type }));

    if (inputType === InputWidgetType.INPUT) {
      res = res.filter((item) => item.id !== DataType.Bool);
    }

    return res;
  }, [inputType]);

  if (globalVariableID) {
    return null;
  }

  if (![InputWidgetType.INPUT, InputWidgetType.DROPDOWN, InputWidgetType.LIST].includes(inputType as InputWidgetType)) {
    return null;
  }

  return (
    <ChartSettingItemContainer label="Data Type">
      <Controller
        control={control}
        name="dataType"
        render={({ field: { value, ref, onChange, ...field } }) => (
          <StyledSelect
            clearable={false}
            searchable={false}
            {...field}
            inputRef={ref}
            value={[{ id: lowerCase(value) }]}
            options={options}
            onChange={(params) => {
              const dataType = String(params.value?.[0]?.id ?? '');
              if (dataType) {
                onChange(dataType);
              }
            }}
            placeholder="Select data type"
            labelKey="label"
          />
        )}
      />
    </ChartSettingItemContainer>
  );
};
