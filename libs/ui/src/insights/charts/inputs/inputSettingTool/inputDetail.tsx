import { useMemo, useState } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { DataType, GlobalInputProps, InputWidgetType } from '../type';
import CommonSetting from './commonSetting';
import DefaultInputSetting from './defaultInputSetting';
import DefaultSelectSetting from './defaultSelectSetting';
import GlobalTextArea from '../inputContainer/globalTextArea';
import GlobalDatePicker from '../inputContainer/globalDatePicker';
import GlobalDatetimePicker from '../inputContainer/globalDatetimePicker';
import ConfirmButtons from '../../../components/confirmButtons';
import { useGSQLQuery } from '../../query/useGSQLQuery';
import { useQueries, useGraphList, useSchema } from '../../../pages/chart/useMetaHook';
import GlobalSearch from './globalSearch';
import { showToast } from '../../../components/styledToasterContainer';
import { KIND } from 'baseui/toast';
import { AxiosError } from 'axios';
import { SearchItem } from '../../chartSlice';
import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import { GlobalParam, GlobalParams, GlobalVariables } from '../../../chart';
import { getErrorMessage } from '../../../components/error';
import { getParamTypeDefaultValue } from '../../../chart/globalParams';
import { TypeInspector, TypeInspectorForInput } from './components/TypeInspector';
import DefaultListSetting from './defaultListSetting';
import { FormProvider, useForm } from 'react-hook-form';
import DefaultMapSetting from './defaultMapSetting';
import { handleForm2InputStateGlobalParam } from './utils/form2InputStateGlobalParam';
import { VertexTypeSelector } from './components/VertexTypeSelector';
import { initializeFormData } from './utils/initializeForm';
import { useGlobalVariables2FormState } from './hooks/useGlobalVariables2FormState';

interface InputDetailProps extends GlobalInputProps {
  globalVariables?: GlobalVariables;
  globalParameters: GlobalParams;
  graphName: string;
  close?: () => void;
}

export default function InputDetail(props: InputDetailProps) {
  const [css] = useStyletron();

  const {
    globalParam,
    graphName: initialGraphName,
    chartProps,
    globalParameters,
    globalVariables,
    onChangeGlobalParameter,
    onChangeInputState,
    close,
  } = props;
  const form = useForm({
    defaultValues: initializeFormData({ inputState: props.inputState, globalParam, globalParameters, globalVariables }),
  });
  const { watch } = form;

  const { baseURL, isClusterMode } = chartProps;

  const [inputState, setInputState] = useState(props.inputState);
  const [inputValue, setInputValue] = useState(globalParam);

  const keyType = watch('keyType');
  const valueType = watch('valueType');
  const vertexType = watch('vertexType');
  const dataType = watch('dataType');
  const globalVariableID = watch('globalVariableID');
  const inputType = watch('inputType');
  const elementType = useMemo(() => {
    if (inputType === InputWidgetType.LIST) {
      return dataType;
    }
  }, [inputType, dataType]);
  const globalVariable = useMemo(() => {
    if (!globalVariableID) {
      return undefined;
    }
    return Object.values(globalVariables || {}).find((i) => i.id === globalVariableID);
  }, [globalVariableID, globalVariables]);

  const { settings } = inputState;
  const searchPattern = settings['searchPattern'] ?? [];
  const patternLimit = settings['patternLimit'] ?? 500;
  const hopLimit = settings['hopLimit'] ?? 5;
  const query = settings['query'] ?? '';
  const timeLimit = settings['timeLimit'] ?? 1000;
  const memoryLimit = settings['memoryLimit'] ?? 1000;
  const staticData = settings['staticData'];
  const queryType = settings['queryType'] ?? 'pattern';
  const graphName = settings['graphName'] ?? initialGraphName;

  const [cachedQuery, setCachedQuery] = useState(query);
  const [cachedSearchPattern, setCachedSearchPattern] = useState(searchPattern);
  const [cachedPatternLimit, setCachedPatternLimit] = useState(patternLimit);

  const {
    schema,
    isLoading: isLoadingSchema,
    isError: isSchemaError,
    error: schemaError,
  } = useSchema(baseURL, graphName);
  const {
    queries,
    isLoading: isLoadingQueries,
    isError: isQueriesError,
    error: queriesError,
  } = useQueries(baseURL, graphName);
  const graphs = useGraphList(baseURL);

  const { error, chartData, refetch } = useGSQLQuery({
    baseURL,
    cache: false,
    force: false,
    queryType,
    searchPattern: settings['useQuery'] ? cachedSearchPattern : [],
    query: settings['useQuery'] ? cachedQuery : '',
    staticData: staticData,
    patternLimit: cachedPatternLimit,
    globalParameters,
    globalVariables,
    queries,
    queriesError,
    schema,
    schemaError,
    graphName,
    refreshRate: 0,
    isClusterMode,
    onSuccess: () => {
      if (!!settings.open && queryType !== 'static_data') {
        showToast({
          kind: KIND.positive,
          message: 'Query executed successfully.',
        });
      }
    },
    onError: (err: AxiosError<any, any>) => {
      if (!!settings.open && queryType !== 'static_data') {
        showToast({
          kind: KIND.negative,
          message: `Execute query failed. \n${getErrorMessage(err)}`,
        });
      }
    },
  });

  const shouldShowVertexTypeSelector = useMemo(() => {
    if (globalVariableID) {
      return false;
    }

    if (inputType === InputWidgetType.INPUT && dataType === DataType.Vertex) {
      return true;
    }

    if ((inputType === InputWidgetType.LIST || inputType === InputWidgetType.DROPDOWN) && dataType === DataType.Vertex) {
      return true;
    }

    return false;
  }, [globalVariableID, inputType, dataType]);

  const onChangeGlobalParamType = (type: string) => {
    if (['STRING', 'NUMBER', 'BOOL', 'DATETIME', 'VERTEX', 'LIST'].indexOf(type) === -1) {
      return;
    }
    if (type === 'LIST') {
      const tempGlobalParam = {
        ...inputValue,
        elementType: inputValue.type,
        value: [],
        type,
      } as GlobalParam;
      setInputValue(tempGlobalParam);
    } else {
      const tempGlobalParam = {
        ...inputValue,
        // @ts-ignore
        ...getParamTypeDefaultValue(type),
      } as GlobalParam;
      setInputValue(tempGlobalParam);
    }
  };

  const onChangeInputValue = (globalParam: GlobalParam) => {
    setInputValue(globalParam);
  };

  const onSettingUpdate = (key: string, value: any) => {
    const newInputState = {
      ...inputState,
      settings: {
        ...settings,
        [key]: value,
      },
    };
    setInputState(newInputState);
  };

  useGlobalVariables2FormState({ globalVariable, form });

  return (
    <div
      className={css({
        display: 'flex',
        flexDirection: 'column',
      })}
    >
      <FormProvider {...form}>
        <div
          className={css({
            padding: '12px',
            maxHeight: '75vh',
            overflow: 'auto',
          })}
        >
          {inputState && (
            <CommonSetting
              variableID={globalVariableID || globalParam.id}
              globalParameters={globalParameters}
              globalVariables={globalVariables}
              inputState={inputState}
              onChangeInputState={setInputState}
            />
          )}
          {shouldShowVertexTypeSelector && (
            // FIXME form type here
            <VertexTypeSelector dataType={dataType} elementType={elementType} baseURL={baseURL} form={form as any} />
          )}
          {[InputWidgetType.INPUT, InputWidgetType.SLIDER].includes(inputType as InputWidgetType) && (
            <DefaultInputSetting baseURL={baseURL} initialValue={inputValue} inputState={inputState} />
          )}
          {!globalVariableID && inputType === InputWidgetType.TEXT && (
            <ChartSettingItemContainer label="Default Value">
              <GlobalTextArea inputState={inputState} value={inputValue} onChangeInputValue={onChangeInputValue} />
            </ChartSettingItemContainer>
          )}
          {!globalVariableID && inputState.widgetType === InputWidgetType.DATETIME_PICKER && (
            <ChartSettingItemContainer label="Default Value">
              <GlobalDatetimePicker
                value={inputValue}
                globalParam={inputValue}
                inputState={inputState}
                onChangeGlobalParameter={onChangeInputValue}
                onChangeInputValue={onChangeInputValue}
                onChangeInputState={setInputState}
              />
            </ChartSettingItemContainer>
          )}
          {!globalVariableID && inputType === InputWidgetType.DATEPICKER && (
            <ChartSettingItemContainer label="Default Value">
              <GlobalDatePicker value={inputValue} globalVariable={globalVariable} globalParam={inputValue} inputState={inputState} onChangeInputValue={onChangeInputValue} />
            </ChartSettingItemContainer>
          )}
          {inputType === InputWidgetType.DROPDOWN && (
            <DefaultSelectSetting
              baseURL={baseURL}
              chartData={chartData}
              graphName={graphName}
              schema={schema}
              labelKey={inputState.settings?.labelKey}
              valueKey={inputState.settings?.valueKey}
              globalParam={inputValue}
              globalVariable={globalVariable}
              inputState={inputState}
              onChangeGlobalParamType={onChangeGlobalParamType}
              onChangeGlobalParameter={onChangeInputValue}
              onChangeInputState={setInputState}
            />
          )}
          {!globalVariableID && inputType === InputWidgetType.LIST && (
            <DefaultListSetting
              // FIXME form type
              form={form as any}
              elementType={dataType}
              globalParam={inputValue}
              defaultGraph={graphName}
              defaultVertexType={inputState.vertexType}
              baseURL={baseURL}
            />
          )}
          {!globalVariableID && inputType === InputWidgetType.MAP && (
            <DefaultMapSetting
              // FIXME form type
              form={form as any}
              globalParam={inputValue}
              defaultKeyType={inputState.keyType}
              defaultValueType={inputState.valueType}
            />
          )}
          {!globalVariableID && inputType === InputWidgetType.DROPDOWN && (
            <GlobalSearch
              globalInputProps={{ ...props, inputState: inputState, onChangeInputState: setInputState }}
              chartInputProps={{
                queryType: queryType,
                searchPattern: searchPattern,
                patternLimit: patternLimit,
                hopLimit: hopLimit,
                timeLimit,
                memoryLimit,
                interpretQuery: query,
                staticData: staticData,
                graphName: graphName,
                schema: schema,
                onGraphChanged: (graphName) => {
                  setInputState({
                    ...inputState,
                    settings: { ...settings, graphName, searchPattern: [] },
                  });
                  setCachedSearchPattern([]);
                },
                graphs: graphs,
                chartProps: { ...chartProps, chartData, schema, graphName, settings, onSettingUpdate },
                globalParameters: globalParameters,
                globalVariables: globalVariables,
                queries: queries,
                isLoading: isLoadingSchema || isLoadingQueries,
                isError: isSchemaError || isQueriesError,
                onQueryTypeChange: (queryType) => onSettingUpdate('queryType', queryType),
                onSearchPatternChanged: (searchPattern) => onSettingUpdate('searchPattern', searchPattern),
                onPatternLimitChanged: (limit) => onSettingUpdate('patternLimit', limit),
                onHopLimitChanged: (limit) => onSettingUpdate('hopLimit', limit),
                onTimeLimitChanged: (limit) => onSettingUpdate('timeLimit', limit),
                onMemoryLimitChanged: (limit) => onSettingUpdate('memoryLimit', limit),
                onInterpretQueryChanged: (interpretQuery) => onSettingUpdate('query', interpretQuery),
                onStaticDataChanged: (staticData) => onSettingUpdate('staticData', staticData),
                onRunQuery: (value, limit?: number) => {
                  if (queryType === 'pattern') {
                    if (value === cachedSearchPattern && limit === cachedPatternLimit) {
                      // if the state is not changed, just run refetch
                      refetch();
                    } else {
                      // else update react-query input
                      setCachedSearchPattern(value as Array<SearchItem>);
                      setCachedPatternLimit(limit as number);
                    }
                  } else if (queryType === 'interactive') {
                    if (value === cachedQuery) {
                      // if the state is not changed, just run refetch
                      refetch();
                    } else {
                      // else update react-query input
                      setCachedQuery(value as string);
                    }
                  } else if (queryType === 'static_data') {
                    // there is no need to run query for static data
                  }
                },
              }}
              error={error}
            />
          )}
          {globalVariable ? (
            <TypeInspector
              dataType={globalVariable.type}
              keyType={globalVariable.type === 'MAP' && globalVariable.keyType}
              valueType={globalVariable.type === 'MAP' && globalVariable.valueType}
              elementType={globalVariable.type === 'LIST' && globalVariable.elementType}
              vertexType={
                (globalVariable.type === 'VERTEX' || globalVariable.type === 'LIST') && globalVariable.vertexType
              }
            />
          ) : (
            <TypeInspectorForInput
              keyType={keyType}
              valueType={valueType}
              vertexType={vertexType}
              inputType={inputType}
              dataType={dataType}
              isMultiple={inputState.settings?.multi}
            />
          )}
        </div>
      </FormProvider>
      <ConfirmButtons
        containerOverrides={{
          padding: '8px',
          gap: '8px',
        }}
        onConfirm={async () => {
          // validate form
          const pass = await form.trigger();
          if (!pass) {
            return;
          }

          const values = form.getValues();
          const { draftGlobalParams, draftInputState } = handleForm2InputStateGlobalParam(
            inputState,
            globalVariableID ? globalVariable : inputValue,
            values
          );

          onChangeInputState && onChangeInputState(draftInputState);
          onChangeGlobalParameter?.(draftGlobalParams, draftInputState);
          close();
        }}
        onCancel={() => {
          close();
        }}
      />
    </div>
  );
}
