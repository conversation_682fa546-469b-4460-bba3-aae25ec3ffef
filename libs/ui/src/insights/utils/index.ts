import short from 'short-uuid';
import jsSHA from 'jssha/dist/sha256';
import { lazy } from 'react';

export const getUID = () => {
  return short.generate();
};

export function hexToRGBA(hex: string, alpha = 1) {
  const r = parseInt(hex.slice(1, 3), 16),
    g = parseInt(hex.slice(3, 5), 16),
    b = parseInt(hex.slice(5, 7), 16);

  return 'rgba(' + r + ',' + g + ',' + b + ',' + alpha + ')';
}

export const stringToColor = (str: string): string => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  let color = '#';
  for (let i = 0; i < 3; i++) {
    const value = (hash >> (i * 8)) & 0xff;
    color += ('00' + value.toString(16)).substr(-2);
  }
  return hexToRGBA(color);
};

export const getContrastTextColor = (color: string) => {
  if (color.indexOf('#') > -1) {
    return getContrastTextColor(hexToRGBA(color));
  } else if (color.indexOf('rgb') > -1) {
    const positions = [];
    for (let i = 0; i < color.length; i++) {
      if (['(', ',', ')'].includes(color[i])) {
        positions.push(i);
      }
    }
    var r = parseInt(color.substring(positions[0] + 1, positions[1]));
    var g = parseInt(color.substring(positions[1] + 1, positions[2]));
    var b = parseInt(color.substring(positions[2] + 1, positions[3]));
    return (r * 299 + g * 587 + b * 114) / 1000 >= 160 ? 'rgb(0,0,0)' : 'rgb(255,255,255)';
  }
};

export const isNumber = (n: any) => {
  return !isNaN(Number(n)) && isFinite(Number(n));
};

export function beautifyNumber(num: number): string {
  if (num === 0) {
    return '0';
  }
  let result = '';
  const sig = num < 0 ? '-' : '';
  num = Math.abs(num);
  for (let cnt = 0; num > 0; ) {
    result = (num % 10) + result;
    num = Math.floor(num / 10);
    if (++cnt % 3 === 0 && num > 0) {
      result = ',' + result;
    }
  }
  return sig + result;
}

export const valueComparator = (a: any, b: any) => {
  if (isNumber(a) && isNumber(b)) {
    return Number(a) - Number(b);
  } else {
    return String(a).localeCompare(String(b), 'en', {
      sensitivity: 'base',
    });
  }
};

export function encodeURLSearchParams(params: URLSearchParams): string {
  let url = '';
  params.forEach(function (value, key) {
    if (url !== '') {
      url += '&';
    }
    url += `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
  });
  return url;
}

export function filterProps(props) {
  const keys = Object.keys(props);
  return keys
    .filter((key) => !key.startsWith('$'))
    .reduce((acc, key) => {
      return {
        ...acc,
        [key]: props[key],
      };
    }, {});
}

export function getSHA256(str: string) {
  const shaObj = new jsSHA('SHA-256', 'TEXT');
  shaObj.update(str);
  return shaObj.getHash('HEX');
}

export function lazyWithPreload(factory: () => Promise<{ default: React.ComponentType<any> }>) {
  const Component: any = lazy(factory);
  Component.preload = factory;
  return Component;
}

// convert a Unicode string to a string in which
// each 16-bit unit occupies only one byte
function toBinary(string) {
  const codeUnits = new Uint16Array(string.length);
  for (let i = 0; i < codeUnits.length; i++) {
    codeUnits[i] = string.charCodeAt(i);
  }
  return String.fromCharCode(...new Uint8Array(codeUnits.buffer));
}

export function escapedBase64(dbId: string): string {
  return btoa(toBinary(dbId));
}

export function isValidHttpUrl(str: string): boolean {
  let url;
  try {
    url = new URL(str);
  } catch (_) {
    return false;
  }
  return url.protocol === 'http:' || url.protocol === 'https:';
}

export function parseNumber(value: any) {
  return parseFloat(`${typeof value === 'object' ? value.toString() : value}`);
}

export function isPrimitive(value: any): boolean {
  const valueType = typeof value;
  return valueType === 'number' || valueType === 'string' || valueType === 'boolean' || valueType === 'bigint';
}

export function convert2Number(value: string) {
  if (value === '') {
    return '';
  }

  try {
    return BigInt(value);
  } catch {
    const number = Number(value);
    return Number.isNaN(number) ? value : number;
  }
}
