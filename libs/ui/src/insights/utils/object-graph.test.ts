import { visitObjectsInGraph } from './object-graph';

describe('visitObjectsInGraph', () => {
  it('should visit all objects in a nested object', () => {
    const obj = {
      a: 1,
      b: {
        c: 2,
        d: {
          e: 3,
        },
      },
    };
    const visited = new Set();
    visitObjectsInGraph(obj, (path) => {
      const pathStr = path.join('.');
      visited.add(pathStr);
      return true;
    });
    expect(visited.has('')).toBe(true);
    expect(visited.has('b')).toBe(true);
    expect(visited.has('b.d')).toBe(true);
  });

  it('should not visit children if shouldVisitChildrenFn returns false', () => {
    const obj = {
      a: {
        b: 1,
        c: {
          d: 2,
        },
      },
    };
    const visited = new Set();
    visitObjectsInGraph(obj, (path, isLeaf) => {
      const pathStr = path.join('.');
      visited.add(pathStr);
      return !isLeaf;
    });
    expect(visited.has('a')).toBe(true);
    expect(visited.has('a.b')).toBe(false);
    expect(visited.has('a.c')).toBe(true);
    expect(visited.has('a.c.d')).toBe(false);
  });

  it('should handle arrays correctly', () => {
    const obj = {
      a: [1, 2, { b: 3 }],
      c: [{ d: { e: 4 } }],
    };
    const visited = new Set();
    visitObjectsInGraph(obj, (path) => {
      const pathStr = path.join('.');
      visited.add(pathStr);
      return true;
    });
    expect(visited.has('a')).toBe(true);
    expect(visited.has('a.2')).toBe(true);
    expect(visited.has('c')).toBe(true);
    expect(visited.has('c.0')).toBe(true);
    expect(visited.has('c.0.d')).toBe(true);
  });
});
