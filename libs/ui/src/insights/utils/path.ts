import { compareWithInstanceVersion, getBaseURL, getVersion } from '@tigergraph/tools-models';

// for cloud, we need to
// 1. add baseRUL
// 2. add /api prefix to resolve cors issue.
// 3. for version < 3.9.2, do not add /api prefix
export function getUserUploadedIconPathPrefix(isCloud: boolean) {
  if (isCloud) {
    return `${getBaseURL()}${
      getVersion() && compareWithInstanceVersion('3.9.2', '>=') ? '/api' : ''
    }/assets/img/user-uploaded-icons`;
  }

  return '/assets/img/user-uploaded-icons';
}

export function uploadedIconPath(icon: string, isCloud: boolean) {
  if (icon) {
    return `${getUserUploadedIconPathPrefix(isCloud)}/${icon}`;
  }
  return '';
}
