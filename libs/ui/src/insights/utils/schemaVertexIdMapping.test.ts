import { buildSchemaVertexIdMap } from './schemaVertexIdMapping';

describe('buildSchemaVertexIdMap', () => {
  it('should return empty object if schema is undefined', () => {
    const vertexIdMap = buildSchemaVertexIdMap(undefined);
    expect(vertexIdMap).toEqual({});
  });

  it('should return empty object if graph is undefined', () => {
    const schema = { graph: undefined };
    // @ts-ignore
    const vertexIdMap = buildSchemaVertexIdMap(schema);
    expect(vertexIdMap).toEqual({});
  });

  it('should return empty object if vertices are undefined', () => {
    const schema = { graph: { vertices: undefined } };
    // @ts-ignore
    const vertexIdMap = buildSchemaVertexIdMap(schema);
    expect(vertexIdMap).toEqual({});
  });

  it('should return empty object if vertices are empty', () => {
    const schema = { graph: { vertices: {} } };
    // @ts-ignore
    const vertexIdMap = buildSchemaVertexIdMap(schema);
    expect(vertexIdMap).toEqual({});
  });

  it('should return vertex primary id for each vertex type', () => {
    const schema = {
      graph: {
        vertices: {
          Person: { PrimaryId: { AttributeName: 'id' } },
          Place: { PrimaryId: { AttributeName: 'place_id' } },
          Organization: { PrimaryId: { AttributeName: 'org_id' } },
        },
      },
    };
    // @ts-ignore
    const vertexIdMap = buildSchemaVertexIdMap(schema);
    expect(vertexIdMap).toEqual({
      Person: 'id',
      Place: 'place_id',
      Organization: 'org_id',
    });
  });
});
