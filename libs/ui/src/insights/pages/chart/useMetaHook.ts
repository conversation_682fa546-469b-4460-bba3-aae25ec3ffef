import { reqGraphSty<PERSON>, reqQueryMetaList, reqGraphStatistics, reqHostList, reqMemoryTimeoutLimit } from '../../api';
import { ChartSchema } from '../../chart/schema';
import { QueryMeta, QueryMetaLogic } from '@tigergraph/tools-models/query';
import { useQuery } from 'react-query';
import { Statistics } from './type';
import { Color } from '@tigergraph/tools-models/gvis/insights';
import { AxiosError } from 'axios';
import { simpleAuth, getUserList, getGraphSchema, compareWithInstanceVersion } from '@tigergraph/tools-models';
import { getDiscriminatorMap } from '../../../graph/data';

export async function fetchGraphList(baseURL: string) {
  const response = (
    await simpleAuth({
      baseURL,
    })
  ).data;
  const graphList: string[] = [];
  if (!response.error) {
    const results = response.results;
    const privileges = results.privileges;

    let hasGlobalPrivileges = false;
    if (
      Object.keys(privileges).includes('1') &&
      ((Array.isArray(privileges['1']) && hasReadWritePermission(privileges['1'])) ||
        hasReadWritePermission(privileges['1']['privileges']))
    ) {
      hasGlobalPrivileges = true;
    }

    for (const graphName of Object.keys(privileges).filter((q) => q !== '1')) {
      if (hasGlobalPrivileges) {
        graphList.push(graphName);
      } else {
        const graphPrivileges = privileges[graphName];

        if (
          (Array.isArray(graphPrivileges) && hasReadWritePermission(graphPrivileges)) ||
          hasReadWritePermission(graphPrivileges['privileges'])
        ) {
          graphList.push(graphName);
        }
      }
    }
  }
  return graphList;
}

export function useGraphList(baseURL: string, params?: { enabled?: boolean }): string[] {
  const { data } = useQuery(
    ['meta', baseURL, 'graphs'],
    async () => {
      return fetchGraphList(baseURL);
    },
    params
  );

  return data ?? [];
}

export function useCheckWriteQueryPermission(baseURL: string): boolean {
  const { data } = useQuery(['meta', baseURL, 'checkWriteQueryPermission'], async () => {
    // for 4.2.0, `write_query` privilege was removed
    if (compareWithInstanceVersion('4.1.0', '>=')) {
      return true;
    }

    const response = (
      await simpleAuth({
        baseURL,
      })
    ).data;
    const graphList: string[] = [];
    if (!response.error) {
      const results = response.results;
      const privileges = results.privileges;

      let hasGlobalWriteQueryPrivileges = false;
      if (
        Object.keys(privileges).includes('1') &&
        ((Array.isArray(privileges['1']) && hasWriteQueryPermission(privileges['1'])) ||
          hasWriteQueryPermission(privileges['1']['privileges']))
      ) {
        hasGlobalWriteQueryPrivileges = true;
      }

      if (hasGlobalWriteQueryPrivileges) {
        return true;
      }

      for (const graphName of Object.keys(privileges).filter((q) => q !== '1')) {
        if (hasGlobalWriteQueryPrivileges) {
          graphList.push(graphName);
        } else {
          const graphPrivileges = privileges[graphName];

          if (
            (Array.isArray(graphPrivileges) && hasWriteQueryPermission(graphPrivileges)) ||
            hasWriteQueryPermission(graphPrivileges['privileges'])
          ) {
            graphList.push(graphName);
          }
        }
      }
    }

    // if no write query permission on any graph, we return false
    return graphList.length > 0;
  });

  // default assume user have write query permission
  if (data === undefined) {
    return true;
  }

  return data;
}

export function useUserList(baseURL: string) {
  const { data } = useQuery(['meta', baseURL, 'users'], async () => {
    const response = (await getUserList({}, { baseURL })).data;
    if (!response.error) {
      return response.results;
    }
    return [];
  });
  return data ?? [];
}

function hasReadWritePermission(privileges: Array<string>): boolean {
  if (
    privileges &&
    privileges.includes('READ_SCHEMA') &&
    privileges.includes('READ_DATA')
    // removed from 4.1.0
    // privileges.includes('READ_QUERY') &&
    // privileges.includes('WRITE_QUERY')
  ) {
    return true;
  }
  return false;
}

function hasWriteQueryPermission(privileges: Array<string>): boolean {
  // 4.1.0 remove WRITE_QUERY permission
  return true;
}

export const emptySchema = {
  graph: { vertices: {}, edges: {} },
  results: {
    GraphName: '',
    VertexTypes: [],
    EdgeTypes: [],
  },
};

export function useSchema(
  baseURL: string,
  graph: string
): {
  schema: ChartSchema;
  isLoading: boolean;
  isError: boolean;
  error?: AxiosError;
} {
  const { data, isLoading, isError, error } = useQuery<ChartSchema, AxiosError>(
    ['meta', baseURL, 'schema', graph],
    async () => {
      const response = (
        await getGraphSchema(
          {
            graph,
          },
          { baseURL }
        )
      ).data;

      const schemaVertices = {},
        schemaEdges = {};
      if (!response['error']) {
        const results = response['results'];
        for (const vertex of results['VertexTypes']) {
          const type = vertex['Name'];
          schemaVertices[type] = vertex;
        }
        for (const edge of results['EdgeTypes']) {
          const type = edge['Name'];
          schemaEdges[type] = edge;
        }
      }

      // todo(lin)
      //   remove load graph style from critical path
      try {
        const graphStylesResponse = await reqGraphStyles(baseURL, graph);

        if (!graphStylesResponse['error']) {
          const { vertexStyles, edgeStyles } = graphStylesResponse['results'];
          Object.entries(vertexStyles).forEach(([type, style]) => {
            schemaVertices[type]['style'] = style;
          });
          Object.entries(edgeStyles).forEach(([type, style]) => {
            schemaEdges[type]['style'] = style;
          });
        }
      } catch (error) {
        // ignore error
      }

      // give schema default style if needed
      const color = new Color();
      for (let type of Object.keys(schemaVertices)) {
        if (!schemaVertices[type]['style']) {
          schemaVertices[type]['style'] = {
            fillColor: color.getColor(type),
          };
        }
      }
      for (let type of Object.keys(schemaEdges)) {
        if (!schemaEdges[type]['style']) {
          schemaEdges[type]['style'] = {
            fillColor: color.getColor(type),
          };
        }
      }

      const schema = {
        results: response['results'],
        graph: { vertices: schemaVertices, edges: schemaEdges },
      } as ChartSchema;

      const discriminatorMap = getDiscriminatorMap(schema.results);
      schema.results.DiscriminatorMap = discriminatorMap;

      return schema;
    },
    {
      enabled: !!graph,
      refetchOnWindowFocus: true,
    }
  );
  return { schema: data || emptySchema, isLoading, isError, error };
}

export const emptyQueries = {};

export function useQueries(
  baseURL: string,
  graph: string
): {
  queries: { [key: string]: QueryMeta };
  isLoading: boolean;
  isError: boolean;
  error?: AxiosError;
} {
  const { data, isLoading, isError, error } = useQuery<{ [key: string]: QueryMeta }, AxiosError>(
    ['meta', baseURL, 'queries', graph],
    async () => {
      const response = await reqQueryMetaList(baseURL, graph);
      const queriesMeta: { [key: string]: QueryMeta } = {};
      if (!response['error']) {
        const gsqlQueriesMeta = response['results'];
        gsqlQueriesMeta.forEach((gsqlQueryMeta) => {
          const queryMeta = QueryMetaLogic.loadFromGSQL(graph, gsqlQueryMeta);
          if (!queryMeta.originalCode && queryMeta.draftCode) {
            queryMeta.originalCode = queryMeta.draftCode;
          }
          queriesMeta[queryMeta.queryName] = queryMeta;
        });
      }
      return queriesMeta;
    },
    {
      enabled: !!graph,
      refetchOnWindowFocus: true,
    }
  );
  return { queries: data || emptyQueries, isLoading, isError, error };
}

const emptyStatistics = [];

export function useStatistics(baseURL: string, graph: string, enabled = true) {
  const { data, isLoading, isFetching, isError, error } = useQuery<Statistics, AxiosError>(
    ['meta', baseURL, 'statistics', graph],
    async () => {
      const vertexStatistics = {
          function: 'stat_vertex_number',
          type: '*',
        },
        edgeStatistics = {
          function: 'stat_edge_number',
          type: '*',
        };
      let statistics: Statistics = [];
      let response = await reqGraphStatistics(baseURL, graph, vertexStatistics);
      if (!response['error']) {
        statistics = statistics.concat(response['results']);
      }
      response = await reqGraphStatistics(baseURL, graph, edgeStatistics);
      if (!response['error']) {
        statistics = statistics.concat(response['results']);
      }
      return statistics;
    },
    {
      enabled: !!graph && enabled,
    }
  );
  return {
    statistics: data || emptyStatistics,
    isLoading,
    isFetching,
    isError,
    error,
  };
}

export function useClusterMode(baseURL: string, enabled: boolean) {
  const { data, isLoading, isError, error } = useQuery<boolean, AxiosError>(
    ['meta', baseURL, 'clusterMode'],
    async () => {
      let response = await reqHostList(baseURL);
      if (response.error) {
        return false;
      }
      return response.results['System.HostList'].length > 1;
    },
    {
      enabled,
    }
  );
  return {
    isClusterMode: data || false,
    isLoading,
    isError,
    error,
  };
}

export function useTimeMemoryDefaultLimit(enabled?: boolean) {
  return useQuery<{ memoryLimit?: number; timeLimit?: number }, AxiosError>(
    ['meta', 'time', 'memory', 'limit'],
    async () => {
      const response = await reqMemoryTimeoutLimit();

      return {
        memoryLimit: response.results?.['GPE.QueryLocalMemLimitMB'] || undefined,
        timeLimit: response.results?.['RESTPP.Factory.DefaultQueryTimeoutSec'] ?? 30,
      };
    },
    {
      enabled,
    }
  );
}
