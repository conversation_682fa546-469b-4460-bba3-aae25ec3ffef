import { Input } from '@tigergraph/app-ui-lib/input';
import { styled, useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Popover, StatefulPopover } from 'baseui/popover';
import { TimePicker } from 'baseui/timepicker';
import { ChevronDown } from 'lucide-react';
import { useMemo, useState } from 'react';
import { DatePickIcon, StyledStatefulCalendar } from './styledStatefulCalendar';
import { getFormattedDate, quickTimeSelectOptions } from '../utils/datetime';

// We use January 1st, 100 years ago from today as the minimum date
const minDate = new Date(new Date().getFullYear() - 100, 0, 1);

export type Props = {
  disabled?: boolean;
  value?: string;
  onChange?(date: string): void;
  showTimeSelect?: boolean;
  placeholder?: string;
};

export interface OnQuickTimeSelect {
  (quickTimeID: string): void;
}

export const mapQuickOption2Label = new Map<string, string>(quickTimeSelectOptions.map((i) => [i.id, i.label]));

const QuickTimeSelectOption = styled('button', {
  display: 'inline-block',
  fontSize: '12px',
  fontWeight: 500,
  lineHeight: '16px',
  color: '#2C3237',
  padding: '2px 8px',
  border: '1px solid #AAB5BF',
  cursor: 'pointer',
  borderRadius: '2px',
  outline: '0',
  background: 'inherit',
});

export const QuickTimeSelect = (props: { onTimeSelect: OnQuickTimeSelect }) => {
  const [css, theme] = useStyletron();
  const unfoldTime = quickTimeSelectOptions.slice(0, 3);
  const foldTime = quickTimeSelectOptions.slice(3);

  const handleQuickSelect = (id: string) => {
    props.onTimeSelect?.(id);
  };

  return (
    <div className={css({ display: 'flex', justifyContent: 'space-around' })}>
      {unfoldTime.map((t) => (
        <QuickTimeSelectOption onClick={() => handleQuickSelect(t.id)} key={t.id}>
          {t.label}
        </QuickTimeSelectOption>
      ))}
      {foldTime.length > 0 && (
        <StatefulPopover
          content={
            <ul className={css({ listStyle: 'none', padding: 0, margin: '8px 0' })}>
              {foldTime.map((t) => (
                <li
                  key={t.id}
                  onClick={() => handleQuickSelect(t.id)}
                  className={css({
                    cursor: 'pointer',
                    padding: '8px 12px',
                    fontSize: '14px',
                    ':hover': {
                      backgroundColor: theme.colors.backgroundPrimary,
                    },
                  })}
                >
                  {t.label}
                </li>
              ))}
            </ul>
          }
        >
          <span>
            <QuickTimeSelectOption>
              More <ChevronDown className={css({ verticalAlign: 'middle' })} size={16} />
            </QuickTimeSelectOption>
          </span>
        </StatefulPopover>
      )}
    </div>
  );
};

const isValidDate = (date: unknown) => date instanceof Date && !isNaN(date.getTime());
const getDateFromValueString = (value: string): Date | undefined => {
  const date = new Date(value);
  if (isValidDate(date)) {
    return date;
  }
  return undefined;
};

export const TimeSelect = (props: Props) => {
  const [css] = useStyletron();
  const dateValue = useMemo(() => {
    return getDateFromValueString(props.value);
  }, [props.value]);

  return (
    <div>
      <h3 className={css({ fontSize: '14px', margin: '16px 0 8px' })}>Time</h3>
      <TimePicker
        value={dateValue}
        size="compact"
        creatable
        nullable
        format="24"
        onChange={(date) => {
          const datetime = getFormattedDate(date, true);
          props.onChange?.(datetime);
        }}
      />
    </div>
  );
};

export const DatetimePicker = (props: Props & { onQuickSelect?: OnQuickTimeSelect }) => {
  const [css, theme] = useStyletron();

  const dateValue = useMemo(() => {
    return getDateFromValueString(props.value);
  }, [props.value]);

  return (
    <div className={css({ background: theme.colors.backgroundPrimary, padding: '16px 24px 24px' })}>
      <QuickTimeSelect onTimeSelect={props.onQuickSelect} />
      <StyledStatefulCalendar
        value={dateValue}
        minDate={minDate}
        initialState={{ value: dateValue }}
        onChange={({ date }) => {
          if (Array.isArray(date)) {
            return;
          }

          if (isValidDate(dateValue)) {
            date.setHours(dateValue.getHours());
            date.setMinutes(dateValue.getMinutes());
            date.setSeconds(dateValue.getSeconds());
          }

          const formattedDate = getFormattedDate(date, props.showTimeSelect);
          props.onChange?.(formattedDate);
        }}
      />
      {props.showTimeSelect && <TimeSelect {...props} />}
    </div>
  );
};

export const DatetimePickerInput = (props: Props) => {
  const { value, disabled, placeholder } = props;
  const [showDatetimePicker, setShowDatetimePicker] = useState(false);

  const handleFocus = () => {
    setShowDatetimePicker(true);
  };

  const handleBlur = () => {
    setShowDatetimePicker(false);
  };

  const formattedValue = useMemo(() => {
    const quickSelectValue = mapQuickOption2Label.get(value);

    if (quickSelectValue) {
      return quickSelectValue;
    }
    return value ? value : '';
  }, [value]);

  const onQuickSelect = (id: string) => {
    props.onChange?.(id);
    handleBlur();
  };

  return (
    <>
      <Popover
        isOpen={showDatetimePicker}
        onClickOutside={handleBlur}
        content={<DatetimePicker {...props} onQuickSelect={onQuickSelect} />}
      >
        <Input
          disabled={disabled}
          onFocus={handleFocus}
          value={formattedValue}
          placeholder={placeholder || 'Select datetime'}
          startEnhancer={<DatePickIcon $size="16px" />}
        />
      </Popover>
    </>
  );
};
