import { Checkbox } from '@tigergraph/app-ui-lib/checkbox';
import { CheckboxProps, LABEL_PLACEMENT, STYLE_TYPE } from 'baseui/checkbox';
import { expand } from 'inline-style-expand-shorthand';

export default function StyledToggle(props: CheckboxProps) {
  return (
    <Checkbox
      checkmarkType={STYLE_TYPE.toggle_round}
      labelPlacement={LABEL_PLACEMENT.left}
      noToggleIcon={true}
      {...props}
      overrides={{
        Root: {
          style: {
            justifyContent: 'space-between',
            height: '100%',
            alignItems: 'center',
          },
        },
        Toggle: {
          style: ({ $checked }) => ({
            width: '10px',
            height: '10px',
            transform: $checked ? 'translateX(18px)' : 'translateX(2px)',
          }),
        },
        ToggleTrack: {
          style: {
            width: '30px',
            height: '16px',
            ...expand({
              margin: '0',
              borderRadius: '8px',
            }),
          },
        },
      }}
    />
  );
}
