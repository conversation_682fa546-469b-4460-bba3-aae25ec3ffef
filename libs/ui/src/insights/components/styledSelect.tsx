import React from 'react';
import { Select } from '@tigergraph/app-ui-lib/select';
import { SelectProps, SIZE } from 'baseui/select';
import { mergeOverrides } from 'baseui';
import { Overrides } from 'baseui/overrides';

export default function StyledSelect({ overrides, ...props }: SelectProps) {
  return (
    <Select
      onBlurResetsInput={false}
      {...props}
      overrides={mergeOverrides(
        {
          Popover: {
            props: {
              ignoreBoundary: true,
              popperOptions: {
                modifiers: {
                  preventOverflow: {
                    enabled: false,
                  },
                  hide: {
                    enabled: false,
                  },
                },
              },
              overrides: {
                Body: {
                  style: {
                    'z-index': 4,
                  },
                },
              },
            },
          },
          Root: {
            style: {
              display: 'inline-block',
              width: '100%',
            },
          },
          Dropdown: {
            style: {
              maxHeight: 'min(90vh, 400px)',
            },
          },
          Tag: {
            props: {
              overrides: {
                Root: {
                  style: {
                    width: 'fit-content',
                    maxWidth: '100%',
                  },
                },
                Text: {
                  style: {
                    maxWidth: '100%',
                  },
                },
              },
            },
          },
        },
        overrides as Overrides<any>
      )}
    />
  );
}
