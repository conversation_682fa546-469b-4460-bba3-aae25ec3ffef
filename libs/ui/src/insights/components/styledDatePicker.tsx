import React, { forwardRef } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { DatepickerProps } from 'baseui/datepicker';
import { DatePicker } from '@tigergraph/app-ui-lib/datepicker';
import { BaseInput, SIZE } from 'baseui/input';
import StyledTimePicker from './styledTimePicker';
import { refineTime } from './styledTimePicker/util';

export interface StyledDatePickerProps extends DatepickerProps {
  selectTime?: boolean;
}

export default function StyledDatePicker(props: StyledDatePickerProps) {
  const [css] = useStyletron();
  const value = Array.isArray(props.value) ? props.value[0] : props.value;

  return (
    <DatePicker
      {...props}
      overrides={{
        Popover: {
          props: {
            ignoreBoundary: true,
            popperOptions: {
              modifiers: {
                preventOverflow: {
                  enabled: false,
                },
                hide: {
                  enabled: false,
                },
              },
            },
          },
        },
        // @ts-ignore
        MonthContainer: ({ children }) => (
          <>
            {children}
            {props.selectTime && (
              <div
                className={css({
                  padding: '10px',
                })}
              >
                <div
                  className={css({
                    display: 'flex',
                  })}
                >
                  Select time
                </div>
                <div>
                  <StyledTimePicker
                    value={value ? `${value.getHours()}:${value.getMinutes()}:${value.getSeconds()}` : ''}
                    onChange={(time) => {
                      const [hour, minute, second] = refineTime(time).split(':');
                      const newDate = value ? new Date(value) : new Date();
                      newDate.setHours(parseInt(hour), parseInt(minute), parseInt(second));
                      props.onChange({ date: newDate });
                    }}
                  />
                </div>
              </div>
            )}
          </>
        ),
      }}
    />
  );
}
