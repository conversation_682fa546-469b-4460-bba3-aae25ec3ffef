import { AxiosError } from 'axios';
import { ClientError } from 'graphql-request';
import { useStyletron } from 'styletron-react';
import { StyledToast } from './styledToasterContainer';
import { KIND } from 'baseui/toast';
import clsx from 'clsx';

export function ErrorDisplay({
  error,
  label,
  isGSQLError,
  className,
  closeable = true
}: {
  error: AxiosError | Error | ClientError;
  label?: string;
  isGSQLError?: boolean;
  className?: string;
  closeable?: boolean;
}) {
  const [css] = useStyletron();
  if (!error) {
    return null;
  }

  let message = getErrorMessage(error);

  // 1. is gsql error
  // 2. detected if contain `interpreted mode`
  //      interpret query run will failed with different reason, only show interpreted query tips is contains `interpreted mode`
  const isInterpretGSQLError = isGSQLError && message.includes('interpreted mode');

  return (
    <div role="alert" className={clsx(css({ padding: '12px' }), className)}>
      <StyledToast
        message={
          <div>
            {label ?? 'Something went wrong:'}
            <div
              className={css({
                marginTop: '8px',
                maxHeight: '320px',
                overflow: 'auto',
              })}
            >
              {message}
              {isInterpretGSQLError ? (
                <span>
                  GSQL queries run in interpreted mode have some{' '}
                  <a
                    href="https://docs.tigergraph.com/gsql-ref/current/appendix/interpreted-gsql-limitations"
                    rel="noreferrer"
                    target="_blank"
                  >
                    limitations
                  </a>
                  . Switch to GraphStudio to install the query and then run the installed query in Insights.
                </span>
              ) : null}
            </div>
          </div>
        }
        kind={KIND.negative}
        hideBorder={true}
        closeable={closeable}
      />
    </div>
  );
}

export function getErrorMessage(error: AxiosError | Error | ClientError | string | undefined) {
  if (typeof error === 'string') {
    return error;
  }
  let message = error?.message;

  if (error instanceof ClientError) {
    message = error.response.error || error.response.message;
    // error message from graphql api
    if (error.response.errors && error.response.errors.length > 0) {
      message = error.response.errors[0].message;
    }
  } else if ('response' in error) {
    // check for axios error
    if (error.response['error']) {
      message = error.response['error'];
      // @ts-ignore
    } else if (error?.response?.data?.message) {
      // error message from rest api
      // @ts-ignore
      message = (error as AxiosError).response?.data?.message;
    }
  }

  if (!message) {
    message = '';
  }

  return message;
}
