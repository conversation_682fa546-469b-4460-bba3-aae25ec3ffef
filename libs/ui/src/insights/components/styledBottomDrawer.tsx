import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Layer } from 'baseui/layer';
import React from 'react';

export interface StyledBottomDrawerProps {
  children: React.ReactNode;
  isOpen: boolean;
}

export default function StyledBottomDrawer(props: StyledBottomDrawerProps) {
  const [css, theme] = useStyletron();

  const { children, isOpen } = props;

  return (
    <Layer>
      <div
        className={css({
          width: '100%',
          height: 'fit-content',
          position: 'fixed',
          left: '0',
          bottom: '0',
          overflowX: 'auto',
          transform: isOpen ? 'translateY(0)' : 'translateY(100%)',
          transition: 'all 0.8s ease',
          transformOrigin: 'bottom',
          boxShadow: theme.colors['shadow.popup'],
          color: theme.colors['text.primary'],
          backgroundColor: theme.colors['background.secondary'],
          borderTop: `1px solid ${theme.colors.divider}`,
          fontSize: '14px',
        })}
      >
        {children}
      </div>
    </Layer>
  );
}
