import React, { forwardRef } from 'react';
import { useStyletron, styled } from '@tigergraph/app-ui-lib/Theme';
import { Select, Input } from './baseui';
import { filterProps } from '../../utils';
import { RuleStyleProps, Palate, Rule, Rules } from './type';
import {
  getCategoricalPalate,
  getCategoricalPalates,
  getColorGradient,
  getContinualColorsPalates,
  getContinualPalate,
  getScaleOrdinal,
} from './colors';

export function RangeColorStyle(props: RuleStyleProps) {
  const { rule, updateRuleField } = props;

  const palates = getContinualColorsPalates();
  let palate = palates[0];
  for (let i = 0; i < palates.length; i++) {
    if (palates[i].name === rule.palateName) {
      palate = palates[i];
      break;
    }
  }

  return (
    <>
      <ColorPalateSelect
        palates={palates}
        palate={palate}
        onPalateSelected={(palate) => {
          updateRuleField('palateName', palate.name);
          const colors = palate.colors;
          updateRuleField('styleStartValue', colors[0]);
          updateRuleField('styleEndValue', colors[colors.length - 1]);
        }}
      />
      <RangeColorPoint label="min point" {...props} isStart={true} />
      <RangeColorPoint label="max point" {...props} isStart={false} />
    </>
  );
}

export function UniqueColorStyle(props: RuleStyleProps) {
  const { rule, updateRuleField } = props;
  const palates = getCategoricalPalates();

  let palate = palates[0];
  for (let i = 0; i < palates.length; i++) {
    if (palates[i].name === rule.palateName) {
      palate = palates[i];
      break;
    }
  }

  return (
    <>
      <ColorPalateSelect
        palates={palates}
        palate={palate}
        onPalateSelected={(palate) => {
          updateRuleField('palateName', palate.name);
          const colors = palate.colors;
          updateRuleField('styleStartValue', colors[0]);
          updateRuleField('styleEndValue', colors[colors.length - 1]);
        }}
      />
      <UniqueColorList {...props} />
    </>
  );
}

function ColorPalateSelect({
  palate,
  palates,
  onPalateSelected,
}: {
  palates: Palate[];
  palate: Palate;
  onPalateSelected: (palate: Palate) => void;
}) {
  const [css] = useStyletron();

  return (
    <Select
      maxDropdownHeight={'300px'}
      overrides={{
        Dropdown: {
          style: {
            marginTop: '8px',
            paddingTop: '8px',
            paddingLeft: '8px',
            paddingBottom: '8px',
            paddingRight: '8px',
          },
        },
        DropdownListItem: {
          component: ColorPalateItem,
        },
        ValueContainer: {
          component: () => (
            <div
              className={css({
                marginLeft: '8px',
                marginTop: '3px',
                marginBottom: '3px',
                flexGrow: 1,
                display: 'flex',
                height: '24px',
                borderRadius: '4px',
                background: palate.type !== 'categorical' ? getColorGradient(palate) : '',
              })}
            >
              {palate.type === 'categorical' ? <UniqueColorPalate palate={palate} /> : null}
            </div>
          ),
        },
      }}
      options={palates}
      value={[palate]}
      labelKey="id"
      onChange={(params) => {
        onPalateSelected(params.value[0] as Palate);
      }}
      clearable={false}
      searchable={false}
    />
  );
}

type ListItemProps = {
  [x: string]: any;
  item: Palate;
};

const ColorPalateItem = forwardRef<HTMLLIElement, ListItemProps>((props, ref) => {
  const [css] = useStyletron();
  const { item, ...rest } = props;

  return (
    <li
      {...filterProps(rest)}
      ref={ref}
      className={css({
        cursor: 'pointer',
        listStyle: 'none',
        display: 'flex',
        alignItems: 'center',
        ':not(:first-child)': {
          marginTop: '8px',
        },
        height: '24px',
        borderRadius: '4px',
        background: item.type !== 'categorical' ? getColorGradient(item) : '',
      })}
    >
      {item.type === 'categorical' ? <UniqueColorPalate palate={item} /> : null}
    </li>
  );
});

function RangeColorPoint({
  label,
  isStart,
  rule,
  updateRuleField,
}: { label: string; isStart: boolean } & RuleStyleProps) {
  const [css] = useStyletron();
  return (
    <div
      className={css({
        alignSelf: 'flex-end',
      })}
    >
      <StyleLabel>{label}</StyleLabel>
      <div
        className={css({
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        })}
      >
        <Input
          type="number"
          value={isStart ? rule.conditionStartValue : rule.conditionEndValue}
          onChange={(e) =>
            updateRuleField(
              isStart ? 'conditionStartValue' : 'conditionEndValue',
              Number(parseFloat(e.currentTarget.value))
            )
          }
          overrides={{
            Root: {
              style: {
                width: '114px',
              },
            },
          }}
        />
        <span
          className={css({
            width: '24px',
            height: '24px',
            marginLeft: '4px',
            borderRadius: '4px',
            backgroundColor: isStart ? rule.styleStartValue : rule.styleEndValue,
          })}
        />
      </div>
    </div>
  );
}

export function RangeNumericStyle(props: RuleStyleProps) {
  return (
    <>
      <RangeNumericPoint label="min point" {...props} isStart={true} />
      <RangeNumericPoint label="max point" {...props} isStart={false} />
    </>
  );
}

function RangeNumericPoint({
  label,
  isStart,
  rule,
  ruleConfigs,
  updateRuleField,
}: { label: string; isStart: boolean } & RuleStyleProps) {
  const [css] = useStyletron();

  const { styleKey } = rule;

  let options = [];
  for (let ruleConfig of ruleConfigs) {
    if (ruleConfig.styleKey === styleKey) {
      options = ruleConfig.styleOptions;
    }
  }

  return (
    <div>
      <StyleLabel>{label}</StyleLabel>
      <div
        className={css({
          display: 'flex',
          justifyContent: 'space-between',
        })}
      >
        <Input
          type="number"
          value={isStart ? rule.conditionStartValue : rule.conditionEndValue}
          onChange={(e) =>
            updateRuleField(
              isStart ? 'conditionStartValue' : 'conditionEndValue',
              Number(parseFloat(e.currentTarget.value))
            )
          }
        />
        <Select
          clearable={false}
          value={[{ value: isStart ? rule.styleStartValue : rule.styleEndValue }]}
          options={options}
          valueKey="value"
          labelKey="label"
          onChange={(params) => {
            const value = params.value[0];
            if (isStart) {
              updateRuleField('styleStartValue', value.value);
              updateRuleField('styleStartLabel', value.label);
            } else {
              updateRuleField('styleEndValue', value.value);
              updateRuleField('styleEndLabel', value.label);
            }
          }}
          overrides={{
            Root: {
              style: {
                width: '80px',
                marginLeft: '4px',
                flexShrink: 0,
              },
            },
          }}
        />
      </div>
    </div>
  );
}

function UniqueColorList({ field, rule }: RuleStyleProps) {
  const { palateName } = rule;
  const scaleOrdinal = getScaleOrdinal(palateName);

  const [css] = useStyletron();
  // if no filed
  if (!field) {
    return (
      <div
        className={css({
          textAlign: 'center',
          marginTop: '8px',
        })}
      >
        empty value list
      </div>
    );
  }

  let total = field.values.length;
  // only show first 60 sample
  const values = field.values.slice(0, 60);

  return (
    <ul
      className={css({
        listStyle: 'none',
        padding: '8px 4px 0 4px',
        margin: '0',
        marginLeft: '8px',
        maxHeight: '200px',
        overflowY: 'auto',
        scrollbarWidth: 'thin',
      })}
    >
      {values.map((value, i) => (
        <li
          className={css({
            display: 'flex',
            alignItems: 'center',
            ':not(:first-child)': {
              marginTop: '8px',
            },
          })}
          key={value}
        >
          <span
            className={css({
              marginRight: '8px',
              width: '120px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              flex: 1,
            })}
          >
            {String(value)}
          </span>
          <span
            className={css({
              width: '24px',
              height: '24px',
              borderRadius: '4px',
              backgroundColor: scaleOrdinal(`${value}`),
            })}
          />
        </li>
      ))}
      {total > 30 ? (
        <div
          className={css({
            fontSize: '14px',
            marginTop: '6px',
          })}
        >
          +{total - 30} more
        </div>
      ) : null}
    </ul>
  );
}

export function RuleColor({ rule, first = true, last = true }: { rule: Rule; first?: boolean; last?: boolean }) {
  const { condition, styleType, styleValue, palateName } = rule;
  const [css] = useStyletron();

  if (styleType !== 'color') {
    return null;
  }

  if (condition === 'unique value') {
    const palate = getCategoricalPalate(palateName);
    if (!palate) {
      return null;
    }

    return <UniqueColorPalate palate={palate} first={first} last={last} />;
  }

  const palate = getContinualPalate(palateName);

  return (
    <div
      className={css({
        height: '100%',
        ...(first
          ? {
              borderTopLeftRadius: '4px',
              borderBottomLeftRadius: '4px',
            }
          : null),
        ...(last
          ? {
              borderTopRightRadius: '4px',
              borderBottomRightRadius: '4px',
            }
          : null),
        background: condition === 'range' ? getColorGradient(palate) : styleValue,
      })}
    />
  );
}

export function RuleArrowColor({ rules, defaultColor, type }: { rules: Rules; defaultColor: string; type: string }) {
  let arrrowColor = '';
  let startColor = '';
  let endColor = '';
  if (!rules || rules.length === 0) {
    arrrowColor = defaultColor;
    startColor = defaultColor;
    endColor = defaultColor;
  } else if (rules.length === 1) {
    arrrowColor = rules[0].styleValue;
    startColor = rules[0].styleValue;
    endColor = rules[0].styleValue;
  } else {
    arrrowColor = rules[1].styleValue;
    startColor = rules[0].styleValue;
    endColor = rules[1].styleValue;
  }
  const [css] = useStyletron();
  return (
    <div
      className={css({
        width: '24px',
        height: '24px',
      })}
    >
      <svg width="24px" height="24px" viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <marker id={`${type}-head`} orient="auto" markerWidth="3" markerHeight="4" refX="0.1" refY="2">
            <path d="M0,0 V4 L2,2 Z" fill={arrrowColor} />
          </marker>

          <linearGradient id={`${type}-gradient`} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="50%" stopColor={startColor} />
            <stop offset="50%" stopColor={endColor} />
          </linearGradient>
        </defs>

        <path
          id="arrow-line"
          markerEnd={`url(#${type}-head)`}
          strokeWidth="6"
          fill="none"
          stroke={`url(#${type}-gradient)`}
          d="M5,40, 40 10"
        />
      </svg>
    </div>
  );
}

export function UniqueColorPalate({
  palate,
  first = true,
  last = true,
}: {
  palate: Palate;
  first?: boolean;
  last?: boolean;
}) {
  const [css] = useStyletron();
  return (
    <div
      className={css({
        display: 'flex',
        height: '100%',
        width: '100%',
      })}
    >
      {/* we have 60 color in palate, only show the first 12 color */}
      {palate.colors.slice(0, 12).map((color, index) => (
        <span
          key={index}
          className={css({
            flex: 1,
            backgroundColor: color,
            ':first-child': first
              ? {
                  borderTopLeftRadius: '4px',
                  borderBottomLeftRadius: '4px',
                }
              : null,
            ':last-child': last
              ? {
                  borderTopRightRadius: '4px',
                  borderBottomRightRadius: '4px',
                }
              : null,
          })}
        />
      ))}
    </div>
  );
}

export const StyleLabel = styled('div', {
  paddingTop: '8px',
  paddingBottom: '4px',
});
