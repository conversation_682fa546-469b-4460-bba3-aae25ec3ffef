import React, { forwardRef, useRef, useState } from 'react';
import { List, IItemProps, arrayMove, arrayRemove } from 'react-movable';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import useHover from 'ahooks/lib/useHover';
import { MdDragIndicator } from '../../../react-icons';

import IconButton from '../iconButton';
import { MdDeleteOutline } from '../../../react-icons';
import { Rule, Rules, StyleDesc, FieldArray } from './type';
import { ConfirmPopover } from '../confirmPopover';
import { EditRuleStylePopover } from './RuleStylePopover';

export type RuleListProps = {
  widgetType?: string;
  rules: Rules;
  onRuleChanged: (rules: Rules) => void;
  ruleConfigs: StyleDesc[];
  fields: FieldArray;
  conditionSelectorHidden?: boolean;
};

export function RuleList({
  rules = [],
  widgetType,
  onRuleChanged,
  ruleConfigs,
  fields,
  conditionSelectorHidden,
}: RuleListProps) {
  const [css, theme] = useStyletron();

  const onDelete = (index: number) => {
    let newEditRules = [...rules.slice(0, index), ...rules.slice(index + 1)];
    onRuleChanged(newEditRules);
  };

  const onSave = (index: number, rule: Rule) => {
    let newEditRules = [...rules.slice(0, index), rule, ...rules.slice(index + 1)];
    onRuleChanged(newEditRules);
  };

  return (
    <div
      className={css({
        ...theme.typography.Body2,
      })}
    >
      <List
        lockVertically
        values={rules}
        onChange={({ oldIndex, newIndex }) => {
          let newEditRules = newIndex === -1 ? arrayRemove(rules, oldIndex) : arrayMove(rules, oldIndex, newIndex);
          onRuleChanged(newEditRules);
        }}
        renderList={({ children, props, isDragged }) => (
          <ul
            {...props}
            className={css({
              margin: 0,
              padding: 0,
              listStyle: 'none',
              display: 'block',
              cursor: isDragged ? 'grabbing' : 'inherit',
            })}
          >
            {children}
          </ul>
        )}
        renderItem={({ props, value, index, isDragged, isSelected }) => {
          return (
            <PreviewRuleItem
              key={index}
              index={index}
              rule={value}
              widgetType={widgetType}
              onDelete={onDelete}
              onSave={onSave}
              isDragged={isDragged}
              isSelected={isSelected}
              ruleConfigs={ruleConfigs}
              conditionSelectorHidden={conditionSelectorHidden}
              fields={fields}
              {...props}
            />
          );
        }}
      />
    </div>
  );
}

const PreviewRuleItem = forwardRef(
  (
    {
      index,
      rule,
      onDelete,
      onSave,
      isDragged,
      isSelected,
      ruleConfigs,
      fields,
      conditionSelectorHidden,
      widgetType,
      ...props
    }: IItemProps & {
      index: number;
      rule: Rule;
      widgetType?: string;
      onDelete: (index: number) => void;
      onSave: (index: number, rule: Rule) => void;
      isDragged: boolean;
      isSelected: boolean;
      conditionSelectorHidden: boolean;
      ruleConfigs: StyleDesc[];
      fields: FieldArray;
    },
    ref: React.Ref<HTMLLIElement>
  ) => {
    const [css, theme] = useStyletron();
    const innerRef = useRef<HTMLDivElement>(null);
    const isHovering = useHover(innerRef);
    const [isOpen, setIsOpen] = useState(false);

    const isDnd = isHovering || isDragged || isSelected;

    return (
      <li
        {...props}
        ref={ref}
        className={css({
          // todo(lin)
          ...(props.style as any),
          padding: '4px 8px 4px 8px',
          listStyleType: 'none',
          borderRadius: '4px',
          color: '#3F5870',
          ...theme.typography.HeadingMenu,
          cursor: isDragged ? 'grabbing' : 'inherit',
          ':hover': {
            backgroundColor: theme.colors.gray100,
          },
          ':active': {
            backgroundColor: theme.colors.secondary300,
          },

          backgroundColor: isDragged || isSelected ? theme.colors.gray100 : 'transparent',
        })}
      >
        <div
          ref={innerRef}
          className={css({
            display: 'flex',
            alignItems: 'center',
          })}
        >
          {isDnd ? (
            <button
              data-movable-handle
              key="movable-handle"
              style={{
                border: 'none',
                outline: 'none',
                margin: 0,
                padding: 0,
                background: 'transparent',
                cursor: isDragged ? 'grabbing' : 'grab',
              }}
              tabIndex={-1}
            >
              <MdDragIndicator width={16} height={16} color={theme.colors.gray400} style={{ width: '12px' }} />
            </button>
          ) : (
            <div
              className={css({
                width: '12px',
              })}
            />
          )}
          <EditRuleStylePopover
            rule={rule}
            ruleConfigs={ruleConfigs}
            conditionSelectorHidden={conditionSelectorHidden}
            fields={fields}
            widgetType={widgetType}
            onSave={(rule) => {
              onSave(index, rule);
            }}
          />
          <div
            className={css({
              width: '20px',
              marginLeft: '21px',
              boxSizing: 'border-box',
              display: 'flex',
              justifyContent: 'flex-end',
              alignItems: 'center',
            })}
          >
            {isDnd || isOpen ? (
              <>
                <IconButton key="delete" onClick={() => setIsOpen(true)}>
                  <MdDeleteOutline width={16} height={16} color={theme.colors.gray300} />
                </IconButton>
                <ConfirmPopover
                  isOpen={isOpen}
                  onClose={() => setIsOpen(false)}
                  confirmLabel="Confirm to delete"
                  onConfirm={() => onDelete(index)}
                >
                  {/* just need a place holder */}
                  <span />
                </ConfirmPopover>
              </>
            ) : null}
          </div>
        </div>
      </li>
    );
  }
);
