export type Condition = '=' | '!=' | '>' | '>=' | '<' | '<=' | 'contains' | 'range' | 'unique value' | 'always';

export type FieldType = 'string' | 'number' | 'boolean';

export type Field = {
  type: FieldType;
  name: string;
  values: (string | number)[];
  conditions?: Condition[];
};

export type FieldArray = Field[];

export const FIELD_CONDITIONS: Record<FieldType, Condition[]> = {
  string: ['=', '!=', '>', '>=', '<', '<=', 'unique value', 'always'],
  number: ['=', '!=', '>', '>=', '<', '<=', 'range', 'unique value', 'always'],
  boolean: ['=', 'unique value', 'always'],
};

export type StyleType = 'color' | 'numeric' | 'edgetype';

export type StyleOptions = { value: number | string; label: string }[];

export type StyleDesc = {
  styleKey: string;
  styleType: StyleType;
  styleLabel: string;
  styleOptions?: StyleOptions;
};

export type RuleType = 'single' | 'range';

export type Rule = {
  fieldType: FieldType;
  fieldName: string;
  columnName?: string; // for table column

  condition: Condition;
  // single condition value
  conditionValue: string | boolean | number;
  // range condition value
  conditionStartValue: number;
  conditionEndValue: number;

  styleKey: string;
  styleType: StyleType;
  styleLabel: string;
  // single style value
  styleValue: string;
  // range style value
  palateName: string;
  styleStartValue: string;
  styleStartLabel: string;
  styleEndValue: string;
  styleEndLabel: string;
};

export type Rules = Rule[];

export type RuleStyleProps = {
  field?: Field;
  rule: Rule;
  ruleConfigs: StyleDesc[];
  updateRuleField: (ruleField: keyof Rule, ruleFieldValue: any) => void;
};

export type Palate = {
  name: string;
  // note:
  // 1. categorical is discrete color
  // 2. sequential only support single hue for now.
  // 3. both sequential and diverging are continuous color
  type: 'categorical' | 'sequential' | 'diverging';
  colors: ReadonlyArray<string>;
  interpolate?: (t: number) => string;
};
