import React, { forwardRef, ReactNode, useState } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { MdDragIndicator } from '../../../react-icons';
import { MdDeleteOutline } from '../../../react-icons';
import IconButton from '../../components/iconButton';
import IconPopover from '../iconPopover';
import { ConfirmStatefulPopover } from '../confirmPopover';
import { PopoverOverrides } from 'baseui/popover';

export interface StyledListLabelProps {
  popover?: ReactNode | ((args: { close: () => void }) => ReactNode);
  popoverOverrides?: PopoverOverrides;
  label?: ReactNode;
  onConfirm?: () => void;
  onCancel?: () => void;
  onRemove?: () => void;
  onOpen?: () => void;
  isDragged: boolean;
  isSelected: boolean;
  value: ReactNode;
}

export const StyledListLabel = forwardRef((props: StyledListLabelProps, ref: React.Ref<HTMLLIElement>) => {
  const [css, theme] = useStyletron();

  const { value, isDragged, popover, popoverOverrides, onConfirm, onCancel, onRemove, onOpen } = props;

  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className={css({
        display: 'flex',
        width: '100%',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: isHovered ? theme.colors.gray100 : '#fff',
        borderRadius: '4px',
        cursor: 'default',
      })}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className={css({
          display: 'flex',
          width: `calc(100% - ${onRemove ? 16 : 0}px)`,
          alignItems: 'center',
          color: isHovered ? theme.colors.black01 : theme.colors.black02,
        })}
      >
        <div
          data-movable-handle
          className={css({
            height: '16px',
            cursor: isDragged ? 'grabbing' : 'grab',
            visibility: isHovered ? 'visible' : 'hidden',
          })}
        >
          <MdDragIndicator
            width={16}
            height={16}
            color={theme.colors.gray400}
            style={{
              width: '12px',
            }}
          />
        </div>
        <div
          className={css({
            cursor: popover ? 'pointer' : 'default',
            flex: 1,
            lineHeight: '32px',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
          })}
        >
          {popover ? (
            <IconPopover
              label={value}
              overrides={popoverOverrides}
              popover={popover}
              onConfirm={onConfirm}
              onCancel={onCancel}
              onOpen={onOpen}
            />
          ) : (
            value
          )}
        </div>
      </div>
      {onRemove && (
        <div
          className={css({
            display: 'flex',
            visibility: isHovered ? 'visible' : 'hidden',
          })}
        >
          <ConfirmStatefulPopover confirmLabel="Confirm to delete" onConfirm={onRemove}>
            <IconButton>
              <MdDeleteOutline width={16} height={16} color={theme.colors.gray300} />
            </IconButton>
          </ConfirmStatefulPopover>
        </div>
      )}
    </div>
  );
});
