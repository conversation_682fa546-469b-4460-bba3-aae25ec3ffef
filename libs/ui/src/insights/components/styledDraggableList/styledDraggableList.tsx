import React, { ReactNode } from 'react';
import { List } from 'react-movable';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { StyledListLabel } from './styledListLabel';
import { PopoverOverrides } from 'baseui/popover';

export interface StyledDraggableListProps {
  items?: ReactNode[];
  popovers?: ReactNode[] | ((args: { close: () => void }) => ReactNode)[];
  popoverOverrides?: PopoverOverrides;
  removable?: (value: ReactNode, index: number) => boolean;
  onChange?: (args: { oldIndex: number; newIndex: number }) => any;
  onConfirm?: (index: number) => void;
  onCancel?: (index: number) => void;
  onRemove?: (index: number) => void;
  onOpen?: (index: number) => void;
}

export default function StyledDraggableList(props: StyledDraggableListProps) {
  const [css] = useStyletron();
  const { items, popovers, popoverOverrides, removable, onRemove, onConfirm, onCancel, onChange, onOpen } = props;

  return (
    <List
      values={items}
      onChange={onChange}
      renderList={({ children, props, isDragged }) => (
        <ul
          className={css({
            margin: 0,
            padding: 0,
            listStyle: 'none',
            display: 'block',
            cursor: isDragged ? 'grabbing' : 'inherit',
          })}
          {...props}
        >
          {children}
        </ul>
      )}
      renderItem={({ value, props, index, isDragged, isSelected }) => (
        <li
          className={css({
            listStyleType: 'none',
          })}
          {...props}
        >
          <StyledListLabel
            value={value}
            isDragged={isDragged}
            isSelected={isSelected}
            popover={popovers?.[index]}
            popoverOverrides={popoverOverrides}
            onRemove={onRemove && (!removable || removable(value, index)) ? () => onRemove(index) : undefined}
            onConfirm={onConfirm ? () => onConfirm(index) : undefined}
            onCancel={onCancel ? () => onCancel(index) : undefined}
            onOpen={onOpen ? () => onOpen(index) : undefined}
          />
        </li>
      )}
    />
  );
}
