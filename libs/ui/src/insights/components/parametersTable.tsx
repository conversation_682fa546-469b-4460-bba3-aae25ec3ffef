import React, { useMemo } from 'react';
import { TableBuilder } from '@tigergraph/app-ui-lib/table';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { TableBuilderColumn } from 'baseui/table-semantic';
import { GlobalParam, GlobalParams, GlobalVariables } from '../chart';
import { isObject, upperCase } from 'lodash';

type Props = {
  params: GlobalParams;
  globalVariables?: GlobalVariables;
};

const getType = (param: GlobalParam) => {
  if (param.type === 'LIST') {
    return `List<${param.elementType}>`;
  }
  if (param.type === 'MAP') {
    return `MAP<${upperCase(param.keyType)}, ${upperCase(param.valueType)}>`;
  }
  return param.type;
};

const getVariableRow = (param: GlobalParam, isGlobalVariable: boolean) => {
  const needStringify = ['LIST', 'VERTEX', 'MAP', 'BOOL', 'BOOLEAN'].includes(param.type) || isObject(param.value);
  return {
    name: isGlobalVariable ? `${param.name}(Global)` : param.name,
    type: getType(param),
    value:
      needStringify
        ? JSON.stringify(
            param.value,
            null,
            param.type === 'VERTEX' || (param.type === 'LIST' && param.elementType === 'VERTEX') || param.type === 'MAP' ? 2 : 0
          )
        : param.value,
  };
};

export function ParametersTable({ params, globalVariables = {} }: Props) {
  const rows = useMemo(() => {
    const result = [];
    [{ params: globalVariables, isGlobalVariable: true }, { params }].forEach(({ params, isGlobalVariable }) => {
      Object.values(params).forEach(param => {
        const r = getVariableRow(param, isGlobalVariable);
        result.push(r);
      })
    });
    return result;
  }, [params, globalVariables]);

  const [css] = useStyletron();

  return (
    <TableBuilder
      data={rows}
      emptyMessage="No inputs available."
      overrides={{
        Root: { style: { maxHeight: '200px', overflow: 'auto' } },
      }}
    >
      <TableBuilderColumn header="Variable Name">{(row) => row.name}</TableBuilderColumn>
      <TableBuilderColumn header="Type">{(row) => row.type}</TableBuilderColumn>
      <TableBuilderColumn
        header="Current Value"
        overrides={{
          TableBodyCell: {
            style: {
              maxWidth: '400px',
            },
          },
        }}
      >
        {(row) => (
          <pre
            className={css({
              padding: 0,
              margin: 0,
              whiteSpace: 'pre-wrap',
              wordWrap: 'break-word',
            })}
          >
            {row.value}
          </pre>
        )}
      </TableBuilderColumn>
    </TableBuilder>
  );
}
