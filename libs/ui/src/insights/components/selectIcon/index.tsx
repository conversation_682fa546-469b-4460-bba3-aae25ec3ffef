import React, { useMemo, useRef, ReactNode, useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { AxiosError } from 'axios';
import { expand } from 'inline-style-expand-shorthand';
import { KIND } from 'baseui/toast';
import { Search, Delete } from 'baseui/icon';
import { Input } from '@tigergraph/app-ui-lib/input';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Overrides } from 'baseui/overrides';

import { showToast } from '../styledToasterContainer';
import UploadIcon from '../../assets/images/upload.svg';
import { Button } from '@tigergraph/app-ui-lib/button';
import { ButtonOverrides } from 'baseui/button';
import { mergeOverrides } from 'baseui';
import { getErrorMessage } from '../error';
import { uploadedIconPath } from '../../utils/path';
import { getAxiosInstance, getBaseURL } from '@tigergraph/tools-models';

// the outermost box of select icon
export function SelectIconContainer(props: Props) {
  const [css, theme] = useStyletron();
  const [searchKeyword, setSearchKeyword] = useState('');
  return (
    <div
      className={css({
        height: '200px',
        overflowY: 'auto',
        overflowX: 'hidden',
        borderRadius: '4px',
      })}
    >
      <Input
        startEnhancer={<Search size={16} color={theme.colors['input.icon']} />}
        placeholder="Search Icon"
        value={searchKeyword}
        onChange={({ currentTarget: { value } }) => {
          setSearchKeyword(value);
        }}
        overrides={{
          Root: {
            style: () => ({
              ...expand({
                border: '0',
              }),
              marginBottom: '8px',
            }),
          },
        }}
      />
      <SelectIcon {...props} searchKeyword={searchKeyword} />
    </div>
  );
}

export type Props = {
  searchKeyword?: string;
  iconURL: string | undefined;
  isCloud: boolean;
  themeType?: 'light' | 'dark';
  onIconSelected: (iconURL: string, iconName?: string) => void;
  onIconDeleted?: (iconURL: string, iconName?: string) => void;
};

export function getTag(icon: string) {
  const tag = icon
    .split('.')[0]
    .split('-')
    .filter((token) => isNaN(+token))
    .join(' ')
    .trim()
    .toLowerCase();
  return tag;
}

// generate the main body of all icons
export function SelectIcon({
  iconURL,
  onIconSelected,
  onIconDeleted,
  searchKeyword,
  themeType = 'light',
  isCloud,
}: Props) {
  const queryClient = useQueryClient();
  const inputRef = useRef<HTMLInputElement>();
  const baseUrl = isCloud ? getBaseURL() || '' : '';

  const icons = useMemo(() => {
    return buildInIcons
      .map((icon) => {
        return {
          path: `${baseUrl}/studio/assets/gvis/icons/builtin/64/${icon}`,
          tag: getTag(icon),
          name: icon,
          buildIn: true,
        };
      })
      .sort((a, b) => {
        if (a.tag > b.tag) {
          return 1;
        } else if (a.tag < b.tag) {
          return -1;
        } else {
          return 0;
        }
      });
  }, [baseUrl]);

  const [css, theme] = useStyletron();

  const { data: uploadIcons = [] } = useQuery<
    { tag: string; path: string; name: string; buildIn: boolean }[],
    AxiosError
  >(['user_icons'], async () => {
    const response = await getAxiosInstance().get('/api/data/user_icons');
    return response.data.results.map((item: { name: string }) => ({
      tag: getTag(item.name),
      path: uploadedIconPath(item.name, isCloud),
      name: item.name,
      buildIn: false,
    }));
  });

  let fileName = '';
  const uploadMutation = useMutation<Response, AxiosError, { imageFile: File }>(
    async ({ imageFile }) => {
      const formData = new FormData();
      formData.set('file', imageFile);
      fileName = imageFile.name;
      return await getAxiosInstance().post(`/api/data/user_icons/upload`, formData);
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['user_icons']);
        onIconSelected(uploadedIconPath(fileName, isCloud));
        showToast({
          kind: KIND.positive,
          message: `Uploaded the icon.`,
        });
      },
      onError: (err) => {
        // @ts-ignore
        if (err.response?.data?.message?.includes('already exists under path')) {
          showToast({
            kind: KIND.negative,
            message: `Upload the icon failed. \nFile name already exists!`,
          });
          return;
        }
        showToast({
          kind: KIND.negative,
          message: `Upload the icon failed. \n${getErrorMessage(err)}`,
        });
      },
    }
  );

  const deleteMutation = useMutation<Response, AxiosError, { name: string; path: string }>(
    async ({ name }) => {
      return await getAxiosInstance().delete(`/api/data/user_icons?path=${encodeURIComponent(name)}`);
    },
    {
      onSuccess: (_, { name, path }) => {
        queryClient.invalidateQueries(['user_icons']);
        showToast({
          kind: KIND.positive,
          message: `Deleted the icon.`,
        });
        onIconDeleted?.(path, name);
      },
      onError: (err) => {
        showToast({
          kind: KIND.negative,
          message: `Delete the icon failed. \n${getErrorMessage(err)}`,
        });
      },
    }
  );

  const allIcons = uploadIcons.concat(icons);

  const iconButtonSize = 40;
  const innerIconSize = 32;

  return (
    <div>
      <div
        className={css({
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'space-between',
          rowGap: '4px',
          columnGap: '4px',
        })}
      >
        <SelectIconButton
          overrides={{
            BaseButton: {
              style: {
                width: `${iconButtonSize}px`,
                height: `${iconButtonSize}px`,
                ...expand({
                  border: '1px dashed ' + theme.colors['button.border'],
                }),
              },
            },
          }}
          onClick={() => {
            if (inputRef.current) {
              inputRef.current.click();
            }
          }}
        >
          <>
            <UploadIcon />
            <input
              onChange={(e) => {
                let files = e.target.files;
                if (files.length > 0) {
                  uploadMutation.mutate({ imageFile: files[0] });
                }
                inputRef.current.value = '';
              }}
              name="file"
              type="file"
              accept="image/png"
              ref={inputRef}
              tabIndex={-1}
              style={{ display: 'none' }}
            />
          </>
        </SelectIconButton>
        <SelectIconButton
          overrides={{
            BaseButton: {
              style: {
                width: `${iconButtonSize}px`,
                height: `${iconButtonSize}px`,
              },
            },
          }}
          onClick={() => {
            if (inputRef.current) {
              onIconSelected('', '');
            }
          }}
        >
          None
        </SelectIconButton>
        {allIcons.map((icon, index) =>
          !searchKeyword || !searchKeyword.trim() || icon.tag.includes(searchKeyword.trim()) ? (
            <div style={{ position: 'relative' }} key={`${'buildIn_' + index}`}>
              <SelectIconButton
                overrides={{
                  BaseButton: {
                    style: {
                      width: `${iconButtonSize}px`,
                      height: `${iconButtonSize}px`,
                    },
                  },
                }}
                onClick={() => {
                  onIconSelected(icon.path, icon.buildIn ? `icon_${icon.name.split('-')[0].trim()}` : icon.name);
                }}
                selected={icon.path === iconURL}
              >
                <img
                  src={icon.path}
                  alt={icon.tag}
                  className={css({
                    width: `${innerIconSize}px`,
                    maxHeight: `${innerIconSize}px`,
                    filter: `${icon.buildIn && themeType === 'light' ? 'invert(1)' : 'none'}`,
                  })}
                />
              </SelectIconButton>
              {!icon.buildIn ? (
                <Button
                  type="button"
                  shape="square"
                  kind="destructive"
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteMutation.mutate({
                      name: icon.name,
                      path: icon.path,
                    });
                  }}
                  overrides={{
                    BaseButton: {
                      style: {
                        ...expand({ borderRadius: '2px', margin: '2px', border: '0' }),
                        backgroundColor: 'transparent',
                        position: 'absolute',
                        top: 0,
                        right: 0,
                        width: '14px',
                        height: '14px',
                      },
                    },
                  }}
                >
                  <Delete size={14} />
                </Button>
              ) : null}
            </div>
          ) : null
        )}
      </div>
    </div>
  );
}

type iconButtonProps = {
  selected?: boolean;
  onClick: () => void;
  children: ReactNode;
  overrides?: ButtonOverrides;
};

// single icon button
export function SelectIconButton(props: iconButtonProps) {
  const [, theme] = useStyletron();
  const IconSelectedBorder = {
    ...expand({
      border: '2px solid ' + theme.colors.secondary800,
      borderRadius: '4px',
    }),
  };

  return (
    <Button
      kind="text"
      shape="square"
      type="button"
      overrides={mergeOverrides(
        {
          BaseButton: {
            style: {
              ...(props.selected ? IconSelectedBorder : {}),
              backgroundColor: theme.colors['button.background.default'],
            },
          },
        },
        props.overrides as Overrides<any>
      )}
      onClick={() => {
        props.onClick();
      }}
    >
      {props.children}
    </Button>
  );
}

const buildInIcons = [
  '002-mobile-phone-1.png',
  '004-phone-call-2.png',
  '005-mobile-phone.png',
  '006-email-9.png',
  '007-speech-bubble-6.png',
  '008-speech-bubble-5.png',
  '009-speech-bubble-4.png',
  '010-email-8.png',
  '011-email-7.png',
  '012-smartphone-2.png',
  '013-speech-bubble-3.png',
  '014-email-6.png',
  '015-email-5.png',
  '016-email-4.png',
  '017-email-3.png',
  '018-email-2.png',
  '019-phone-book-2.png',
  '020-speech-bubble-2.png',
  '021-speech-bubble-1.png',
  '022-phone-book-1.png',
  '023-phone-call-1.png',
  '024-phone-book.png',
  '025-speech-bubble.png',
  '026-phone-call.png',
  '027-email-1.png',
  '028-email.png',
  '029-planet-1.png',
  '030-growth-1.png',
  '031-owl.png',
  '032-desk-1.png',
  '033-open-book-2.png',
  '034-laptop.png',
  '035-open-book-1.png',
  '036-book-2.png',
  '037-pencil.png',
  '038-growth.png',
  '039-diploma-1.png',
  '040-graphic-tool.png',
  '041-calculator.png',
  '042-online-learning.png',
  '043-column.png',
  '044-telescope.png',
  '045-book-1.png',
  '046-hourglass.png',
  '047-rook.png',
  '048-open-book.png',
  '049-doubt.png',
  '050-apple-1.png',
  '051-diploma.png',
  '052-cells.png',
  '053-solar-system.png',
  '054-ascend.png',
  '055-brain-1.png',
  '056-chemistry.png',
  '057-hand-mirror.png',
  '058-mortarboard.png',
  '059-earth-globe.png',
  '060-atomic.png',
  '061-planet.png',
  '062-medal.png',
  '063-books.png',
  '064-learning.png',
  '065-microscope.png',
  '066-desk.png',
  '067-graduate.png',
  '068-college.png',
  '069-startup.png',
  '070-idea-4.png',
  '071-satellite.png',
  '072-studying.png',
  '073-university-1.png',
  '074-idea-3.png',
  '075-university.png',
  '076-brain.png',
  '077-idea-2.png',
  '078-trophy.png',
  '079-security-code-1.png',
  '080-secure-file.png',
  '081-spy-1.png',
  '082-hacked-computer.png',
  '083-shield.png',
  '084-life-preserver.png',
  '085-security-camera.png',
  '086-bomb.png',
  '087-open-padlock.png',
  '088-wall.png',
  '089-infected-folder.png',
  '090-spam.png',
  '091-trojan.png',
  '092-infected-file.png',
  '093-locked-email.png',
  '094-secure-download.png',
  '095-password.png',
  '096-strongbox.png',
  '097-spy-alarm-on-laptop.png',
  '098-secure-cloud.png',
  '099-magnifying-glass.png',
  '100-warning.png',
  '101-alarm.png',
  '102-secure-pendrive.png',
  '103-secure-smartphone.png',
  '104-virus.png',
  '105-computer-secure.png',
  '106-key.png',
  '107-security-code.png',
  '108-spy.png',
  '109-ecologic-light-bulb-1.png',
  '110-basket-1.png',
  '111-global-warming.png',
  '112-piggy-bank.png',
  '113-melting.png',
  '114-mobile-store.png',
  '115-computer-1.png',
  '116-statistics.png',
  '117-tag-1.png',
  '118-voucher.png',
  '119-plant.png',
  '120-location-1.png',
  '121-battery.png',
  '122-delivery-man.png',
  '123-socket.png',
  '124-cash-machine.png',
  '125-smartphone.png',
  '126-search-3.png',
  '127-gasoline.png',
  '128-barcode.png',
  '129-recycled-bag.png',
  '130-secure-web.png',
  '131-plug.png',
  '132-search-2.png',
  '133-cash-payment.png',
  '134-placeholder.png',
  '135-cash.png',
  '136-secure-payment.png',
  '137-home.png',
  '138-nuclear-plant.png',
  '139-online-shopping.png',
  '140-recycled-paper.png',
  '141-secure-shopping.png',
  '142-computer.png',
  '143-water-2.png',
  '144-shopping-cart.png',
  '145-co2-cloud.png',
  '146-cart.png',
  '147-windmill.png',
  '148-unpacking.png',
  '149-ladybug.png',
  '150-radiation.png',
  '151-credit-card.png',
  '152-shamrock.png',
  '153-call.png',
  '154-store-1.png',
  '155-book.png',
  '156-water-bottle.png',
  '157-basket.png',
  '158-recycle-bin.png',
  '159-auction.png',
  '160-seed.png',
  '161-schedule.png',
  '162-water-1.png',
  '163-deposit.png',
  '164-product.png',
  '165-earth.png',
  '166-sunflower.png',
  '167-like.png',
  '168-dispute.png',
  '169-bicycle.png',
  '170-search-1.png',
  '171-barrel.png',
  '172-store.png',
  '173-leaf.png',
  '174-leaves.png',
  '175-review-1.png',
  '176-package-2.png',
  '177-recycling.png',
  '178-electric-car.png',
  '179-tag.png',
  '180-package-1.png',
  '181-garbage-truck.png',
  '182-customer-service.png',
  '183-bolt.png',
  '184-sales.png',
  '185-electric-station.png',
  '186-receipt.png',
  '187-waste.png',
  '188-ecologic-light-bulb.png',
  '189-delivery-1.png',
  '190-solar-panel.png',
  '191-review.png',
  '192-package.png',
  '193-water-tap.png',
  '194-trees.png',
  '195-coupon-1.png',
  '196-apple.png',
  '197-coupon.png',
  '198-water.png',
  '199-support.png',
  '200-sprout-1.png',
  '201-safety.png',
  '202-delivery.png',
  '203-sprout.png',
  '204-sun.png',
  '205-new.png',
  '206-wallet.png',
  '207-factory.png',
  '208-group-2.png',
  '209-strategic.png',
  '210-promotion-1.png',
  '211-goal.png',
  '212-analytics.png',
  '213-id-card.png',
  '214-video-conference.png',
  '215-office-block.png',
  '216-location.png',
  '217-presentation-4.png',
  '218-selection.png',
  '219-settings.png',
  '220-time.png',
  '221-managers.png',
  '222-presentation-3.png',
  '223-target.png',
  '224-presentation-2.png',
  '225-rank.png',
  '226-speech-1.png',
  '227-pie-chart.png',
  '228-workspace.png',
  '229-manager-6.png',
  '230-presentation-1.png',
  '231-boss.png',
  '232-businessmen.png',
  '233-speech.png',
  '234-idea-1.png',
  '235-promotion.png',
  '236-promoting.png',
  '237-search.png',
  '238-idea.png',
  '239-decision-making-1.png',
  '240-manager-5.png',
  '241-manager-4.png',
  '242-presentation.png',
  '243-collaboration-1.png',
  '244-group-1.png',
  '245-manager-3.png',
  '246-manager-2.png',
  '247-collaboration.png',
  '248-organization.png',
  '249-decision-making.png',
  '250-manager-1.png',
  '251-strategy.png',
  '252-meeting-1.png',
  '253-management.png',
  '254-meeting.png',
  '255-group.png',
  '256-manager.png',
  '257-google.png',
  '258-address-1.png',
  '259-address-2.png',
  '260-bank-card-1.png',
  '261-bank-card-2.png',
  '262-id-1.png',
  '263-id-2.png',
  '264-id-3.png',
  '265-order-1.png',
  '266-order-2.png',
  '267-company-1.png',
  '268-company-2.png',
  '269-credit-card.png',
  '270-device-1.png',
  '271-device-2.png',
  '272-ip-1.png',
  '273-ip-2.png',
  '274-person.png',
  '275-user.png',
  '276-profile.png',
  '277-graph.png',
  '278-circular-pie-chart.png',
  '279-clock.png',
  '280-grocery.png',
  '281-group.png',
  '282-id-card.png',
  '283-payment.png',
  '284-money.png',
  '285-asset.png',
  '286-phone-1.png',
  '287-phone-2.png',
  '288-smart-phone.png',
  '289-pin-1.png',
  '290-pin-2.png',
  '291-pin-3.png',
  '292-transaction-1.png',
  '293-transaction-2.png',
  '294-transaction-3.png',
  '295-transaction-4.png',
  '296-s3-file.png',
];
