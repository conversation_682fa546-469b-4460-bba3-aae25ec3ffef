import { styled, useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Link } from 'react-router-dom';
import React, { useState } from 'react';
import { Heading, HeadingLevel } from 'baseui/heading';

import LogoIcon from '../assets/images/logo.svg';

export const NavBarHeight = 40;

export const NavBar = styled('section', ({ $theme }) => {
  return {
    position: 'fixed',
    left: 0,
    right: 0,
    height: `${NavBarHeight}px`,
    boxSizing: 'border-box',
    padding: '8px 22px 8px 16px',
    boxShadow: '0px 4px 5px rgb(41 69 96 / 5%)',
    borderBottom: '1px solid rgb(212, 218, 223)',
    display: 'flex',
    alignItems: 'center',
    backgroundColor: 'white',
  };
});

export const NavIcon = ({ iconURL, to = '/apps', size }: { iconURL?: string; to?: string; size?: number }) => {
  if (!size || size < 0) {
    size = 24;
  }
  let [iconStatus, changeIconStatus] = useState(true);
  const [css] = useStyletron();
  return (
    <Link to={to}>
      <img
        src={iconURL && iconStatus ? iconURL : LogoIcon}
        onError={() => {
          changeIconStatus(false);
        }}
        alt="logo"
        className={css({
          display: 'block',
          height: `${size}px`,
          marginRight: '10px',
          filter: `invert(${iconStatus && iconURL && iconURL.indexOf('builtin') > 0 ? 1 : 0})`,
        })}
      />
    </Link>
  );
};

export const NavTitle = (props) => {
  const [, theme] = useStyletron();
  return (
    <HeadingLevel>
      <Heading
        {...props}
        overrides={{
          Block: {
            style: {
              ...theme.typography.HeadingMedium,
              margin: '0',
              color: theme.colors.gray1000,
              fontSize: `${props.fontSize}px`,
              flex: 1,
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
          },
        }}
      />
    </HeadingLevel>
  );
};
