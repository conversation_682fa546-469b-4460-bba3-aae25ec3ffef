import { Input } from '@tigergraph/app-ui-lib/input';
import React from 'react';
import { InputProps, SIZE } from 'baseui/input';
import { expand } from 'inline-style-expand-shorthand';
import { mergeOverrides } from 'baseui';
import { Overrides } from 'baseui/overrides';

export default function StyledInput({ overrides, ...props }: InputProps) {
  return (
    <Input
      overrides={mergeOverrides(
        {
          Root: {
            style: (props) => {
              const { $isFocused, $theme } = props;
              let border = $isFocused ? `1px solid ${$theme.colors.accent}` : null;
              return {
                width: '100%',
                ...expand(border ? { border } : {}),
                fontSize: '14px',
                ...overrides?.Root?.['style'],
              };
            },
          },
          ClearIcon: {
            props: {
              overrides: {
                Svg: {
                  style: ({ $theme }) => ({}),
                },
              },
            },
          },
        },
        overrides as Overrides<any>
      )}
      {...props}
    />
  );
}
