import { ReactNode } from 'react';
import { mergeOverrides, useStyletron } from 'baseui';
import { PLACEMENT, PopoverOverrides, StatefulPopover } from 'baseui/popover';
import IconButton from './iconButton';
import { MdEdit } from '@react-icons/all-files/md/MdEdit';
import ConfirmButtons from './confirmButtons';
import { expand } from 'inline-style-expand-shorthand';
import { Overrides } from 'baseui/overrides';

type Props = {
  popover: ReactNode | ((args: { close: () => void }) => ReactNode);
  label?: ReactNode;
  overrides?: PopoverOverrides;
  onConfirm?: () => void;
  onCancel?: () => void;
  onOpen?: () => void;
};

export default function IconPopover(props: Props) {
  const [css, theme] = useStyletron();
  const { popover, overrides, label, onConfirm, onCancel, onOpen } = props;

  return (
    <StatefulPopover
      dismissOnEsc={false}
      dismissOnClickOutside={false}
      ignoreBoundary={true}
      popperOptions={{
        modifiers: {
          preventOverflow: {
            enabled: true,
          },
        },
      }}
      content={({ close }) => (
        <div
          className={css({
            display: 'flex',
            flexDirection: 'column',
            rowGap: '8px',
          })}
        >
          {typeof popover === 'function' ? popover({ close }) : popover}
          {onConfirm && (
            <ConfirmButtons
              onConfirm={() => {
                close();
                onConfirm();
              }}
              onCancel={() => {
                close();
                onCancel && onCancel();
              }}
            />
          )}
        </div>
      )}
      placement={PLACEMENT.left}
      onOpen={() => onOpen && onOpen()}
      returnFocus
      autoFocus
      overrides={mergeOverrides(
        {
          Body: {
            style: {
              ...expand({
                borderRadius: '5px',
              }),
              padding: '8px',
              backgroundColor: '#fff',
              width: '320px',
              boxSizing: 'border-box',
            },
          },
          Inner: {
            style: {
              backgroundColor: '#fff',
              // flex: 1,
            },
          },
        },
        overrides as Overrides<any>
      )}
    >
      {label ? (
        label
      ) : (
        <IconButton
          $style={{
            color: theme.colors.accent,
          }}
        >
          <MdEdit size={16} />
        </IconButton>
      )}
    </StatefulPopover>
  );
}
