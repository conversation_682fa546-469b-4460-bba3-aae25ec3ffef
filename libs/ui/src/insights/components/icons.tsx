import React from 'react';

export function EditIcon() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M2.5 14.55V17.0833C2.5 17.3167 2.68333 17.5 2.91667 17.5H5.45C5.55833 17.5 5.66667 17.4583 5.74167 17.375L14.8417 8.28333L11.7167 5.15833L2.625 14.25C2.54167 14.3333 2.5 14.4333 2.5 14.55ZM17.2583 5.86667C17.5833 5.54167 17.5833 5.01667 17.2583 4.69167L15.3083 2.74167C14.9833 2.41667 14.4583 2.41667 14.1333 2.74167L12.6083 4.26667L15.7333 7.39167L17.2583 5.86667V5.86667Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function DuplicateIcon() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.3334 0.833328H3.33341C2.41675 0.833328 1.66675 1.58333 1.66675 2.49999V14.1667H3.33341V2.49999H13.3334V0.833328ZM15.8334 4.16666H6.66675C5.75008 4.16666 5.00008 4.91666 5.00008 5.83333V17.5C5.00008 18.4167 5.75008 19.1667 6.66675 19.1667H15.8334C16.7501 19.1667 17.5001 18.4167 17.5001 17.5V5.83333C17.5001 4.91666 16.7501 4.16666 15.8334 4.16666ZM15.8334 17.5H6.66675V5.83333H15.8334V17.5Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function SettingIcon() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M15.9499 10.7833C15.9833 10.5333 15.9999 10.275 15.9999 10C15.9999 9.73333 15.9833 9.46667 15.9416 9.21667L17.6333 7.9C17.7833 7.78333 17.8249 7.55833 17.7333 7.39167L16.1333 4.625C16.0333 4.44167 15.8249 4.38333 15.6416 4.44167L13.6499 5.24167C13.2333 4.925 12.7916 4.65833 12.2999 4.45833L11.9999 2.34167C11.9666 2.14167 11.7999 2 11.5999 2H8.39993C8.19993 2 8.0416 2.14167 8.00826 2.34167L7.70826 4.45833C7.2166 4.65833 6.7666 4.93333 6.35826 5.24167L4.3666 4.44167C4.18326 4.375 3.97493 4.44167 3.87493 4.625L2.28326 7.39167C2.18326 7.56667 2.2166 7.78333 2.38326 7.9L4.07493 9.21667C4.03326 9.46667 3.99993 9.74167 3.99993 10C3.99993 10.2583 4.0166 10.5333 4.05826 10.7833L2.3666 12.1C2.2166 12.2167 2.17493 12.4417 2.2666 12.6083L3.8666 15.375C3.9666 15.5583 4.17493 15.6167 4.35826 15.5583L6.34993 14.7583C6.7666 15.075 7.20826 15.3417 7.69993 15.5417L7.99993 17.6583C8.0416 17.8583 8.19993 18 8.39993 18H11.5999C11.7999 18 11.9666 17.8583 11.9916 17.6583L12.2916 15.5417C12.7833 15.3417 13.2333 15.075 13.6416 14.7583L15.6333 15.5583C15.8166 15.625 16.0249 15.5583 16.1249 15.375L17.7249 12.6083C17.8249 12.425 17.7833 12.2167 17.6249 12.1L15.9499 10.7833ZM9.99993 13C8.34993 13 6.99993 11.65 6.99993 10C6.99993 8.35 8.34993 7 9.99993 7C11.6499 7 12.9999 8.35 12.9999 10C12.9999 11.65 11.6499 13 9.99993 13Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function DeleteIcon() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" pointerEvents="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.85714 18.2222C6.85714 19.2 7.62857 20 8.57143 20H15.4286C16.3714 20 17.1429 19.2 17.1429 18.2222V7.55556H6.85714V18.2222ZM8.57143 9.33333H15.4286V18.2222H8.57143V9.33333ZM15 4.88889L14.1429 4H9.85714L9 4.88889H6V6.66667H18V4.88889H15Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function PreviewIcon() {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10.6667 9.09333V22.9067C10.6667 23.96 11.8267 24.6 12.7201 24.0267L23.5734 17.12C24.4001 16.6 24.4001 15.4 23.5734 14.8667L12.7201 7.97333C11.8267 7.4 10.6667 8.04 10.6667 9.09333Z"
        fill="#656565"
      />
    </svg>
  );
}

export function Ellipsis() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" pointerEvents="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12 8C13.1 8 14 7.1 14 6C14 4.9 13.1 4 12 4C10.9 4 10 4.9 10 6C10 7.1 10.9 8 12 8ZM12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10ZM12 16C10.9 16 10 16.9 10 18C10 19.1 10.9 20 12 20C13.1 20 14 19.1 14 18C14 16.9 13.1 16 12 16Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function People() {
  return (
    <svg width="22" height="14" viewBox="0 0 22 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M15 6C16.66 6 17.99 4.66 17.99 3C17.99 1.34 16.66 0 15 0C13.34 0 12 1.34 12 3C12 4.66 13.34 6 15 6ZM7 6C8.66 6 9.99 4.66 9.99 3C9.99 1.34 8.66 0 7 0C5.34 0 4 1.34 4 3C4 4.66 5.34 6 7 6ZM7 8C4.67 8 0 9.17 0 11.5V14H14V11.5C14 9.17 9.33 8 7 8ZM15 8C14.71 8 14.38 8.02 14.03 8.05C15.19 8.89 16 10.02 16 11.5V14H22V11.5C22 9.17 17.33 8 15 8Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function Public() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM9 17.93C5.05 17.44 2 14.08 2 10C2 9.38 2.08 8.79 2.21 8.21L7 13V14C7 15.1 7.9 16 9 16V17.93ZM15.9 15.39C15.64 14.58 14.9 14 14 14H13V11C13 10.45 12.55 10 12 10H6V8H8C8.55 8 9 7.55 9 7V5H11C12.1 5 13 4.1 13 3V2.59C15.93 3.78 18 6.65 18 10C18 12.08 17.2 13.97 15.9 15.39Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function ExpandMore() {
  return (
    <svg id="Icons" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
      <title>ic_expand_more</title>
      <path d="M7.41,8.3,6,9.7l6,6,6-6L16.59,8.3,12,12.88Z" fill="#6e6e6e" />
    </svg>
  );
}
