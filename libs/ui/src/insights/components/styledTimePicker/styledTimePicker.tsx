import React, { useState, useMemo } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { StatefulPopover, PLACEMENT } from 'baseui/popover';
import { mergeOverrides } from 'baseui';
import { Overrides } from 'baseui/overrides';
import { ItemsT } from 'baseui/menu';
import StyledInput from '../styledInput';
import StyledTimePickerMenu from './styledTimePickerMenu';
import { StyledTimePickerProps } from './type';
import { validateTime, refineTime } from './util';

// time format HH:mm:ss only for now
export default function StyledTimePicker(props: StyledTimePickerProps) {
  const [css] = useStyletron();
  const { value, onChange, use12Hours } = props;

  const [inputTime, setInputTime] = useState<string>(validateTime(value));
  const [hour, minute, second] = refineTime(inputTime).split(':');

  const buildOptions = (range: number): ItemsT => {
    const options = [];
    for (let i = 0; i < range; i++) {
      options.push({ label: String(i).padStart(2, '0') });
    }
    return options;
  };

  const { hourList, minuteList, secondList } = useMemo(() => {
    const hourRange = use12Hours ? 12 : 24;
    const minuteRange = 60;
    const secondRange = 60;
    const hourList = buildOptions(hourRange),
      minuteList = buildOptions(minuteRange),
      secondList = buildOptions(secondRange);
    return {
      hourList,
      minuteList,
      secondList,
    };
  }, [use12Hours]);

  return (
    <StatefulPopover
      content={
        <div
          className={css({
            maxHeight: '200px',
            display: 'flex',
            padding: '8px 4px',
            boxSizing: 'border-box',
          })}
        >
          <StyledTimePickerMenu
            highlightedItem={hour}
            items={hourList}
            onItemSelect={({ item }) => {
              const value = `${item.label}:${minute}:${second}`;
              setInputTime(value);
            }}
          />
          <StyledTimePickerMenu
            highlightedItem={minute}
            items={minuteList}
            onItemSelect={({ item }) => {
              const value = `${hour}:${item.label}:${second}`;
              setInputTime(value);
            }}
          />
          <StyledTimePickerMenu
            highlightedItem={second}
            items={secondList}
            onItemSelect={({ item }) => {
              const value = `${hour}:${minute}:${item.label}`;
              setInputTime(value);
            }}
          />
        </div>
      }
      onClose={() => {
        if (inputTime) {
          onChange(inputTime);
        }
      }}
      placement={PLACEMENT.bottomLeft}
      popoverMargin={4}
      returnFocus
      autoFocus={false}
      {...props}
      overrides={mergeOverrides(
        {
          Inner: {
            style: {
              maxHeight: '200px',
            },
          },
        },
        props.overrides as Overrides<any>
      )}
    >
      <div>
        <StyledInput
          value={inputTime}
          onChange={(e) => {
            const value = e.target['value'];
            setInputTime(value);
            if (validateTime(value)) {
              onChange(validateTime(value));
            }
          }}
          placeholder="HH:mm:ss"
        />
      </div>
    </StatefulPopover>
  );
}
