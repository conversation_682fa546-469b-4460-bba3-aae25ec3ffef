import React, { useRef, useEffect } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { mergeOverrides } from 'baseui';
import { Overrides } from 'baseui/overrides';
import { MenuOverrides, StatefulMenu } from 'baseui/menu';
import { expand } from 'inline-style-expand-shorthand';
import { StyledMenuProps } from './type';

export default function StyledTimePickerMenu(props: StyledMenuProps) {
  const [css, theme] = useStyletron();
  const { highlightedItem } = props;
  const menuRef = useRef<HTMLDivElement>();
  const firstRender = useRef<boolean>(false);
  const itemHeight = 32;

  useEffect(() => {
    if (menuRef.current && !firstRender.current) {
      menuRef.current.scrollTop = parseInt(highlightedItem) * itemHeight;
      firstRender.current = true;
    }
  }, [highlightedItem]);

  return (
    <div
      ref={menuRef}
      className={css({
        overflow: 'auto',
        scrollbarWidth: 'none',
        '::-webkit-scrollbar': {
          display: 'none',
        },
      })}
    >
      <StatefulMenu
        {...props}
        overrides={mergeOverrides(
          {
            List: {
              style: {
                ...expand({
                  padding: '0',
                }),
              },
            },
            ListItem: {
              style: (p) => {
                if (!p['id']) {
                  return;
                }
                const item = p['id'].split('-')[1].padStart(2, '0');
                return {
                  backgroundColor: item === highlightedItem ? theme.colors['background.selected.subtlest'] : 'none',
                  fontSize: '16px',
                  lineHeight: '24px',
                  ...expand({
                    padding: '4px 16px 4px 8px',
                  }),
                };
              },
            },
          },
          props.overrides as Overrides<MenuOverrides>
        )}
      />
    </div>
  );
}
