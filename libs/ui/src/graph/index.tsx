import cytoscape, {
  Core,
  Layouts,
  ElementDefinition,
  Ext,
  Stylesheet,
  LayoutOptions,
  EventHandler,
  Collection,
} from 'cytoscape';
import React, { useRef, useMemo, useEffect, useState, useCallback, MutableRefObject, forwardRef } from 'react';

import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import useSize from 'ahooks/lib/useSize';
import { NavigateFunction } from 'react-router';
import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { TRIGGER_TYPE } from 'baseui/popover';
import { Button } from '@tigergraph/app-ui-lib/button';
import { KIND, SHAPE } from 'baseui/button';
import { expand } from 'inline-style-expand-shorthand';

import { TbLayoutSidebarLeftExpand, TbLayoutSidebarRightExpand } from 'react-icons/tb';

import { get, toJson, forEach } from './react-cytoscapejs/json';
import { patchElements } from './react-cytoscapejs/patch';
import { shallowObjDiff } from './react-cytoscapejs/diff';

import popper from 'cytoscape-popper';
import dagre from 'cytoscape-dagre';
// @ts-ignore
import cytoscapeCola from './cola/cytoscape-cola';
// @ts-ignore
import edgeConnections from './cytoscape-edge-connections';
// @ts-ignore
import edgehandles from '@tigergraph/cytoscape-edgehandles';

import { Provider, createGraphForCytoscape } from './context';

import { createStyleSheet } from './stylesheet';
import { convertGraphToCytoscape, canonicalizeGraph, removeDuplicateEdges, isSameGraph } from './data';

import Bottom from './ui/bottom';

import { CytoscapeExtensions, GraphEvents, GraphRef, NodePositions, Schema, SettingType } from './type';
import { ExternalGraph } from '@tigergraph/tools-models/gvis/insights';
import { Hover, shouldHandleEvent } from './ui/hover';
import { useGraphEvents } from './hooks';
import ContextMenu from './react-menu';
import useUndoRedo from './useUndoRedo';
import UndoRedo from './ui/undoRedo';
import LeftUI from './ui/leftUI';
import { updateGraphStyle, updateGraphLabel } from './util';
import { GlobalParams } from '../globalParams';
import { getLayoutByLabel } from './layouts';
import ElementDetail from './ui/elementDetail';
import { StyledToasterContainer } from '../styledToasterContainer';
import { CytoscapeComponent2 } from './react-cytoscapejs/component';

cytoscape.use(dagre);
// @ts-ignore
cytoscape.use(cytoscapeCola as Ext);
cytoscape.use(edgeConnections);
cytoscape.use(popper);
cytoscape.use(edgehandles);

// the following code is comment out, we may later need to debug memory leak issue

// let wasmInstance: any;
// async function initWasm() {
//   // @ts-ignore
//   wasmInstance = await cytoscapeCola.cola.getDerivativeComputerWasm();
// }

// initWasm();

// function debugMemory() {
//   if (!wasmInstance) {
//     return;
//   }
//   // @ts-ignore
//   console.log(wasmInstance.getWasm().memory.buffer.byteLength / 1024 / 1024);
// }

// setInterval(() => {
//   debugMemory();
// }, 1000);

const graphWidgetStyle = { width: '100%', height: '100%' };

function shouldDisableInteractiveLayout(cy: Core) {
  return cy.nodes().length > 1000;
}

const defaultSetting: Required<SettingType> = {
  layout: 'Force',

  edgeLength: 200,

  spacingFactor: 1.5,

  nodeSep: 50,
  edgeSep: 50,
  rankSep: 200,
  rankDir: 'TB',
  align: 'Center',

  showThumbNail: false,
  showUndoRedo: false,

  actionsByType: {},
  rulesByType: {},
  attrMapGraphLabel: {},
  attrMapGraphLabelShow: {},
};

export type Props = {
  id: string;
  schema: Schema;
  globalTypes?: string[];

  graphName: string;

  graph: ExternalGraph;
  // for schema graph, we will hide some ui like context menu, table etc.
  isSchemaGraph?: boolean;
  hideLeftUI?: boolean;
  hideContextMenu?: boolean;
  // for view mode, we can switch layout, but for edit mode, we use preset layout
  schemaMode?: 'edit' | 'view';
  presetNodePositions?: NodePositions;
  onGraphChange: (graph: ExternalGraph) => void;

  settings: SettingType;
  onSettingUpdate?: (key: string, value: any) => void;

  parentRef?: MutableRefObject<(Core & CytoscapeExtensions) | null>;

  // this is needed for insights, we evaluate graph styling based on GlobalParams
  globalParameters?: GlobalParams;
  // for mapping data and load data, we need to createAuxNode.
  createEdgeAuxNode?: boolean;

  navigate?: NavigateFunction;
  links?: { pageName: string; pageID: string; params: GlobalParams }[];
  getLink?: (pageID: string, params: Record<string, string>) => string;
  isEditMode?: boolean;
  showHoverTooltip?: boolean;
  hoverTooltipDelay?: number;

  userUploadedIconPathPrefix?: string;

  children?: React.ReactNode;

  insights?: boolean;

  // control whether to show ghost node
  showGhostNode?: boolean;
} & GraphEvents;

const defaultGlobalType: string[] = [];
const defaultOnSettingUpdate = () => {};
const defaultGlobalParameters = {};

const Graph = forwardRef<GraphRef, Props>((props, ref) => {
  const {
    id,
    schema,
    globalTypes = defaultGlobalType,
    graphName,
    isSchemaGraph = false,
    hideLeftUI,
    hideContextMenu,
    presetNodePositions,
    onGraphChange,
    settings,
    onSettingUpdate = defaultOnSettingUpdate,
    parentRef,
    globalParameters = defaultGlobalParameters,
    createEdgeAuxNode,

    navigate,
    links,
    getLink,
    isEditMode = false,
    showHoverTooltip = true,
    hoverTooltipDelay = 500,

    userUploadedIconPathPrefix,

    insights = false,
  } = props;

  const effectiveSettings = useMemo(() => {
    return {
      ...defaultSetting,
      ...settings,
    };
  }, [settings]);

  const {
    layout,
    // Force Cola
    edgeLength,

    // Radial && Layer
    spacingFactor,

    // Tree
    nodeSep,
    edgeSep,
    rankSep,
    rankDir,
    align,

    showThumbNail,
    showUndoRedo,

    actionsByType,
    rulesByType,
  } = effectiveSettings;

  // treat cy as state,as we want to rerender when get cy instance.
  const [cy, setCy] = useState<(Core & CytoscapeExtensions) | null>(null);

  const layoutsRef = useRef<Layouts | null>(null);
  const layoutTimerRef = useRef<NodeJS.Timeout | null>(null);

  const elementsRef = useRef<ElementDefinition[]>([]);

  const [showAnalysis, setShowAnalysis] = useState(false);

  const [showDetailPanel, setShowDetailPanel] = useState(false);
  const isFirstElementClickRef = useRef(false);
  const [detailData, setDetailData] = useState<Record<string, any> | null>(null);

  const setShowThumbNail = useCallback(
    (showThumbNail: boolean) => {
      onSettingUpdate('showThumbNail', showThumbNail);
    },
    [onSettingUpdate]
  );

  const [css, theme] = useStyletron();

  const domRef = useRef<HTMLElement | null>(null);
  const size = useSize(domRef);

  const [previousGraph, setPreviousGraph] = useState<ExternalGraph>(props.graph);

  const memoGraphFromProps = useMemo(() => {
    if (isSameGraph(props.graph, previousGraph)) {
      return previousGraph;
    }
    // track previous graph
    setPreviousGraph(props.graph);
    return props.graph;
  }, [props.graph, previousGraph]);

  const graph = useMemo(() => {
    let canonicalizedGraph = canonicalizeGraph(memoGraphFromProps, schema);
    // for init data loading, after reverse the reverse edge, remove duplicate edge.
    canonicalizedGraph = removeDuplicateEdges(canonicalizedGraph);
    return canonicalizedGraph;
  }, [schema, memoGraphFromProps]);

  const layoutName = getLayoutByLabel(layout);

  const layoutOptions: LayoutOptions = useMemo(() => {
    return {
      name: layoutName,
      // todo(lin)
      // set avoidOverlap to false to improve layout performance for cola layout
      // also for wasm-cola integration, we should set avoidOverlap to false
      avoidOverlap: layoutName === 'cola' ? false : true,
      // animate should set to true, or else cytoscape layout algorithm will block ui thread
      animate: true,
      randomize: true,
      fit: false,
      padding: 24,

      // todo(lin)
      //  for cola, this may effect the layout performance, need to do some test
      nodeDimensionsIncludeLabels: true,
      quality: 'proof',

      edgeLength,
      handleDisconnected: true,
      // known issue in cola layout: if we start graph with nodes and no edges
      // the if we drag and drop node, the node will move to the initial position
      infinite: true,

      spacingFactor,
      directed: true,

      nodeSep,
      edgeSep,
      rankSep,
      rankDir,
      // special handling for tree align
      align: align === 'Center' ? undefined : align,
    };
  }, [layoutName, edgeLength, spacingFactor, nodeSep, edgeSep, rankSep, rankDir, align]);

  // known issue:  aux node/edge will cause cola layout unstable
  // we currently do not run force layout on graph with aux node/edge
  const runLayout = useCallback(
    (extraParams?: Record<string, any>, onLayoutStop?: () => void) => {
      if (!cy) {
        return;
      }

      if (layoutsRef.current) {
        layoutsRef.current.stop();
        layoutsRef.current = null;
      }

      if (layoutTimerRef.current) {
        clearTimeout(layoutTimerRef.current);
        layoutTimerRef.current = null;
      }

      // default fit to false
      let fit = true;

      const haveFitSetting = extraParams && 'fit' in extraParams;

      if (haveFitSetting) {
        fit = extraParams['fit'] as boolean;
      } else if (layoutOptions.name === 'preset') {
        // !!! for preset layout, we need set fit to false
        fit = false;
      }

      const eles: Collection | undefined = extraParams?.eles;
      if (eles) {
        const { eleType, ...props } = extraParams;
        extraParams = props;
      }

      const finalOptions = {
        ...layoutOptions,
        fit,
        ...extraParams,
        // for large graph, we want large refresh rate so the layout is much quicker to converge but also more jerky
        // refresh: getRefreshSettingForGraph(cy),
      };

      cy.maxZoom(2);

      let layout = cy.layout(finalOptions);
      if (eles) {
        layout = eles.layout(finalOptions);
      }

      cy.one('layoutstop', () => {
        if (onLayoutStop) {
          onLayoutStop();
        }

        // for graph without edges, we stop force layout so nodes drag works (a known issue for cola layout)
        if (finalOptions.name === 'cola' && cy.edges().length === 0) {
          if (layoutsRef.current) {
            layoutsRef.current.stop();
            layoutsRef.current = null;
          }
        }
      });
      // Skip running layout if it's preset layout
      if (layoutOptions.name !== 'preset') {
        // todo
        // for webcola layout, it use priority queue to calculate min distance between nodes
        // for 5k node/edges, it may take 1000ms to do the calculation
        // we can try to use https://github.com/lemire/FastPriorityQueue.js to optimize this.
        layout.run();
      }
      cy.maxZoom(10);

      layoutsRef.current = layout;

      if (finalOptions.name === 'cola') {
        // refer https://www.sciencedirect.com/science/article/pii/S0012365X08000083 4.1. Running time
        let verticesCount = cy.nodes().length;
        let edgesCount = cy.edges().length;

        if (eles) {
          verticesCount = eles.nodes().length;
          edgesCount = eles.edges().length;
        }

        // n: 1337, m: 1208, maxLayoutTime 5958ms
        // n: 2085, m: 2679, maxLayoutTime 18619ms
        // @ts-ignore for node expansion (with randomize false), reduce the maxLayoutTime
        let maxLayoutTime =
          Math.max(verticesCount * verticesCount, verticesCount * edgesCount) / (finalOptions['randomize'] ? 300 : 600);

        if (edgesCount === 0) {
          maxLayoutTime = 2000;
        }

        if (maxLayoutTime <= 1000) {
          maxLayoutTime = 1000;
        }
        if (maxLayoutTime <= 3000 && edgesCount > 0) {
          maxLayoutTime = 3000;
        }

        if (
          layoutsRef.current &&
          // @ts-ignore
          layoutsRef.current.options.name === 'cola' &&
          // when grabon, use release event to stop layout.
          !(extraParams && extraParams['grabon'])
        ) {
          layoutTimerRef.current = setTimeout(() => {
            if (layoutsRef.current) {
              layoutsRef.current.stop();
              layoutsRef.current = null;
            }
            layoutTimerRef.current = null;
          }, maxLayoutTime);
        }
      }
      // for force layout
      // first layout with `fit: true`
      // then set layout options to `fit: false`, so user can drag and move nodes.
      // we could do this in 'layoutstop' callback, but for large graph, it take long time (> 10s) to reach layoutstop state,
      if (finalOptions.name === 'cola' && fit) {
        setTimeout(() => {
          if (layoutsRef.current) {
            // @ts-ignore
            layoutsRef.current.options.fit = false;
          }
        }, 1000);
      }
    },
    [layoutOptions, cy]
  );

  useEffect(() => {
    // handle node dragging
    // type for collection event (grab free) is missing type cytoscape, so we use any
    const grabHandler: EventHandler = function (e: any) {
      const target = e.target;

      if (!target || !target.isNode()) {
        return;
      }

      // @ts-ignore, only consider cola layout
      if (layoutsRef.current && layoutsRef.current.options.name !== 'cola') {
        return;
      }

      // if no edge, skip force layout
      if (cy?.edges().length === 0) {
        return;
      }

      // add check if cola layout is running, is so, just return
      if (layoutTimerRef.current) {
        return;
      }

      switch (e.type) {
        case 'grabon':
          const originalEvent = e.originalEvent;
          // only allow interactive drag if alt/options key is pressed
          if (!originalEvent.altKey) {
            return;
          }
          target.removeScratch('__cola_locked');
          // if (cy && shouldDisableInteractiveLayout(cy)) {
          //   return;
          // }
          runLayout({
            randomize: false,
            fit: false,
            centerGraph: false,
            grabon: true,
          });
          break;
        case 'freeon':
          target.scratch('__cola_locked', true);

          // @ts-ignore
          if (layoutsRef.current && layoutsRef.current.options.name === 'cola') {
            // @ts-ignore
            layoutsRef.current.stop();
            layoutsRef.current = null;
          }
          break;
      }
    };

    // we need to runLayout before drag start so we listen on `grabon` event
    // if we listen on `grab` event, the code on cytoscape-cola will run first and the drag will not work
    cy?.on('grabon freeon ', grabHandler);

    return () => {
      cy?.off('grabon freeon', grabHandler);
    };
  }, [cy, runLayout, layoutsRef]);

  useGraphEvents(cy, ref, props, runLayout);

  // handle widget resize
  useEffect(() => {
    if (cy) {
      cy.resize();
      // Could cause joggle when GST is changing window size
      // fitView(cyRef.current);
    }
  }, [size, cy]);

  // generate elements based on graph data
  useEffect(() => {
    if (!cy) {
      return;
    }

    if (cy.destroyed()) {
      console.error('cy is destroyed');
      return;
    }

    let elements: ElementDefinition[] = [];
    elements = convertGraphToCytoscape(graph, presetNodePositions);

    // we skip run layout when partially change graph
    const isFirstTime = elementsRef.current.length === 0;

    cy.batch(() => {
      // Need to remove edge scratch first
      // Otherwise, it would conflict with removing aux nodes and throw errors
      if (createEdgeAuxNode) {
        cy.edges().forEach((e) => {
          e.removeScratch('edgeConnections');
        });

        // assume if createEdgeAuxNode is true, we use preset layout, so clean all then recreate is fine here.
        cy.remove('*');

        // Make batch updates to nodes only
        const nodeEles = elements.slice(0, graph.nodes.length);
        cy.add(nodeEles);
      } else {
        if (isFirstTime) {
          cy.add(elements);
        } else {
          // patch use batch internally
          patchElements(cy, elementsRef.current, elements, toJson, get, forEach, shallowObjDiff);
        }
      }
    });

    elementsRef.current = elements;

    // Can't make batch updates to aux nodes,
    // Otherwise the aux node position of curved edges would be incorrect.
    if (createEdgeAuxNode) {
      const edgeEles = elements.slice(graph.nodes.length);
      // create aux nodes for each edge
      cy.edgeConnections().addEdges(edgeEles);
    }
  }, [cy, graph, presetNodePositions, schema, runLayout, createEdgeAuxNode]);

  // any layout options change, we rerun the layouts
  useEffect(() => {
    if (!cy) {
      return;
    }
    runLayout();
  }, [runLayout, cy]);

  // @ts-ignore
  const stylesheets: Stylesheet[] = useMemo(() => {
    return createStyleSheet(schema, theme, userUploadedIconPathPrefix);
  }, [schema, theme, userUploadedIconPathPrefix]);

  // update node style as CytoscapeComponent WILL NOT do that.
  useEffect(() => {
    if (cy) {
      updateGraphStyle(cy, rulesByType, globalParameters);
    }
  }, [cy, rulesByType, globalParameters]);

  const { attrMapGraphLabel, attrMapGraphLabelShow } = settings;
  useEffect(() => {
    if (cy && schema) {
      updateGraphLabel(cy, schema, attrMapGraphLabel, attrMapGraphLabelShow);
    }
  }, [cy, attrMapGraphLabel, attrMapGraphLabelShow, schema, graph.nodes]);

  const graphT = useMemo(() => {
    return createGraphForCytoscape(cy!, runLayout);
  }, [cy, runLayout]);

  const { undoRedoRef, undoRedoState } = useUndoRedo(cy, graph, onGraphChange, settings, onSettingUpdate, runLayout);

  const cyCallback = useCallback(
    (cyInstance: Core & CytoscapeExtensions) => {
      setCy(cyInstance);
      if (cyInstance) {
        if (parentRef) {
          parentRef.current = cyInstance;
        }

        // fix cy.headless issue.
        // @ts-ignore
        cyInstance.headless = () => false;

        // init resize ref
        domRef.current = cyInstance.container();

        // clear all inactive state in nodes/edges when click background
        cyInstance.on('tap', (event) => {
          const evtTarget = event.target;

          if (evtTarget === cyInstance) {
            cyInstance.nodes().removeClass('inactive');
            cyInstance.edges().removeClass('inactive');
          }
        });

        cyInstance.on('tap', 'node, edge', function (event) {
          if (isSchemaGraph) {
            return;
          }
          if (!shouldHandleEvent(event)) {
            return;
          }
          // show detail pane when first click element
          // only automatically show detail panel once.
          if (!isFirstElementClickRef.current) {
            isFirstElementClickRef.current = true;
            setShowDetailPanel(true);
          }
          const target = event.target;
          if (target.data) {
            setDetailData(target.data());
          }
        });
      }
    },
    [parentRef, isSchemaGraph]
  );

  return (
    <div
      className={css({
        width: '100%',
        height: '100%',
        position: 'relative',
        display: 'flex',
      })}
    >
      {/* insights already add toastContainer, but gst and others do not, so we add it here */}
      {!insights ? <StyledToasterContainer /> : null}
      <div
        className={css({
          flexGrow: 0,
          flexShrink: 0,
          width: showDetailPanel ? '241px' : 0,
          height: '100%',
          overflow: 'hidden',
          backgroundColor: 'white',
          boxSizing: 'border-box',
          borderRight: showDetailPanel ? `1px solid ${theme.colors.divider}` : 'none',
          display: 'flex',
          flexDirection: 'column',
        })}
      >
        {detailData ? (
          <ElementDetail
            elementData={detailData}
            isSchemaGraph={isSchemaGraph}
            globalTypes={globalTypes}
            schema={schema}
            showAsPopover={false}
          />
        ) : (
          <div
            className={css({
              ...theme.typography.Body2,
              padding: '8px',
            })}
          >
            Please select a vertex or edge to show the detailed information
          </div>
        )}
      </div>
      <div
        className={css({
          flexGrow: 0,
          flexShrink: 0,
          width: showDetailPanel ? 'calc(100% - 240px)' : '100%',
          height: '100%',
          position: 'relative',
        })}
        tabIndex={0}
        role="img"
        aria-label={`${graphName} graph visualization canvas contains ${graph.nodes.length} nodes and ${graph.links.length} edges`}
      >
        <CytoscapeComponent2
          cy={cyCallback}
          style={graphWidgetStyle}
          stylesheet={stylesheets}
          wheelSensitivity={0.1}
          isSchemaGraph={isSchemaGraph}
          hideEdgesOnViewport={false}
        />
        {cy ? (
          <Provider
            value={{
              id: id,
              schema,
              graphName: graphName,
              isSchemaGraph: isSchemaGraph,
              globalTypes,
              graphData: graph,
              graphT,
            }}
          >
            {!isSchemaGraph && !hideLeftUI && (
              <LeftUI
                cy={cy}
                schema={schema}
                graph={graph}
                onElementSelect={(id) => {
                  cy.centre(cy.getElementById(id).select());
                }}
                showAnalysis={showAnalysis}
                setShowAnalysis={setShowAnalysis}
                settings={settings}
                onSettingUpdate={onSettingUpdate}
                undoRedoInstance={undoRedoRef.current!}
              />
            )}
            <Bottom
              layout={layout}
              onToggleThumbNail={() => {
                setShowThumbNail(!showThumbNail);
              }}
              hideThumbNailButton={isSchemaGraph}
              hideLayoutsButton={isSchemaGraph}
              showThumbNail={showThumbNail}
              onLayoutChange={(newLayout) => {
                // clear all locked state
                if (cy && newLayout === 'Force') {
                  cy.nodes().forEach((e) => {
                    e.removeScratch('__cola_locked');
                  });
                }
                if (newLayout === layout) {
                  // rerun layout
                  runLayout();
                } else {
                  onSettingUpdate('layout', newLayout);
                }
              }}
            />
            {showHoverTooltip && <Hover hoverTooltipDelay={hoverTooltipDelay} />}
            {!isSchemaGraph && !hideContextMenu ? (
              <ContextMenu
                layout={layoutOptions.name}
                actionsByType={actionsByType}
                navigate={navigate}
                links={links}
                getLink={getLink}
                globalParameters={globalParameters}
                isEditMode={isEditMode}
                undoRedoRef={undoRedoRef}
                settings={settings}
                onSettingUpdate={onSettingUpdate}
                onShowAnalysis={() => {
                  setShowAnalysis(true);
                }}
                onVertexDoubleClick={props.onDoubleClick}
              />
            ) : null}
            {showUndoRedo && <UndoRedo {...undoRedoState} />}
            {!isSchemaGraph ? (
              <StatefulPopover
                triggerType={TRIGGER_TYPE.hover}
                content="Display detailed info for selected vertex or edge"
              >
                <Button
                  kind={KIND.tertiary}
                  shape={SHAPE.square}
                  onClick={() => {
                    setShowDetailPanel(!showDetailPanel);
                  }}
                  overrides={{
                    BaseButton: {
                      style: {
                        ...expand({
                          border: '1px solid #C2BFBB',
                        }),
                        position: 'absolute',
                        left: '8px',
                        top: 'calc(50% - 12px)',
                      },
                    },
                  }}
                >
                  {showDetailPanel ? (
                    <TbLayoutSidebarRightExpand size={24} pointerEvents="none" />
                  ) : (
                    <TbLayoutSidebarLeftExpand size={24} pointerEvents="none" />
                  )}
                </Button>
              </StatefulPopover>
            ) : null}
            {props.children}
          </Provider>
        ) : null}
      </div>
    </div>
  );
});

export default Graph;
