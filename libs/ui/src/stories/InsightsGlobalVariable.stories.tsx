import React, { useState, useCallback, Suspense } from 'react';
import { Meta } from '@storybook/react';
import Provider from '@tigergraph/app-ui-lib/Provider';
import { QueryClient, QueryClientProvider } from 'react-query';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';

import { GlobalParam, GlobalParams, GlobalVariable, GlobalVariables } from '../insights/chart';
import { ChartContainer } from '../insights/pages/chart';
import { ChartState, initializeChart } from '../insights/charts/chartSlice';
import { ChartStatus } from '../insights/pages/chart/type';
import { ApplicationState, initializeApplication } from '../insights/pages/application/applicationSlice';
import { DashboardState, initializeDashboard } from '../insights/pages/dashboard/dashboardSlice';
import { setVersion } from '@tigergraph/tools-models/utils/version';
import { StyledToasterContainer } from '../insights/components/styledToasterContainer';

setVersion('4.2.0');

const globalVariables: GlobalVariables = {
  global_string: {
    id: 'global_string',
    name: 'global_string',
    type: 'STRING',
    value: 'test123',
  },
  global_number: {
    id: 'global_number',
    name: 'global_number',
    type: 'NUMBER',
    value: 123,
  },
  global_datetime: {
    id: 'global_datetime',
    name: 'global_datetime',
    type: 'DATETIME',
    value: '2021-09-01 00:00:00',
  },
  global_bool: {
    id: 'global_bool',
    name: 'global_bool',
    type: 'BOOLEAN',
    value: true,
  },
  global_list: {
    id: 'global_list',
    name: 'global_list',
    type: 'LIST',
    elementType: 'STRING',
    value: ['hello', 'world'],
  },
  global_vertex: {
    id: 'global_vertex',
    name: 'global_vertex',
    type: 'VERTEX',
    value: {
      vertexType: 'City',
      vertexID: '123',
    }
  },
  global_vertex_list: {
    id: 'global_vertex_list',
    name: 'global_vertex_list',
    type: 'LIST',
    elementType: 'VERTEX',
    value: [{
      vertexType: 'City',
      vertexID: '123',
    }]
  },
  global_map: {
    id: 'global_map',
    name: 'global_map',
    type: 'MAP',
    keyType: 'string',
    valueType: 'number',
    value: [
      { key: 'hello', value: 1 },
      { key: 'world', value: 23 },
    ],
  },
};

const client = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
});

export default {
  title: 'Insights Global Variable',
  component: ChartContainer,
  decorators: [
    (Story) => {
      return (
        <Provider>
          <QueryClientProvider client={client}>
            <BrowserRouter>
              <Suspense fallback={<div>Loading...</div>}>
                <Story />
              </Suspense>
              <StyledToasterContainer />
            </BrowserRouter>
          </QueryClientProvider>
        </Provider>
      );
    },
  ],
} as Meta;

export const InputsChartEdit = () => {
  const data = constructChartState('Inputs');
  const globalParameters: GlobalParams = {
    var: {
      id: 'input_fuVsXuzjwEPexMEjqTDx5v',
      elementType: 'VERTEX',
      name: 'var',
      type: 'LIST',
      value: [],
    },
  };
  data.chart.chartSettings = {
    inputStates: [
      {
        dataType: 'vertex',
        id: 'input_fuVsXuzjwEPexMEjqTDx5v',
        name: 'var',
        settings: {},
        widgetType: 'List',
        selectedGraph: 'ldbc_snb',
        // "vertexType": "City"
      },
    ],
  };
  return (
    <ChartWrapper
      {...data}
      initGlobalVariables={globalVariables}
      initGlobalParam={globalParameters}
      chartStatus="edit"
    />
  );
};

export const InputsChartPreview = () => {
  const data = constructChartState('Inputs');
  const globalParameters = {
    variable: {
      id: 'input_fuVsXuzjwEPexMEjqTDx5v',
      name: 'variable',
      type: 'VERTEX',
      value: {
        vertexID: 'Chang Sha',
        vertexType: 'City',
      },
    },
  } as GlobalParams;
  data.chart.chartSettings = {
    graphName: 'ldbc_snb',
    inputStates: [
      {
        "id": "input_fdQTBbm9dZd8xpsfy2qmkt",
        "name": "global_vertex_list",
        "dataType": "vertex",
        "globalVariableID": "global_vertex_list",
        "widgetType": "Dropdown",
        "settings": {
          "multi": true,
          "options": [
            {
              "isCreatable": true,
              "label": "",
              "value": {
                "id": "Chang Sha",
                "type": "City"
              }
            },
            {
              "isCreatable": true,
              "label": "",
              "value": {
                "id": "123",
                "type": "City"
              }
            }
          ]
        },
        "selectedGraph": "ldbc_snb",
        "vertexType": "City"
      },
    ],
  };

  return <ChartWrapper {...data} initGlobalParam={globalParameters} initGlobalVariables={globalVariables} chartStatus="normal" />;
};

function ChartWrapper({
  chart,
  application,
  page,
  chartStatus,
  initGlobalParam = {
    string: {
      id: '1',
      name: 'string',
      type: 'STRING',
      value: 'hello world',
    },
    vertex: {
      id: '2',
      name: 'vertex',
      type: 'VERTEX',
      value: {
        vertexID: 'Chang Sha',
        vertexType: 'City',
      },
    },
  },
  initGlobalVariables = {},
}: {
  chart: ChartState;
  application: ApplicationState;
  page: DashboardState;
  chartStatus?: ChartStatus;
  initGlobalParam?: GlobalParams;
  initGlobalVariables?: GlobalVariables;
}) {
  // add container for sandbox
  if (!document.querySelector('.iframe__container')) {
    const div = document.createElement('div');
    div.classList.add('iframe__container');
    document.body.appendChild(div);
  }

  const [globalParameters, setGlobalParameters] = useState<GlobalParams>(initGlobalParam);
  const [globalVariables, setGlobalVariables] = useState<GlobalVariables>(initGlobalVariables);

  const setGlobalParameter = useCallback((name: string, value: GlobalParam, prevName?: string) => {
    setGlobalParameters((globalParamsForEdit) => {
      let params = {
        ...globalParamsForEdit,
        [name]: value,
      };

      if (prevName) {
        delete params[prevName];
      }
      return params;
    });
  }, []);

  const setGlobalVariable = useCallback((name: string, value: GlobalVariable, prevName?: string) => {
    setGlobalVariables((globalParamsForEdit) => {
      let params = {
        ...globalParamsForEdit,
        [name]: value,
      };

      if (prevName) {
        delete params[prevName];
      }
      return params;
    });
  }, []);

  const deleteGlobalParameter = useCallback((name: string) => {
    setGlobalParameters((globalParameter) => {
      const newGlobalParameters = { ...globalParameter };
      delete newGlobalParameters[name];
      return newGlobalParameters;
    });
  }, []);

  return (
    <div
      style={{
        width: '100%',
        height: 'calc(100vh - 2em)',
      }}
    >
      <ChartContainer
        application={application}
        page={page}
        initChart={chart}
        chartStatus={chartStatus ?? 'edit'}
        globalParams={globalParameters}
        globalVariables={globalVariables}
        setGlobalParameter={setGlobalParameter}
        setGlobalVariable={setGlobalVariable}
        deleteGlobalParameter={deleteGlobalParameter}
        onChartSave={() => {}}
        onChartDiscard={() => {}}
        isFullScreen={false}
        setIsFullScreen={() => {}}
        onChartDuplicate={() => {}}
      />
    </div>
  );
}

function constructChartState(type: string) {
  let application = initializeApplication();
  let page = initializeDashboard();

  let chartState = initializeChart(type);
  chartState.graphName = process.env.GRAPH;

  application.pages = [page];
  page.chartMap![chartState.id] = chartState;

  return {
    application,
    page,
    chart: chartState,
  };
}
