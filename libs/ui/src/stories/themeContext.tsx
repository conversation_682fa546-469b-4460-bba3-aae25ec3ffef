import React from 'react';
import Provider from '@tigergraph/app-ui-lib/Provider';
import { createDarkTheme, createTheme, CustomTheme, useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { createContext, useState, ReactNode, useContext, useCallback, useEffect } from 'react';
import { Button } from '@tigergraph/app-ui-lib/button';

export const ThemeContext = createContext({
  theme: createTheme({}),
  themeType: 'light' as 'light' | 'dark',
  toggleTheme: () => {},
});

const themeKey = 'theme';

export function ThemeProvider({ children }: { children: ReactNode }) {
  const [themeType, setThemeType] = useState<'light' | 'dark'>(
    (localStorage.getItem(themeKey) as 'light' | 'dark') || 'light'
  );
  const [theme, setTheme] = useState<CustomTheme>(themeType === 'light' ? createTheme({}) : createDarkTheme({}));
  const toggleTheme = useCallback(() => {
    if (themeType === 'light') {
      setTheme(createDarkTheme({}));
      setThemeType('dark');
      document.documentElement.classList.add('dark');
    } else {
      setTheme(createTheme({}));
      setThemeType('light');
      document.documentElement.classList.remove('dark');
    }
  }, [themeType]);

  useEffect(() => {
    localStorage.setItem(themeKey, themeType);
  }, [themeType]);

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, themeType }}>
      <Provider theme={theme}>
        {/* hardcode height */}
        <div style={{ height: 'calc(100vh - 24px)', display: 'flex', flexDirection: 'column' }}>
          <ToggleTheme />
          <WrapChildren>{children}</WrapChildren>
        </div>
      </Provider>
    </ThemeContext.Provider>
  );
}

function ToggleTheme() {
  const { toggleTheme, themeType } = useContext(ThemeContext);
  return (
    <Button
      onClick={toggleTheme}
      kind="secondary"
      overrides={{
        BaseButton: {
          style: {
            alignSelf: 'flex-start',
          },
        },
      }}
    >
      {themeType}
    </Button>
  );
}

function WrapChildren({ children }: { children: React.ReactNode }) {
  const [css, theme] = useStyletron();
  return (
    <div
      className={css({
        fontSize: '14px',
        color: theme.colors['text.primary'],
        backgroundColor: theme.colors['background.tertiary.a'],
        flexBasis: 0,
        minHeight: 0,
        flexGrow: 1,
        overflow: 'hidden',
      })}
    >
      {children}
    </div>
  );
}

export function useTheme() {
  return useContext(ThemeContext);
}
