import React from 'react';
import { Meta } from '@storybook/react';
import { QueryClient, QueryClientProvider } from 'react-query';

import Graph from '../graph';
import { ThemeProvider } from './themeContext';
import { setVersion } from '@tigergraph/tools-models/utils/version';
import axios from 'axios';
import StyledToasterContainer from '@tigergraph/app-ui-lib/styledToasterContainer';
import { setupAxiosInstance } from '@tigergraph/tools-models';

export { SchemaEditStory as SchemaEdit } from './SchemaEditStory';
export { SchemaViewStory as SchemaView } from './SchemaViewStory';
export { ExploreGraphInsightsStory as ExploreGraphInsights } from './ExploreGraphInsightsStory';
export { ExploreGraphGraphStudioStory as ExploreGraphGraphStudio } from './ExploreGraphGraphStudioStory';
export { ExploreGraphLarge } from './ExploreGraphLarge';
export { ExploreGraphMedium } from './ExploreGraphMedium';

setVersion('3.10.0');
setupAxiosInstance(axios.create(), 'http://localhost:6005', {});

const client = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
});

export default {
  title: 'Graph',
  component: Graph,
  decorators: [
    (Story) => {
      return (
        <ThemeProvider>
          <QueryClientProvider client={client}>
            <Story />
            <StyledToasterContainer />
          </QueryClientProvider>
        </ThemeProvider>
      );
    },
  ],
} as Meta;
