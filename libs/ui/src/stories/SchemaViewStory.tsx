import React, { useMemo, useRef, useState } from 'react';
import { Core } from 'cytoscape';

import { CytoscapeExtensions, Schema, GraphRef } from '../graph/type';
import { Graph } from '../graph';
import { convertSchemaToGraph } from '../graph/data';
import { getSchema } from './util';

export const SchemaViewStory = () => {
  const graphRef = useRef<GraphRef>(null);
  const cyRef = useRef<(Core & CytoscapeExtensions) | null>(null);

  let schema: Schema = useMemo(() => {
    return getSchema();
  }, []);

  const [graph, setGraph] = useState(() => {
    let graph = convertSchemaToGraph(schema);

    return graph;
  });

  return (
    <Graph
      ref={graphRef}
      parentRef={cyRef}
      schema={schema}
      graph={graph}
      isSchemaGraph={true}
      schemaMode="view"
      id="schema-view"
      graphName="NoCodeAntiFraud"
      onGraphChange={setGraph}
      settings={{
        layout: 'Force',
      }}
    />
  );
};
