import React, { useState, useCallback, Suspense, useEffect } from 'react';
import { Meta } from '@storybook/react';
import Provider from '@tigergraph/app-ui-lib/Provider';
import { QueryClient, QueryClientProvider } from 'react-query';
import { <PERSON>rowserRouter } from 'react-router-dom';

import { ChartContainer } from '../insights/pages/chart';
import { ReactSVG } from 'react-svg';
import { dynamicIconImports, iconImportsURL } from '../icons';
import { SelectLucideIconContainer } from '../insights/components/selectLucideIcon';
import { StyledToasterContainer } from '../insights/components/styledToasterContainer';

const client = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
});

export default {
  title: 'Icons',
  component: ChartContainer,
  decorators: [
    (Story) => {
      return (
        <Provider>
          <QueryClientProvider client={client}>
            <BrowserRouter>
              <Suspense fallback={<div>Loading...</div>}>
                <Story />
              </Suspense>
              <StyledToasterContainer />
            </BrowserRouter>
          </QueryClientProvider>
        </Provider>
      );
    },
  ],
} as Meta;

const IconViewSVG = ({ name }: { name: keyof typeof dynamicIconImports }) => {
  const [svg, setSVG] = useState<string>();

  useEffect(() => {
    (async () => {
      const res = await dynamicIconImports[name]();

      setSVG(`data:image/svg+xml,${encodeURIComponent(res.default)}`);
    })();
  }, [name]);

  return svg ? <ReactSVG src={svg} style={{ color: 'blue' }} /> : null;
};

const IconURLView = ({ name }: { name: keyof typeof iconImportsURL }) => {
  const src = iconImportsURL[name];

  return src ? <ReactSVG src={src} style={{ color: 'red' }} /> : null;
};

export const IconView = () => {
  return (
    <>
      <IconViewSVG name="a-arrow-down" />
      <IconURLView name="a-arrow-up" />
    </>
  );
};

export const IconListSelect = () => {
  const [iconURL, setIconURL] = useState<string>();
  return <SelectLucideIconContainer iconURL={iconURL} onIconSelected={setIconURL} />;
};
