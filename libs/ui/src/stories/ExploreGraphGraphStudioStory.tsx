import React, { useMemo, useRef, useState } from 'react';
import { Core } from 'cytoscape';
import { parseGraph } from '@tigergraph/tools-models/gvis/insights';

import { CytoscapeExtensions, Schema, GraphRef, SettingType } from '../graph/type';
import { Graph } from '../graph';

import patternJson from './data/patter1.json';
import { getSchema } from './util';

export const ExploreGraphGraphStudioStory = () => {
  const graphRef = useRef<GraphRef>(null);
  const cyRef = useRef<(Core & CytoscapeExtensions) | null>(null);

  let schema: Schema = useMemo(() => {
    return getSchema();
  }, []);

  const [settings, setSettings] = useState<SettingType>({
    layout: 'Force',
  });

  const [graph, setGraph] = useState(() => {
    let graph = parseGraph(patternJson);
    return graph;
  });

  return (
    <Graph
      ref={graphRef}
      parentRef={cyRef}
      schema={schema}
      graph={graph}
      id="explore-studio"
      graphName="NoCodeAntiFraud"
      onGraphChange={setGraph}
      settings={settings}
      onSettingUpdate={(key, value) => {
        setSettings({
          ...settings,
          [key]: value,
        });
      }}
      // Skip the onExpandNode method
      onDoubleClick={(item) => console.log(item)}
    />
  );
};
