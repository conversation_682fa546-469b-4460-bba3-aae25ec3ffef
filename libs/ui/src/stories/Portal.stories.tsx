import React, { Suspense } from 'react';
import { Meta } from '@storybook/react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { BrowserRouter } from 'react-router-dom';

import { ChartContainer } from '../insights/pages/chart';
import axios from 'axios';
import { ThemeProvider } from './themeContext';
import StyledToasterContainer from '@tigergraph/app-ui-lib/styledToasterContainer';
import { getBaseURL, setupAxiosInstance } from '@tigergraph/tools-models';

import { Explore } from '../explore';

setupAxiosInstance(axios.create(), 'http://localhost:6005', {});
const client = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
});

export default {
  title: 'Portal',
  component: ChartContainer,
  option: { showPanel: false },
  decorators: [
    (Story) => {
      return (
        <ThemeProvider>
          <QueryClientProvider client={client}>
            <BrowserRouter>
              <Suspense fallback={<div>Loading...</div>}>
                <Story />
              </Suspense>
              <StyledToasterContainer />
            </BrowserRouter>
          </QueryClientProvider>
        </ThemeProvider>
      );
    },
  ],
} as Meta;

export const ExploreGraph = () => {
  return (
    <Explore
      baseURL={getBaseURL()}
      graphName="Transaction_Fraud"
      graphs={['Transaction_Fraud']}
      isCloud={true}
      isEditMode={true}
      isClusterMode={true}
      // initialGraphData={{
      //   nodes: [
      //     {
      //       id: '000 Garcia Divide Suite 750',
      //       type: 'Address',
      //       attrs: {
      //         address: '000 Garcia Divide Suite 750',
      //         no_attributes: '',
      //       },
      //       styles: {},
      //       others: {},
      //       labels: {},
      //     },
      //     {
      //       id: 'Cedar GroveNJ',
      //       type: 'City',
      //       attrs: {
      //         id: 'Cedar GroveNJ',
      //         city: 'Cedar Grove',
      //         population: 12411,
      //       },
      //       styles: {},
      //       others: {},
      //       labels: {},
      //     },
      //   ],
      //   links: [
      //     {
      //       source: {
      //         id: '000 Garcia Divide Suite 750',
      //         type: 'Address',
      //       },
      //       target: {
      //         id: 'Cedar GroveNJ',
      //         type: 'City',
      //       },
      //       directed: false,
      //       type: 'Located_In',
      //       discriminator: '',
      //       attrs: {},
      //       styles: {},
      //       others: {},
      //       labels: {},
      //     },
      //   ],
      // }}
    />
  );
};
