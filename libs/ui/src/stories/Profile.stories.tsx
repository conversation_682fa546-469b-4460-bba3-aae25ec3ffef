import React from 'react';
import { Meta } from '@storybook/react';
import Provider from '@tigergraph/app-ui-lib/Provider';

import ProfileResult from '../profile/ProfileResult';
import profileData from './data/profile.json';
import { Profile } from '../profile/profileView/type';

export default {
  title: 'Profile',
  component: ProfileResult,
  decorators: [
    (Story) => {
      return (
        <Provider>
          <Story />
        </Provider>
      );
    },
  ],
} as Meta;

export const ProfileView = () => {
  const data = profileData.profile;
  return (
    <div
      style={{
        width: '100%',
        height: 'calc(100vh - 2em)',
      }}
    >
      <ProfileResult profileData={data as Profile} />
    </div>
  );
};

export const ProfileViewEmpty = () => {
  const data = undefined;
  return (
    <div
      style={{
        width: '100%',
        height: 'calc(100vh - 2em)',
      }}
    >
      <ProfileResult profileData={data as Profile} />
    </div>
  );
};
