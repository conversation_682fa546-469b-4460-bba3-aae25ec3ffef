import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Core } from 'cytoscape';
import {
  ExternalNode,
  ExternalLink,
  getLinkID,
  ExternalGraph,
  getNodeID,
} from '@tigergraph/tools-models/gvis/insights';
import { KIND } from 'baseui/toast';
import { saveAs } from 'file-saver';

import { CytoscapeExtensions, Schema, GraphRef } from '../graph/type';
import { Graph } from '../graph';
import { convertSchemaToGraph } from '../graph/data';

import styleJSON from './data/style.json';
import { getSchema } from './util';

const globalTypes = ['IP'];

export const SchemaEditStory = () => {
  const graphRef = useRef<GraphRef>(null);
  const cyRef = useRef<(Core & CytoscapeExtensions) | null>(null);
  const [messageKindIndex, setMessageKindIndex] = useState(-1);
  const [percentage, setPercentage] = useState(10);

  let schema: Schema = useMemo(() => {
    return getSchema();
  }, []);

  // setup node position
  let presetNodePositions = useMemo(() => {
    let vertexStyles = styleJSON.results.vertexStyles;

    let keys = Object.keys(vertexStyles);
    let presetNodePositions = {};

    for (let key of keys) {
      let vertexStyle = vertexStyles[key];
      // NB you must copy the position b/c cytoscape owns the objects you pass to it
      presetNodePositions[`${key}#${key}`] = {
        x: vertexStyle.x,
        y: vertexStyle.y,
      };
    }

    // set position for csv file node
    presetNodePositions['gs_file#filename.csv'] = {
      x: 10,
      y: -900,
    };

    return presetNodePositions;
  }, []);

  const [graph, setGraph] = useState(() => {
    let graph = convertSchemaToGraph(schema);

    // for loading files, we use types ['gs_file', 'gs_url', 'gs_s3', 'gs_gcs', 'gs_abs', 'gs_snowflake'], need those for show hover detail
    // for aux edge, we use type `gs_data_source_map_${i}`, for styling purpose
    // for aux edge point to aux node, we need set target.id to the corresponding edge's id
    // for aux node, we use type '__aux_node', for styling purpose, we also have we have special handing for aux node

    graph.nodes.push({
      id: 'filename.csv',
      type: 'gs_file',
      attrs: {
        'Last Modified': '2023-03-15',
        Size: '100K',
      },
    });
    graph.links = graph.links.concat([
      {
        type: 'gs_data_source_map_0',
        source: {
          type: 'gs_file',
          id: 'filename.csv',
        },
        target: graph.nodes[0],
        directed: true,
      },
      {
        type: 'gs_data_source_map_1',
        source: {
          type: 'gs_file',
          id: 'filename.csv',
        },
        target: graph.nodes[0],
        directed: true,
      },
      {
        type: 'gs_data_source_map_2',
        source: {
          type: 'gs_file',
          id: 'filename.csv',
        },
        target: {
          type: '__aux_node',
          id: getLinkID(graph.links[0]),
        },
        directed: true,
      },
      {
        type: 'gs_data_source_map_3',
        source: {
          type: 'gs_file',
          id: 'filename.csv',
        },
        target: {
          type: '__aux_node',
          id: getLinkID(graph.links[0]),
        },
        directed: true,
      },
      {
        type: 'gs_data_source_map_4',
        source: {
          type: 'gs_file',
          id: 'filename.csv',
        },
        target: {
          type: '__aux_node',
          id: getLinkID(graph.links[0]),
        },
        directed: true,
      },
      {
        type: 'gs_data_source_map_5',
        source: {
          type: 'gs_file',
          id: 'filename.csv',
        },
        target: graph.nodes[1],
        directed: true,
      },
    ]);

    return graph;
  });

  const onClick = (element: ExternalNode | ExternalLink | undefined) => {
    console.log('Single click', element);
  };

  const onDoubleClick = (element: ExternalNode | ExternalLink | undefined) => {
    console.log('Double click', element);
  };

  const onSelect = (graph: ExternalGraph) => {
    console.log('selected', graph);
  };

  const onPositionChange = () => {
    console.log('positions changed');
  };

  const drawModeOn = () => {
    graphRef.current?.setDrawMode(true);
  };

  const drawModeOff = () => {
    graphRef.current?.setDrawMode(false);
  };

  const onCreateLink = (source: ExternalNode, target: ExternalNode | ExternalLink) => {
    console.log('onCreateLink', source, target);
  };

  const onCreateLinkCancelled = (source: ExternalNode, position: { x: number; y: number }) => {
    console.log('onCreateLinkCancelled', source, position);
  };

  const addNodeLabel = () => {
    graphRef.current?.displayNodeMessage?.({
      node: {
        id: 'IP',
        type: 'IP',
      },
      message: 'abcSelect the target vertex pattern to create an edge pattern',
      isLoading: messageKindIndex % 2 !== 0,
      kind: KIND[Object.keys(KIND)[messageKindIndex]],
    });
    setMessageKindIndex((messageKindIndex + 1) % 4);
  };

  const removeNodeLabel = () => {
    graphRef.current?.removeNodeMessage?.({
      id: 'IP',
      type: 'IP',
    });
  };

  const setNodeStyle = () => {
    graphRef.current?.setNodeStyle?.(
      {
        id: 'IP',
        type: 'IP',
      },
      {
        width: 120,
        height: 120,
        'background-color': 'red',
      }
    );
  };

  const setLinkStyle = () => {
    graphRef.current?.setLinkStyle?.(
      {
        source: {
          type: 'Purchase',
          id: 'Purchase',
        },
        target: {
          type: 'Address',
          id: 'Address',
        },
        type: 'DELIVERED_AT',
        directed: true,
      },
      {
        width: 10,
        'line-color': 'red',
      }
    );
  };

  const setPositions = () => {
    graphRef.current?.setPositions?.(
      graph.nodes.map((n, i) => {
        return [getNodeID(n), 200 * (i % 5), 200 * Math.floor(i / 5)];
      })
    );
  };

  const addNode = () => {
    let node = {
      id: 'test',
      type: 'test',
    };

    setGraph((graph) => {
      return {
        nodes: graph.nodes.concat(node),
        links: graph.links,
      };
    });
  };

  useEffect(() => {
    setTimeout(() => {
      cyRef.current?.fit(undefined, 100);
    }, 0);
  }, []);

  const savePng = () => {
    if (graphRef.current) {
      saveAs(
        graphRef.current.exportPng({
          maxWidth: window.innerWidth,
          maxHeight: window.innerHeight,
        }) as string,
        'screenshot.png'
      );
    }
  };

  return (
    <>
      <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <div
          style={{
            height: 0,
            flexBasis: 0,
            flexGrow: 1,
          }}
        >
          <Graph
            ref={graphRef}
            parentRef={cyRef}
            schema={schema}
            graph={graph}
            isSchemaGraph={true}
            schemaMode="edit"
            createEdgeAuxNode={true}
            presetNodePositions={presetNodePositions}
            id="schema-edit"
            graphName="NoCodeAntiFraud"
            showCreateEdgeTooltip={true}
            onGraphChange={setGraph}
            settings={{
              layout: 'Preset',
            }}
            onClick={onClick}
            onDoubleClick={onDoubleClick}
            onSelect={onSelect}
            onPositionChange={onPositionChange}
            globalTypes={globalTypes}
            onCreateLink={onCreateLink}
            onCreateLinkCancelled={onCreateLinkCancelled}
          />
        </div>
        <div
          style={{
            display: 'flex',
            columnGap: '10px',
          }}
        >
          <button onClick={savePng}>Save PNG</button>
          <button onClick={drawModeOn}>Draw Mode On</button>
          <button onClick={drawModeOff}>Draw Mode Off</button>
          <button onClick={addNodeLabel}>Add Node Label</button>
          <button onClick={removeNodeLabel}>Remove Node Label</button>
          <button onClick={setNodeStyle}>Set Node Style</button>
          <button onClick={setLinkStyle}>Set Link Style</button>
          <button onClick={setPositions}>Set Positions</button>
          <button onClick={addNode}>Add Node</button>
        </div>
      </div>
    </>
  );
};
