{"error": false, "message": "", "results": [], "version": {"api": "v2", "edition": "enterprise", "schema": 1}, "profile": {"overall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:15.054 UTC", "execution": {"executionTimeMs": 1678, "executionTimePercentage": 99.6, "finalization": {"finalizationTimeMs": 50, "finalizationTimePercentage": 2.97}, "initialization": {"initializationTimeMs": 14, "initializationTimePercentage": 0.831}, "processing": {"processingTimeMs": 1614, "processingTimePercentage": 95.8}}, "scheduling": {"schedulingTimeMs": 7, "schedulingTimePercentage": 0.415}, "startTime": "2025-02-11 20:10:13.369 UTC", "totalTimeMs": 1685}, "topology": {"GPE_1_1": {"vertex": {"readCount": 131372}}}}, "queryName": "page_rank", "requestId": "131075.RESTPP_1_1.1739304613369.N", "serverId": "GPE_1_1", "statements": [{"actions": [{"actionKey": "1.1", "actionOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:13.395 UTC", "processingTimeMs": 4, "processingTimePercentage": 0.237, "startTime": "2025-02-11 20:10:13.391 UTC"}, "topology": {"GPE_1_1": {}}}, "actionText": "HeapAccum < vertexScore > ( output_limit, score desc ) @@topScores;", "actionType": "single"}], "statementKey": "1", "statementOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:13.395 UTC", "processingTimeMs": 4, "processingTimePercentage": 0.237, "startTime": "2025-02-11 20:10:13.391 UTC"}, "topology": {"GPE_1_1": {}}}, "statementText": "HeapAccum<vertexScore>(output_limit, score DESC) @@topScores;", "statementType": "single"}, {"actions": [{"actionKey": "2.1", "actionOverall": {"executionCount": 1, "time": {"endTime": "2025-02-11 20:10:13.395 UTC", "processingTimeMs": 0, "processingTimePercentage": 0, "startTime": "2025-02-11 20:10:13.395 UTC"}}, "actionText": "MaxAccum < float > @@maxDiff = 9999;", "actionType": "single"}], "statementKey": "2", "statementOverall": {"executionCount": 1, "time": {"endTime": "2025-02-11 20:10:13.395 UTC", "processingTimeMs": 0, "processingTimePercentage": 0, "startTime": "2025-02-11 20:10:13.395 UTC"}}, "statementText": "MaxAccum<float> @@maxDiff = 9999;", "statementType": "single"}, {"actions": [{"actionKey": "3.1", "actionOverall": {"executionCount": 1, "time": {"endTime": "2025-02-11 20:10:13.395 UTC", "processingTimeMs": 0, "processingTimePercentage": 0, "startTime": "2025-02-11 20:10:13.395 UTC"}}, "actionText": "SumAccum < float > @received_score = 0;", "actionType": "single"}], "statementKey": "3", "statementOverall": {"executionCount": 1, "time": {"endTime": "2025-02-11 20:10:13.395 UTC", "processingTimeMs": 0, "processingTimePercentage": 0, "startTime": "2025-02-11 20:10:13.395 UTC"}}, "statementText": "SumAccum<float> @received_score = 0;", "statementType": "single"}, {"actions": [{"actionKey": "4.1", "actionOverall": {"executionCount": 1, "time": {"endTime": "2025-02-11 20:10:13.395 UTC", "processingTimeMs": 0, "processingTimePercentage": 0, "startTime": "2025-02-11 20:10:13.395 UTC"}}, "actionText": "SumAccum < float > @score = 1;", "actionType": "single"}], "statementKey": "4", "statementOverall": {"executionCount": 1, "time": {"endTime": "2025-02-11 20:10:13.395 UTC", "processingTimeMs": 0, "processingTimePercentage": 0, "startTime": "2025-02-11 20:10:13.395 UTC"}}, "statementText": "SumAccum<float> @score = 1;", "statementType": "single"}, {"actions": [{"actionKey": "5.1", "actionOverall": {"executionCount": 1, "time": {"endTime": "2025-02-11 20:10:13.395 UTC", "processingTimeMs": 0, "processingTimePercentage": 0, "startTime": "2025-02-11 20:10:13.395 UTC"}}, "actionText": "SetAccum < edge > @@edgeSet;", "actionType": "single"}], "statementKey": "5", "statementOverall": {"executionCount": 1, "time": {"endTime": "2025-02-11 20:10:13.395 UTC", "processingTimeMs": 0, "processingTimePercentage": 0, "startTime": "2025-02-11 20:10:13.395 UTC"}}, "statementText": "SetAccum<EDGE> @@edgeSet;", "statementType": "single"}, {"actions": [{"actionKey": "6.1", "actionOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:13.400 UTC", "processingTimeMs": 5, "processingTimePercentage": 0.297, "startTime": "2025-02-11 20:10:13.395 UTC"}, "topology": {"GPE_1_1": {}}}, "actionText": "Start = { Patient .* };", "actionType": "single.create_vset"}], "statementKey": "6", "statementOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:13.400 UTC", "processingTimeMs": 5, "processingTimePercentage": 0.297, "startTime": "2025-02-11 20:10:13.395 UTC"}, "topology": {"GPE_1_1": {}}}, "statementText": "Start = {Patient.*};", "statementType": "single"}, {"statementKey": "7", "statementOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:13.626 UTC", "processingTimeMs": 226, "processingTimePercentage": 13.4, "startTime": "2025-02-11 20:10:13.400 UTC"}, "topology": {"GPE_1_1": {"vertex": {"readCount": 32843}}}}, "statementText": "WHILE @@maxDiff > max_change LIMIT max_iter DO\n                @@maxDiff = 0;\n                V = SELECT s\n                    FROM Start:s -(INFECTED_BY>:e)- :t\n                    ACCUM t.@received_score += s.@score/(s.outdegree(\"INFECTED_BY\")) \n                    POST-ACCUM s.@score = (1.0-damping) + damping * s.@received_score,\n                               s.@received_score = 0,\n                               @@maxDiff += abs(s.@score - s.@score');\n        END;", "statementType": "loop", "statements": [{"actions": [{"actionKey": "7.1.1", "actionOverall": {"executionCount": 1, "time": {"endTime": "2025-02-11 20:10:13.400 UTC", "processingTimeMs": 0, "processingTimePercentage": 0, "startTime": "2025-02-11 20:10:13.400 UTC"}}, "actionText": "@@maxDiff = 0;", "actionType": "single"}], "statementKey": "7.1", "statementOverall": {"executionCount": 1, "time": {"endTime": "2025-02-11 20:10:13.400 UTC", "processingTimeMs": 0, "processingTimePercentage": 0, "startTime": "2025-02-11 20:10:13.400 UTC"}}, "statementText": "@@maxDiff = 0;", "statementType": "single"}, {"actions": [{"actionKey": "7.2.1", "actionOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:13.621 UTC", "processingTimeMs": 221, "processingTimePercentage": 13.1, "startTime": "2025-02-11 20:10:13.400 UTC"}, "topology": {"GPE_1_1": {"vertex": {"readCount": 32843}}}}, "actionText": "V = select s from Start : s - ( INFECTED_BY > : e ) - _ : t accum t.@received_score += s.@score / ( s.outdegree ( \"INFECTED_BY\" ) ) post-accum s.@score = ( 1.0 - damping ) + damping * s.@received_score, s.@received_score = 0, @@maxDiff += abs ( s.@score - s.@score' );", "actionType": "block.edge_action"}, {"actionKey": "7.2.2", "actionOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:13.626 UTC", "processingTimeMs": 5, "processingTimePercentage": 0.297, "startTime": "2025-02-11 20:10:13.621 UTC"}, "topology": {"GPE_1_1": {}}}, "actionText": "V = select s from Start : s - ( INFECTED_BY > : e ) - _ : t accum t.@received_score += s.@score / ( s.outdegree ( \"INFECTED_BY\" ) ) post-accum s.@score = ( 1.0 - damping ) + damping * s.@received_score, s.@received_score = 0, @@maxDiff += abs ( s.@score - s.@score' );", "actionType": "block.reduce"}], "statementKey": "7.2", "statementOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:13.626 UTC", "processingTimeMs": 226, "processingTimePercentage": 13.4, "startTime": "2025-02-11 20:10:13.400 UTC"}, "topology": {"GPE_1_1": {"vertex": {"readCount": 32843}}}}, "statementText": "V = SELECT s\n                    FROM Start:s -(INFECTED_BY>:e)- :t\n                    ACCUM t.@received_score += s.@score/(s.outdegree(\"INFECTED_BY\")) \n                    POST-ACCUM s.@score = (1.0-damping) + damping * s.@received_score,\n                               s.@received_score = 0,\n                               @@maxDiff += abs(s.@score - s.@score');", "statementType": "block"}]}, {"statementKey": "8", "statementOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:14.007 UTC", "processingTimeMs": 381, "processingTimePercentage": 22.6, "startTime": "2025-02-11 20:10:13.626 UTC"}, "topology": {"GPE_1_1": {"vertex": {"readCount": 65686}}}}, "statementText": "IF output_limit > 0 THEN\n                V = SELECT s FROM Start:s\n                    POST-ACCUM @@topScores += vertexScore(s, s.@score);\n                PRINT @@topScores;\n        END;", "statementType": "case_when", "statements": [{"actions": [{"actionKey": "8.1.1", "actionOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:13.852 UTC", "processingTimeMs": 226, "processingTimePercentage": 13.4, "startTime": "2025-02-11 20:10:13.626 UTC"}, "topology": {"GPE_1_1": {"vertex": {"readCount": 32843}}}}, "actionText": "V = select s from Start : s post-accum @@topScores += vertexScore ( s, s.@score );", "actionType": "block.vertex_action"}, {"actionKey": "8.1.2", "actionOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:13.916 UTC", "processingTimeMs": 64, "processingTimePercentage": 3.8, "startTime": "2025-02-11 20:10:13.852 UTC"}, "topology": {"GPE_1_1": {"vertex": {"readCount": 32843}}}}, "actionText": "V = select s from Start : s post-accum @@topScores += vertexScore ( s, s.@score );", "actionType": "block.reduce"}], "statementKey": "8.1", "statementOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:13.916 UTC", "processingTimeMs": 290, "processingTimePercentage": 17.2, "startTime": "2025-02-11 20:10:13.626 UTC"}, "topology": {"GPE_1_1": {"vertex": {"readCount": 65686}}}}, "statementText": "V = SELECT s FROM Start:s\n                    POST-ACCUM @@topScores += vertexScore(s, s.@score);", "statementType": "block"}, {"actions": [{"actionKey": "8.2.1", "actionOverall": {"executionCount": 1, "time": {"endTime": "2025-02-11 20:10:14.007 UTC", "processingTimeMs": 91, "processingTimePercentage": 5.4, "startTime": "2025-02-11 20:10:13.916 UTC"}}, "actionText": "print @@topScores;", "actionType": "single"}], "statementKey": "8.2", "statementOverall": {"executionCount": 1, "time": {"endTime": "2025-02-11 20:10:14.007 UTC", "processingTimeMs": 91, "processingTimePercentage": 5.4, "startTime": "2025-02-11 20:10:13.916 UTC"}}, "statementText": "PRINT @@topScores;", "statementType": "single"}]}, {"statementKey": "9", "statementOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:15.004 UTC", "processingTimeMs": 997, "processingTimePercentage": 59.2, "startTime": "2025-02-11 20:10:14.007 UTC"}, "topology": {"GPE_1_1": {"vertex": {"readCount": 32843}}}}, "statementText": "IF display THEN\n                PRINT Start[Start.@score];\n                Start = SELECT s\n                        FROM Start:s -(INFECTED_BY>:e)- :t\n                        ACCUM @@edgeSet += e;\n                PRINT @@edgeSet;\n        END;", "statementType": "case_when", "statements": [{"actions": [{"actionKey": "9.1.1", "actionOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:14.778 UTC", "processingTimeMs": 771, "processingTimePercentage": 45.8, "startTime": "2025-02-11 20:10:14.007 UTC"}, "topology": {"GPE_1_1": {}}}, "actionText": "print Start [ Start.@score ];", "actionType": "block"}], "statementKey": "9.1", "statementOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:14.778 UTC", "processingTimeMs": 771, "processingTimePercentage": 45.8, "startTime": "2025-02-11 20:10:14.007 UTC"}, "topology": {"GPE_1_1": {}}}, "statementText": "PRINT Start[Start.@score];", "statementType": "single"}, {"actions": [{"actionKey": "9.2.1", "actionOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:14.995 UTC", "processingTimeMs": 217, "processingTimePercentage": 12.9, "startTime": "2025-02-11 20:10:14.778 UTC"}, "topology": {"GPE_1_1": {"vertex": {"readCount": 32843}}}}, "actionText": "Start = select s from Start : s - ( INFECTED_BY > : e ) - _ : t accum @@edgeSet += e;", "actionType": "block.edge_action"}, {"actionKey": "9.2.2", "actionOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:15.000 UTC", "processingTimeMs": 5, "processingTimePercentage": 0.297, "startTime": "2025-02-11 20:10:14.995 UTC"}, "topology": {"GPE_1_1": {}}}, "actionText": "Start = select s from Start : s - ( INFECTED_BY > : e ) - _ : t accum @@edgeSet += e;", "actionType": "block.reduce"}], "statementKey": "9.2", "statementOverall": {"executionCount": 1, "memory": {"GPE_1_1": {"vertexAccumulator": {"totalFinalMB": 0, "totalPeakMB": 0}}}, "time": {"endTime": "2025-02-11 20:10:15.000 UTC", "processingTimeMs": 222, "processingTimePercentage": 13.2, "startTime": "2025-02-11 20:10:14.778 UTC"}, "topology": {"GPE_1_1": {"vertex": {"readCount": 32843}}}}, "statementText": "Start = SELECT s\n                        FROM Start:s -(INFECTED_BY>:e)- :t\n                        ACCUM @@edgeSet += e;", "statementType": "block"}, {"actions": [{"actionKey": "9.3.1", "actionOverall": {"executionCount": 1, "time": {"endTime": "2025-02-11 20:10:15.004 UTC", "processingTimeMs": 4, "processingTimePercentage": 0.237, "startTime": "2025-02-11 20:10:15.000 UTC"}}, "actionText": "print @@edgeSet;", "actionType": "single"}], "statementKey": "9.3", "statementOverall": {"executionCount": 1, "time": {"endTime": "2025-02-11 20:10:15.004 UTC", "processingTimeMs": 4, "processingTimePercentage": 0.237, "startTime": "2025-02-11 20:10:15.000 UTC"}}, "statementText": "PRINT @@edgeSet;", "statementType": "single"}]}]}}