import styleJ<PERSON><PERSON> from './data/style.json';
import schema<PERSON><PERSON><PERSON> from './data/schema.json';

import perfStyle<PERSON><PERSON><PERSON> from './data/perfStyle.json';
import perfSchemaJ<PERSON><PERSON> from './data/perfSchema.json';

import { Schema } from '../graph/type';

import { iconImportsURL } from '../icons/index';

export function getSchema(): Schema {
  let schema = schemaJSON.results;
  let style = styleJSON.results;

  const { vertexStyles, edgeStyles } = style;
  const { VertexTypes: vertexTypes, EdgeTypes: edgeTypes } = schema;

  return {
    GraphName: 'NoCodeAntiFraud',
    VertexTypes: vertexTypes.map((v) => ({
      ...v,
      style: vertexStyles[v.Name],
    })),
    EdgeTypes: edgeTypes.map((e) => ({
      ...e,
      style: edgeStyles[e.Name],
    })),
  };
}

export function getPerfSchema(): Schema {
  let schema = perfSchemaJSON.results;
  let style = perfStyleJSON.results;

  const { vertexStyles, edgeStyles } = style;
  const { VertexTypes: vertexTypes, EdgeTypes: edgeTypes } = schema;

  return {
    GraphName: 'NoCodeAntiFraud',
    VertexTypes: vertexTypes.map((v, idx) => ({
      ...v,
      style: {
        ...vertexStyles[v.Name],
        icon: iconImportsURL[Object.keys(iconImportsURL)[idx]],
      },
    })),
    EdgeTypes: edgeTypes.map((e) => ({
      ...e,
      style: edgeStyles[e.Name],
    })),
  };
}
