export type Profile = {
  queryName: string;
  requestId: string;
  overall: Overall;
  statements?: Statement[];
};

export type Overall = {
  executionCount: number;
  time: {
    startTime: string;
    endTime: string;
    totalTimeMs: number;
    scheduling: {
      schedulingTimeMs: number;
      schedulingTimePercentage: number;
    };
    execution: {
      executionTimeMs: number;
      executionTimePercentage: number;
      initialization?: {
        initializationTimeMs: number;
        initializationTimePercentage: number;
      };
      processing?: {
        processingTimeMs: number;
        processingTimePercentage: number;
      };
      finalization?: {
        finalizationTimeMs: number;
        finalizationTimePercentage: number;
      };
    };
  };
  memory?: Memory;
  topology?: Topology;
};

export type Statement = {
  statementKey: string;
  statementType: string;
  statementText: string;
  statementOverall: {
    executionCount: number;
    time?: {
      processingTimeMs: number;
      processingTimePercentage: number;
      startTime?: string;
      endTime?: string;
    };
    memory?: Memory;
    topology?: Topology;
  };
  actions?: Action[];
  statements?: Statement[];
};

export type Action = {
  actionKey: string;
  actionType: string;
  actionText: string;
  actionOverall: {
    executionCount: number;
    time?: {
      processingTimeMs: number;
      processingTimePercentage: number;
      averageProcessingTimeMs?: number;
      startTime?: string;
      endTime?: string;
    };
    memory?: Memory;
    topology?: Topology;
  };
};

export type Memory = Record<
  string,
  {
    vertexAccumulator: {
      totalPeakMB: number;
      totalFinalMB: number;
    };
  }
>;

export type Topology = Record<
  string,
  {
    vertex?: {
      readCount?: number;
      updateCount?: number;
      deleteCount?: number;
      insertCount?: number;
    };
    edge?: {
      readCount?: number;
      updateCount?: number;
      deleteCount?: number;
      insertCount?: number;
    };
  }
>;
