/// <reference types="vitest/config" />
import { defineConfig } from 'vite';
import path from 'path'

export default defineConfig({
  resolve: {
    alias: {
      '@src': path.resolve(__dirname, 'src')
    }
  },
  test: {
    globals: true,
    environment: 'happy-dom',
    setupFiles: './src/tests/setupTests.ts',
    testTimeout: 5000,
    coverage: {
      provider: 'custom',
      customProviderModule: 'vitest-monocart-coverage',
    },
  },
});
