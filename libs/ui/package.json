{"name": "@tigergraph/tools-ui", "version": "0.0.399-acessibility-2", "description": "TigerGraph UI", "main": "index.js", "types": "index.d.ts", "module": "esm/index.js", "sideEffects": false, "publishConfig": {"@tigergraph:registry": "https://npm.pkg.github.com/tigergraph"}, "scripts": {"build:readme": "shx cp README.md dist/README.md", "build:license": "shx cp LICENSE dist/LICENSE", "build:package-json": "shx cp package.json dist/package.json", "build:package": "rimraf dist/ && NODE_ENV=production BABEL_ENV=production rollup -c --no-treeshake && yarn typescript:generate && yarn build:readme && yarn build:license && yarn build:package-json", "build": "yarn build:package", "lint": "yarn typescript:check && yarn eslint", "eslint": "eslint . --ext .js,.jsx,.tsx,.ts --quiet", "typescript:check": "tsc -p ./tsconfig.json --noEmit", "typescript:generate": "tsc -p ./tsconfig.json --emitDeclarationOnly", "prettier": "prettier . --write", "prettier:check": "prettier . --check", "prerelease": "yarn lint && yarn build", "release": "cd dist/ && npm publish", "storybook": "yarn && start-storybook -p 6006 -s public", "build-storybook": "build-storybook -s public", "test": "jest --coverage", "deploy-storybook": "storybook-to-ghpages -o storybook-static", "size": "source-map-explorer 'dist/*.js' --no-border-checks"}, "author": "TigerGraph", "license": "SEE LICENSE IN LICENSE", "devDependencies": {"@babel/plugin-transform-nullish-coalescing-operator": "^7.25.8", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@babel/preset-react": "^7.16.7", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^13.1.3", "@rollup/plugin-url": "^6.1.0", "@storybook/addon-a11y": "6.4.19", "@storybook/addon-actions": "^6.4.19", "@storybook/addon-essentials": "6.4.19", "@storybook/addon-links": "^6.4.19", "@storybook/react": "6.4.19", "@storybook/storybook-deployer": "^2.8.10", "@svgr/rollup": "^6.5.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "12.1.5", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.4.3", "@tigergraph/app-ui-lib": "^0.2.3", "@tigergraph/tools-models": "1.0.115-002", "@types/cytoscape": "3.19.10", "@types/cytoscape-dagre": "^2.3.0", "@types/cytoscape-edgehandles": "^4.0.0", "@types/cytoscape-popper": "^2.0.0", "@types/d3-scale": "^4.0.2", "@types/d3-scale-chromatic": "^3.0.0", "@types/file-saver": "^2.0.5", "@types/jest": "^29.4.0", "@types/lodash": "^4.14.192", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/styletron-engine-atomic": "^1.1.1", "@types/styletron-react": "^5.0.3", "@types/styletron-standard": "^2.0.2", "babel-jest": "^29.4.3", "babel-loader": "^8.2.3", "babel-plugin-inline-react-svg": "^2.0.1", "baseui": "^11.0.3", "browserify-sign": "^4.2.2", "eslint": "^8.9.0", "eslint-config-react-app": "^7.0.0", "eslint-plugin-baseui": "^11.0.3", "eslint-plugin-storybook": "^0.5.7", "http-proxy-middleware": "^2.0.6", "jest": "^29.4.3", "loader-utils": "^2.0.4", "msw": "^1.2.1", "msw-storybook-addon": "^1.8.0", "prettier": "^2.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-query": "^3.34.17", "react-router-dom": "^6.2.1", "rollup": "^2.79.1", "rollup-plugin-css-only": "^3.1.0", "rollup-plugin-filesize": "^9.1.2", "shx": "^0.3.4", "source-map-explorer": "^2.5.3", "styletron-engine-atomic": "^1.4.8", "styletron-react": "^6.0.2", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "typescript": "^4.8.3"}, "peerDependencies": {"@tigergraph/app-ui-lib": ">=0.1.25", "baseui": ">=11.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0", "react-query": ">=3.0.0", "react-router-dom": ">=6.2.1", "styletron-engine-atomic": ">=1.4.8", "styletron-react": ">=6.0.2"}, "peerDependenciesMeta": {"react-router-dom": {"optional": true}}, "dependencies": {"@babel/runtime": "^7.17.2", "@babel/runtime-corejs3": "^7.17.2", "@babel/traverse": "7.23.2", "@elfalem/leaflet-curve": "^0.9.1", "@floating-ui/react-dom-interactions": "^0.13.3", "@fortawesome/fontawesome-free": "^6.1.1", "@fortawesome/fontawesome-svg-core": "^1.3.0", "@fortawesome/free-regular-svg-icons": "^6.0.0", "@fortawesome/free-solid-svg-icons": "^6.0.0", "@fortawesome/react-fontawesome": "^0.1.17", "@loadable/component": "^5.15.3", "@react-hook/resize-observer": "^1.2.5", "@reactour/mask": "^1.0.0", "@reactour/popover": "^1.0.0", "@reactour/tour": "^3.0.0", "@rehooks/component-size": "^1.0.3", "@shopify/react-web-worker": "^5.0.2", "@tigergraph/cytoscape-edgehandles": "^4.0.1", "@uiw/react-codemirror": "3.2.7", "@uiw/react-md-editor": "^3.23.5", "ahooks": "^3.1.13", "body-scroll-lock": "^4.0.0-beta.0", "clsx": "^1.1.1", "codemirror": "^5.65.2", "cytoscape": "3.21.3", "cytoscape-dagre": "^2.3.2", "cytoscape-node-html-label": "^1.2.2", "cytoscape-popper": "^2.0.0", "cytoscape-undo-redo": "^1.3.3", "d3-color": "^3.1.0", "d3-scale": "^4.0.2", "d3-scale-chromatic": "^3.0.0", "echarts": "^5.3.1", "echarts-for-react": "^3.0.2", "eventemitter3": "^4.0.7", "file-saver": "^2.0.5", "final-form": "^4.20.6", "graphology": "^0.25.1", "graphology-communities-louvain": "^2.0.1", "graphology-operators": "^1.6.0", "graphology-types": "^0.24.7", "graphql": "^16.5.0", "graphql-request": "^4.2.0", "html-to-image": "^1.9.0", "inline-style-expand-shorthand": "^1.3.0", "json5": "^2.2.2", "jssha": "^3.2.0", "leaflet": "^1.8.0", "leaflet.markercluster": "^1.5.3", "lodash": "^4.17.21", "lodash.memoize": "^4.1.2", "lodash.throttle": "^4.1.1", "mixpanel-browser": "^2.45.0", "papaparse": "^5.3.1", "parse-url": "^8.1.0", "react-beforeunload": "^2.5.3", "react-colorful": "^5.5.1", "react-error-boundary": "^3.1.4", "react-final-form": "^6.5.9", "react-grid-layout": "https://github.com/linyutg/react-grid-layout#de82c8ff6278e3b1a68f2784f0fa6ab41773370a", "react-icon-tint": "^2.3.0", "react-icons": "^4.3.1", "react-merge-refs": "^2.0.1", "react-movable": "^3.0.4", "react-use-clipboard": "^1.0.8", "short-uuid": "^4.2.2", "web-worker": "^1.2.0", "websandbox": "^0.5.3", "zod": "^3.17.3"}, "resolutions": {"@types/cytoscape": "3.19.10"}, "msw": {"workerDirectory": "public"}}