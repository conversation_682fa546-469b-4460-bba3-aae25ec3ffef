import { CoverageReport } from 'monocart-coverage-reports';
import { readFile, writeFile } from 'fs/promises';
import { join } from 'path';

const inputDir = ['./apps/insights/coverage/raw', './libs/ui/coverage/raw'];

const outputDir = './.insights_coverage';

const coverageOptions = {
  name: 'Merged Insights Coverage Report',
  inputDir,
  outputDir,

  sourceFilter: (sourcePath) => {
    // stories
    if (/\/stories\//.test(sourcePath)) {
      return false;
    }

    // cytoscape plugins
    if (/cytoscape.*\.js$/.test(sourcePath)) {
      return false;
    }

    // test files
    if (/\.test\.(js|ts)x?$/.test(sourcePath)) {
      return false;
    }

    // typings
    if (/\.d\.ts$/.test(sourcePath)) {
      return false;
    }

    if (/\.(js|ts)x?$/.test(sourcePath)) {
      return true;
    }

    // other static files like svg, png.
    return false;
  },

  sourcePath: (filePath, info) => {
    // add related src dir
    if (info.distFile.includes('apps/insights')) {
      return `apps/insights/${filePath}`;
    }
    if (info.distFile.includes('libs/ui')) {
      return `libs/ui/${filePath}`;
    }

    return filePath;
  },

  reports: ['v8', 'v8-json', 'console-details'],

  onEnd: (results) => {
    console.log(`coverage report generated: ${results.reportPath}`);
  },
};

await new CoverageReport(coverageOptions).generate();

const coverageReportPath = join(outputDir, 'coverage-report.json');
const coverageSummaryPath = join(outputDir, 'coverage-summary.json');

const coverageReport = await readFile(coverageReportPath).then(r => JSON.parse(r.toString()))

const report2Summary = JSON.stringify({
  total: coverageReport.summary
})

await writeFile(coverageSummaryPath, report2Summary);
