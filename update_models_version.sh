#!/bin/bash

# Check if the correct number of arguments are provided
if [ "$#" -ne 1 ]; then
    echo "Usage: \$0 <version>"
    exit 1
fi

VERSION=$1

# List of app directories
APPS=("gst" "graphql" "gshell" "insights" "gap", "home")

# Directory containing the apps
APPS_DIR="./apps"

# Check if the apps directory exists
if [ ! -d "$APPS_DIR" ]; then
    echo "Error: Directory $APPS_DIR does not exist."
    exit 1
fi

# Save the current directory
CURRENT_DIR=$(pwd)

# Loop through each specified app directory
for APP in "${APPS[@]}"; do
    APP_DIR="$APPS_DIR/$APP"
    
    if [ -d "$APP_DIR" ]; then
        PACKAGE_JSON="$APP_DIR/package.json"
        
        # Check if package.json exists in the app directory
        if [ -f "$PACKAGE_JSON" ]; then
            # Use jq to update the version of @tigergraph/tools-models in package.json
            jq --arg version "$VERSION" '.dependencies["@tigergraph/tools-models"] = $version' "$PACKAGE_JSON" > temp_package.json && mv temp_package.json "$PACKAGE_JSON"
            
            # Check if the jq command was successful
            if [ $? -eq 0 ]; then
                echo "Successfully updated @tigergraph/tools-models to version $VERSION in $PACKAGE_JSON."
                
                # Navigate to the app directory and run yarn
                cd "$APP_DIR"
                yarn
                
                # Check if the yarn command was successful
                if [ $? -eq 0 ]; then
                    echo "Successfully ran yarn in $APP_DIR."
                else
                    echo "Failed to run yarn in $APP_DIR."
                    exit 1
                fi
                
                # Return to the previous directory
                cd "$CURRENT_DIR"
            else
                echo "Failed to update @tigergraph/tools-models in $PACKAGE_JSON."
                exit 1
            fi
        else
            echo "Warning: package.json not found in $APP_DIR."
        fi
    else
        echo "Warning: Directory $APP_DIR does not exist."
    fi
done
