import { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { WidthProvider, Responsive, Layout, Layouts, ResponsiveProps } from 'react-grid-layout';
import { useSearchParams, useParams, useLocation } from 'react-router-dom';
import short from 'short-uuid';
import isEqual from 'lodash/isEqual';
import omitBy from 'lodash/omitBy';
import isUndefined from 'lodash/isUndefined';
import { ModalHeader, ModalBody, ModalButton, ModalFooter } from '@tigergraph/app-ui-lib/modal';
import { useStyletron } from 'baseui';

import { useDashboard } from '../application/application';
import { ChartContainer } from '../chart';
import { ChartState } from '@tigergraph/tools-ui/esm/insights/charts';
import { hideToolsHeader, showToolsHeader } from '../../components/toolsHeader';
import StyledModal from '@tigergraph/tools-ui/esm/insights/components/styledModal';
import { useMutationUpsertPageAndWidget, useQueryPageAndWidgets } from '../application/hook';
import { showToast } from '@tigergraph/app-ui-lib/styledToasterContainer';
import { GlobalParam, GlobalParams, globalParamsToURLSearchParams } from '@tigergraph/tools-ui/esm/insights/chart';
import { getErrorMessage } from '@tigergraph/tools-ui/esm/insights/components/error';
import { KIND } from 'baseui/toast';
import { InputState } from '@tigergraph/tools-ui/esm/insights/charts/inputs/type';
import { DashboardState } from '@tigergraph/tools-ui/esm/insights/pages/dashboard/dashboardSlice';
import {
  duplicateGlobalParam,
  isGlobalParamInSearchParams,
} from '@tigergraph/tools-ui/esm/insights/chart/globalParams';
import { ApplicationState } from '@tigergraph/tools-ui/esm/insights/pages/application/applicationSlice';
import EventEmitter from 'eventemitter3';
import { isOverWidgetLimit } from './limit';

export const eventemitter = new EventEmitter();

const ResponsiveGridLayout = WidthProvider(Responsive);

const mdBreakpoint = 800;

type duplicateT = {
  item: ChartState;
  newID: string;
};

export default function Dashboard() {
  const {
    application,
    page,
    onPageChanged,
    setIsSaving,
    isSaving,
    effectGlobalParameters,
    setRunTimeGlobalParameter,
    deleteRunTimeGlobalParameter,
    globalVariables,
    setGlobalVariable,
    isReadOnly,
    resetScreenshotTimer,
    onEnterFullScreen,
    onExistFullScreen,
  } = useDashboard();
  const [css] = useStyletron();
  const [showWarnDialog, setShowWarnDialog] = useState(false);
  const upsertPageAndWidget = useMutationUpsertPageAndWidget();
  const [breakPoint, setBreakPoint] = useState('md');
  const location = useLocation();

  const params = useParams<{ appID: string; pageID: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [enableLayoutChange, setEnableLayoutChange] = useState(true);
  const gridLayoutContainer = useRef<HTMLDivElement>(null);

  useEffect(() => {
    let originalTitle = document.title;
    const setTitle = () => {
      document.title = page.title;
    };
    const restoreTitle = () => {
      document.title = originalTitle;
    };

    const onPrint = () => {
      window.addEventListener('beforeprint', setTitle);
      window.addEventListener('afterprint', restoreTitle);
      window.print();
    };
    eventemitter.on('onPrint', onPrint);
    return () => {
      eventemitter.off('onPrint', onPrint);
      window.removeEventListener('beforeprint', setTitle);
      window.removeEventListener('afterprint', restoreTitle);
    };
  }, [page]);

  // for params change
  // 1 for page params change, if it appear as URL params, will also update search params.
  const setGlobalParameter = useCallback(
    (name: string, value: GlobalParam) => {
      setRunTimeGlobalParameter(params.pageID, name, value);

      // also update search params.
      if (isGlobalParamInSearchParams(searchParams, value)) {
        // calculate the new global params, as we need this to update params in URL
        const updatedGlobalParameters = {
          ...effectGlobalParameters,
          [name]: value,
        };

        let newSearchParams = globalParamsToURLSearchParams(updatedGlobalParameters, searchParams);
        setSearchParams(newSearchParams, {
          replace: true,
        });
      }
    },
    [searchParams, setSearchParams, params.pageID, setRunTimeGlobalParameter, effectGlobalParameters]
  );

  const exitFullScreen = useCallback(() => {
    onExistFullScreen();
    if (!isReadOnly) {
      showToolsHeader();
    }
  }, [onExistFullScreen, isReadOnly]);

  useEffect(() => {
    setFullScreenChartID('');
    exitFullScreen();
  }, [isReadOnly, location.pathname, onExistFullScreen, exitFullScreen]);

  // need to handle state differently in preview mode and edit mode
  // 1. in preview mode, we need to filter out chart with `invisibleInPreviewMode`, which will trigger layout change,
  //    we do not want to trigger page change, so have to put layout into local react state
  // 2. in edit mode, we rely on react-query to manage state
  const { chartMap, layouts: layoutsInEditMode } = page;
  const [layoutInPreviewMode, setLayoutInPreviewMode] = useState(layoutsInEditMode);
  useEffect(() => {
    // if in preview mode and page layout change(which indicate change to different page), reset layout state in preview mode.
    if (isReadOnly) {
      setLayoutInPreviewMode(layoutsInEditMode);
    }
  }, [layoutsInEditMode, isReadOnly]);

  const layouts = useMemo(() => {
    const l = isReadOnly ? layoutInPreviewMode : layoutsInEditMode;
    let prevHight = 0;
    const extraSmallLayouts = [...(l?.md || [])]
      ?.sort((a, b) => {
        if (a.y === b.y) {
          return a.x - b.x;
        } else {
          return a.y - b.y;
        }
      })
      .map((r) => {
        const result = { ...r, x: 0, y: prevHight, w: 4 };

        prevHight += r.h;

        return result;
      });

    return { ...l, xs: extraSmallLayouts } as Layouts;
  }, [isReadOnly, layoutInPreviewMode, layoutsInEditMode]);

  const onPageLayoutChange = useCallback(
    (layouts: Layouts) => {
      if (isReadOnly) {
        setLayoutInPreviewMode(layouts);
      } else {
        onPageChanged({
          ...page,
          layouts,
        });
      }
    },
    [onPageChanged, page, setLayoutInPreviewMode, isReadOnly]
  );

  const deleteGlobalParameter = useCallback(
    (name: string) => {
      deleteRunTimeGlobalParameter(name);
    },
    [deleteRunTimeGlobalParameter]
  );

  const [fullScreenChartID, setFullScreenChartID] = useState('');
  const [currentPageId, setCurrentPageId] = useState('');
  const [duplicateIdList, setDuplicateIdList] = useState<Array<duplicateT>>([]);
  const [duplicateToOtherPage, setDuplicateToOtherPage] = useState<{ pageID: string; widget: ChartState } | undefined>(
    undefined
  );

  useLockBodyScroll(!!fullScreenChartID);

  const clearDuplicateState = useCallback(() => {
    setDuplicateToOtherPage(undefined);
    setIsSaving(false);
  }, [setIsSaving]);

  useEffect(() => {
    if (currentPageId !== params.pageID) {
      setCurrentPageId(params.pageID);
    }
  }, [params.pageID, setCurrentPageId, currentPageId]);

  const previewChartMap = useMemo(() => {
    let result: typeof chartMap = {};
    for (let chart of Object.values(chartMap)) {
      let invisibleInPreviewMode = chart.chartSettings['invisibleInPreviewMode'];
      if (!invisibleInPreviewMode) {
        result[chart.id] = chart;
      }
    }
    return result;
  }, [chartMap]);

  const mdColumns = 12;
  const xsColumns = 4;

  // check for invisibleInPreviewMode
  // if widget set invisibleInPreviewMode to true, set width to fullWidth and isResizable is false
  // so update page layout if needed.
  useEffect(() => {
    if (isReadOnly) {
      return;
    }

    // for now, we only handle md breakpoints
    let md = layouts.md || [];
    let charts = Object.values(chartMap);
    for (let chart of charts) {
      let invisibleInPreviewMode = chart.chartSettings['invisibleInPreviewMode'];
      if (invisibleInPreviewMode === null || invisibleInPreviewMode === undefined) {
        continue;
      }

      for (let i = 0; i < md.length; i++) {
        let layout = md[i];
        if (layout.i === chart.id) {
          if (invisibleInPreviewMode && layout.w === mdColumns && !layout.isResizable) {
            return;
          }

          if (!invisibleInPreviewMode && layout.isResizable) {
            return;
          }

          onPageLayoutChange({
            ...layouts,
            md: [
              ...md.slice(0, i),
              {
                ...layout,
                isResizable: invisibleInPreviewMode ? false : true,
                w: invisibleInPreviewMode ? mdColumns : layout.w,
              },
              ...md.slice(i + 1),
            ],
          });
        }
      }
    }
  }, [isReadOnly, page, layouts, chartMap, onPageLayoutChange]);

  useEffect(() => {
    if (duplicateIdList.length && !isSaving) {
      // if duplicate list is not empty and page is not saving
      let item = duplicateIdList[0].item;
      let newID = duplicateIdList[0].newID;

      const { updatePage, newWidget } = duplicateWidget(item, newID, page, page.globalParameters);

      setIsSaving(true);
      setDuplicateIdList(duplicateIdList.slice(1, duplicateIdList.length));
      // add widget and change page layout
      upsertPageAndWidget.mutate(
        {
          appId: params.appID,
          pageId: page.id,
          widgetConfig: newWidget,
          pageConfig: updatePage,
        },
        {
          onSuccess: (res) => {
            showToast({
              kind: 'positive',
              message: 'Widget duplicated successfully.',
            });

            setIsSaving(false);
            resetScreenshotTimer();
          },
          onError: (err) => {
            showToast({
              kind: KIND.negative,
              message: `Duplicate widget failed. \n${getErrorMessage(err)}`,
            });
            setIsSaving(false);
          },
        }
      );
    }
  }, [isSaving, layouts, duplicateIdList, resetScreenshotTimer, page, params, upsertPageAndWidget, setIsSaving]);

  useEffect(() => {
    const ref = gridLayoutContainer.current;
    const rz = new ResizeObserver((entries: ResizeObserverEntry[]) => {
      entries.forEach((entry) => {
        if (entry.target === ref && entry.contentRect.width <= mdBreakpoint) {
          if (entry.contentRect.width <= mdBreakpoint) {
            setEnableLayoutChange(false);
          } else {
            setEnableLayoutChange(true);
          }
        }
      });
    });
    rz.observe(ref);
    return () => {
      rz.unobserve(ref);
    };
  }, []);

  const layoutProps: ResponsiveProps = {
    className: 'layout',
    rowHeight: 10,
    breakpoints: { md: mdBreakpoint, xs: 0 },
    margin: {
      md: [10, 10],
      xs: [8, 8],
    },
    cols: { md: mdColumns, xs: xsColumns },
    draggableHandle: '.draggable',
    onLayoutChange: (_: Layout[], allLayouts: Layouts) => {
      // remove undefined from layout so it will not trigger application config change
      allLayouts = removeUndefinedFromLayouts(allLayouts);

      if (isEqual(allLayouts, layouts)) {
        return;
      }

      onPageLayoutChange(allLayouts);
    },
    // useCSSTransforms: false,
    isDraggable: !isReadOnly && !fullScreenChartID && enableLayoutChange,
    isResizable: !isReadOnly && !fullScreenChartID && enableLayoutChange,
    layouts: chartMap && Object.keys(chartMap).length ? layouts : {},
  };

  return (
    <div
      id="screenshot"
      ref={gridLayoutContainer}
      className={css({
        padding: '0 0',
        backgroundColor: '#fafcfe',
      })}
    >
      <ResponsiveGridLayout
        {...layoutProps}
        onBreakpointChange={(newBreakpoint) => setBreakPoint(newBreakpoint)}
        measureBeforeMount={true}
      >
        {Object.values(isReadOnly ? previewChartMap : chartMap).map((item) => {
          const isFullScreen = fullScreenChartID === item.id;
          return (
            <div
              className={css({
                borderRadius: '2px',
                backgroundColor: 'white',
                overflow: 'hidden',
                boxShadow: 'rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;',
              })}
              key={item.id}
              data-grid={{
                w: 4,
                h: 12,
                x: 0,
                y: bottom(layouts[breakPoint]),
              }}
              style={
                isFullScreen
                  ? {
                      width: '100vw',
                      height: '100vh',
                      transform: 'none',
                      position: 'fixed',
                      left: '0',
                      top: '0',
                    }
                  : {
                      zIndex: fullScreenChartID ? '-1' : 'auto',
                    }
              }
            >
              <ChartContainer
                application={application}
                page={page}
                initChart={item}
                chartStatus={isReadOnly ? 'preview' : 'normal'}
                globalParams={effectGlobalParameters}
                globalVariables={globalVariables}
                setGlobalVariable={setGlobalVariable}
                setGlobalParameter={setGlobalParameter}
                deleteGlobalParameter={deleteGlobalParameter}
                onChartSave={() => {}}
                onChartDiscard={() => {}}
                isFullScreen={isFullScreen}
                setIsFullScreen={(isFullScreen) => {
                  setFullScreenChartID(isFullScreen ? item.id : '');
                  if (isFullScreen) {
                    hideToolsHeader();
                    onEnterFullScreen();
                  } else {
                    exitFullScreen();
                  }
                }}
                onChartDuplicate={(pageID: string, isCurrentPage?: boolean) => {
                  if (isCurrentPage) {
                    if (isOverWidgetLimit(chartMap)) {
                      setShowWarnDialog(true);
                    } else {
                      let newID = short.generate();

                      setDuplicateIdList([...duplicateIdList, { item: { ...item }, newID: newID }]);
                    }
                  } else {
                    if (isSaving) {
                      showToast({
                        kind: KIND.warning,
                        message: 'Please wait for the previous operation to complete.',
                      });
                      return;
                    }
                    setIsSaving(true);
                    setDuplicateToOtherPage({
                      pageID,
                      widget: item,
                    });
                  }
                }}
              />
            </div>
          );
        })}
      </ResponsiveGridLayout>
      <StyledModal onClose={() => setShowWarnDialog(false)} isOpen={showWarnDialog}>
        <ModalHeader>Widgets Limit</ModalHeader>
        <ModalBody>
          The number of widgets per page cannot exceed <strong>{process.env.REACT_APP_MAXIMUM_WIDGETS}</strong>
        </ModalBody>
        <ModalFooter>
          <ModalButton
            onClick={() => {
              setShowWarnDialog(false);
            }}
          >
            OK
          </ModalButton>
        </ModalFooter>
      </StyledModal>
      {duplicateToOtherPage ? (
        <DuplicateWidgetToOtherPage
          {...duplicateToOtherPage}
          application={application}
          origGlobalParams={page.globalParameters}
          onFinished={clearDuplicateState}
        />
      ) : null}
    </div>
  );
}

function useLockBodyScroll(lock: boolean): void {
  useLayoutEffect(() => {
    // Prevent scrolling on mount
    document.body.style.overflow = lock ? 'hidden' : 'auto';
  }, [lock]);
}

/**
 * Return the bottom coordinate of the layout.
 *
 * @param  {Array} layout Layout array.
 * @return {Number}       Bottom coordinate.
 */
function bottom(layout: Layout[]): number {
  if (!layout) {
    return 0;
  }
  let max = 0,
    bottomY;
  for (let i = 0, len = layout.length; i < len; i++) {
    bottomY = layout[i].y + layout[i].h;
    if (bottomY > max) max = bottomY;
  }
  return max;
}

function duplicateLayout(layouts: Layouts, chart: ChartState, newID: string): Layouts {
  let newLayouts = {
    ...layouts,
  };
  let breakPoints = Object.keys(newLayouts);

  for (let breakPoint of breakPoints) {
    let bpLayout = newLayouts[breakPoint];
    for (let layout of bpLayout) {
      if (layout.i === chart.id) {
        let newLayout = {
          ...layout,
          i: newID,
          y: bottom(bpLayout),
        };
        newLayouts[breakPoint] = bpLayout.concat(newLayout);
        break;
      }
    }
  }

  return newLayouts;
}

function removeUndefinedFromLayouts(layouts: Layouts) {
  let newLayouts: Layouts = {};
  let breakPoints = Object.keys(layouts);

  for (let breakPoint of breakPoints) {
    let bpLayout = layouts[breakPoint];
    let newBpLayout: Layout[] = [];
    for (let layout of bpLayout) {
      // @ts-ignore
      newBpLayout.push(omitBy(layout, isUndefined));
    }
    newLayouts[breakPoint] = newBpLayout;
  }

  return newLayouts;
}

function getNewVarName(varName: string, globalParams: GlobalParams) {
  if (!globalParams[varName]) {
    return varName;
  }
  for (let i = 2; ; i++) {
    const newName = `${varName}_${i}`;
    if (!globalParams[newName]) {
      return newName;
    }
  }
}

function duplicateWidget(
  widget: ChartState,
  newID: string,
  page: DashboardState,
  origGlobalParams: GlobalParams
): {
  updatePage: DashboardState;
  newWidget: ChartState;
} {
  const { layouts } = page;
  let globalParameters = page.globalParameters;

  let newWidget = { ...widget, title: `[duplicate] ${widget.title}`, id: newID, version: '' };
  let updateLayouts = duplicateLayout(layouts, widget, newID);

  // rename inputs variable name and the corresponding global params
  if (widget.type === 'Inputs') {
    const settings = widget.chartSettings;

    const inputStates = (settings['inputStates'] as InputState[]) || [];

    const newInputStates = inputStates.map((inputState) => {
      const varName = inputState.name;
      const newVarName = getNewVarName(varName, globalParameters);
      globalParameters = duplicateGlobalParam(varName, origGlobalParams, newVarName, globalParameters);

      const newInputState = {
        ...inputState,
        name: newVarName,
      };
      return newInputState;
    });

    newWidget.chartSettings = {
      ...settings,
      inputStates: newInputStates,
    };
  }

  return {
    newWidget,
    updatePage: {
      ...page,
      globalParameters,
      layouts: updateLayouts,
    },
  };
}

function DuplicateWidgetToOtherPage({
  application,
  pageID,
  widget,
  origGlobalParams,
  onFinished,
}: {
  application: ApplicationState;
  pageID: string;
  widget: ChartState;
  origGlobalParams: GlobalParams;
  onFinished: () => void;
}) {
  const { mutate, isLoading } = useMutationUpsertPageAndWidget();

  const { data: page, isSuccess, isError, error } = useQueryPageAndWidgets({ appId: application.id, pageId: pageID });

  useEffect(() => {
    if (isError) {
      showToast({
        kind: KIND.negative,
        message: `Duplicate widget failed. \n${getErrorMessage(error)}`,
      });
      onFinished();
    }
  }, [isError, error, onFinished]);

  useEffect(() => {
    if (!isSuccess || isLoading) {
      return;
    }

    if (isOverWidgetLimit(page.chartMap)) {
      showToast({
        kind: KIND.warning,
        message: (
          <>
            The number of widgets per page cannot exceed <strong>{process.env.REACT_APP_MAXIMUM_WIDGETS}</strong>
          </>
        ),
      });
      onFinished();
      return;
    }

    const newID = short.generate();
    const { newWidget, updatePage } = duplicateWidget(widget, newID, page, origGlobalParams);

    mutate(
      {
        appId: application.id,
        pageId: updatePage.id,
        widgetConfig: newWidget,
        pageConfig: updatePage,
      },
      {
        onSuccess: () => {
          showToast({
            kind: KIND.positive,
            message: 'Widget duplicated successfully.',
          });
          onFinished();
        },
        onError: (err) => {
          showToast({
            kind: KIND.negative,
            message: `Duplicate widget failed. \n${getErrorMessage(err)}`,
          });
          onFinished();
        },
      }
    );
  }, [page, isSuccess, widget, application.id, mutate, isLoading, onFinished, origGlobalParams]);

  return null;
}
