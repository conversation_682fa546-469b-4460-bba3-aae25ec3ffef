<h1 class="cdk-visually-hidden">Map Data To Graph</h1>
<app-loading-indicator [show]="loading | async">
  <app-adjust-horizontal
    #adjustHorizontal
    [adjustHorizontalModel] = "adjustHorizontalModel"
    [internalSplit]="true"
    (interaction)="onAdjustInteraction($event)">
    <ng-container class="aj-left-child">
      <app-toolbar-container
        [config]="toolbarConfig"
        (interaction)="onInteraction($event)">
        <div class="graphchart-container">
          <app-schema-graph-chart
            #schemaGraphContainer
            [graph]="schema"
            [loadingJobGraph]="loadingJobGraph"
            [graphEvents]="canWriteLoadingJob ? graphEvents : {}"
            [globalTypes]="globalTypes"
            [isSchemaDesign]="true"
            [createEdgeAuxNode]="true"
            [autolock]="!canWriteLoadingJob"
            [forceUpdateLoadingStatus]="forceUpdateLoadingStatus">
          </app-schema-graph-chart>
          <app-shortcuts-menu
            class="shortcuts-menu-container"
            [shortcuts]="shortcuts">
          </app-shortcuts-menu>

          <!-- Hint -->
          <div *ngIf="showLeftHint | async"
            fxLayout="column"
            fxLayoutAlign="center center"
            fxLayoutGap="0px"
            class="hint-container hint-text">
            <p>{{ 'There is no graph schema in the system.' | translate }}</p>
            <p>{{ 'Use' | translate }} <b>{{ 'Schema Designer' | translate }}</b> {{ 'to create graph schema.' | translate }}</p>
          </div>
          <!-- End of Hint -->

          <div class="main-panel-warning-container mat-input-warn">
            <div *ngIf="authorizationWarning | async">
              <mat-icon attr.aria-label="{{ 'Warning' | translate }}">warning</mat-icon>
              <span>{{ authorizationWarning | async }}</span>
            </div>
            <div *ngIf="unsavedWarning | async">
              <mat-icon attr.aria-label="{{ 'Warning' | translate }}">warning</mat-icon>
              <span>{{ unsavedWarning | async }}</span>
            </div>
          </div>
        </div>
      </app-toolbar-container>
    </ng-container>

    <ng-container class="aj-right-child">
      <app-toolbar-container
        [ngClass]="{'right-toolbar-container': showLeft && showRight}"
        [hidden]="!isLoadingMapping"
        [config]="mappingToolbarConfig"
        (interaction)="onInteraction($event)">
        <div class="graphchart-container right-container">
          <div id="x6-grpah-container" class="x6-grpah-container"></div>

          <!-- Hint -->
          <div *ngIf="showRightHint | async" fxLayoutAlign="center center" class="hint-container hint-text">
            <div class="hint-table" fxLayout="column" tabindex="0">
              <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
                <p class="hint-table-left">{{ 'Add data file' | translate }}</p>
                <p class="hint-table-right">
                  <span>{{ 'Click' | translate}}</span>
                  <mat-icon
                    class="icon-in-instruction"
                    attr.aria-label="{{ 'Add data file' | translate }}">
                    note_add
                  </mat-icon>
                  <span>{{ 'in toolbar' | translate }}</span>
                </p>
              </div>
              <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
                <p class="hint-table-left">{{ 'Add data mapping' | translate }}</p>
                <p class="hint-table-right">
                  <span>{{ 'Click' | translate}}</span>
                  <mat-icon
                    class="icon-in-instruction"
                    attr.aria-label="{{ 'Add data mapping' | translate }}">
                    shuffle
                  </mat-icon>
                  <span>{{ 'then click data source then click vertex or edge' | translate }}</span>
                </p>
              </div>
              <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
                <p class="hint-table-left">{{ 'Edit data mapping' | translate }}</p>
                <p class="hint-table-right">
                  {{ 'Choose dashed link between data source and graph schema' | translate }}
                </p>
              </div>
              <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
                <p class="hint-table-left">{{ 'Map columns to attributes' | translate }}</p>
                <p class="hint-table-right">
                  {{ 'Drag from data column, then drop on the vertex or edge attribute' | translate }}
                </p>
              </div>
              <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
                <p class="hint-table-left">{{ 'Delete data file or data mapping' | translate }}</p>
                <p class="hint-table-right">
                  <span>{{ 'Choose data file or data mapping to delete then click' | translate }}</span>
                  <mat-icon
                    class="icon-in-instruction"
                    attr.aria-label="{{ 'Delete data file or data mapping' | translate }}">
                    delete
                  </mat-icon>
                  <span>{{'in toolbar' | translate }}</span>
                </p>
              </div>
              <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
                <p class="hint-table-left">{{ 'Publish changes' | translate }}</p>
                <p class="hint-table-right">
                  <span>{{ 'Click' | translate }}</span>
                  <mat-icon
                    class="icon-in-instruction"
                    attr.aria-label="{{ 'Publish changes' | translate }}">
                    save
                  </mat-icon>
                  <span>{{ 'in toolbar' | translate }}</span>
                </p>
              </div>
            </div>
          </div>
          <!-- End of Hint -->
        </div>
      </app-toolbar-container>

      <div
        fxLayout="row"
        class="info-container"
        [hidden]="isLoadingMapping"
        fxShow.gt-sm="true">
        <mat-tab-group
          [(selectedIndex)]='currentStatisticsTab'
          [@.disabled]="true"
          (selectedTabChange)="resizeStatisticsCharts()">
          <mat-tab aria-label="Graph statistics">
            <div fxLayout="column" fxLayoutGap="1px" class="statistics-container">
              <div fxFlex="50%" class="info" tabindex="0">
                <mat-table [dataSource]="graphStatisticsDataSource" class="statistics-table mat-elevation-z8">
                  <caption class="hint-text text-left">{{ 'Graph statistics' | translate }}</caption>
                  <ng-container matColumnDef="type">
                    <mat-header-cell *matHeaderCellDef> {{ 'Type' | translate }} </mat-header-cell>
                    <mat-cell *matCellDef="let row"> {{ row.type }} </mat-cell>
                  </ng-container>

                  <ng-container matColumnDef="number">
                    <mat-header-cell *matHeaderCellDef> {{ 'Number' | translate }} </mat-header-cell>
                    <mat-cell *matCellDef="let row"> {{ row.number | number }} </mat-cell>
                  </ng-container>

                  <mat-header-row *matHeaderRowDef="['type', 'number']"></mat-header-row>
                  <mat-row *matRowDef="let row; columns: ['type', 'number'];"></mat-row>
                </mat-table>
              </div>

              <mat-divider></mat-divider>

              <div fxFlex="50%" fxLayout="column" class="info" tabindex="0">
                <span class="hint-text">{{'Graph statistical trend' | translate }}</span>
                <canvas
                  fxFlex
                  role="img"
                  tabindex="0"
                  [attr.aria-label]="graphStatisticalTrendAriaLabel"
                  #graphStatisticalTrend
                  class="graph-statistical-trend-chart mat-elevation-z8 sho1">
                  <p>The browser does not support the html canvas</p>
                </canvas>
              </div>
            </div>
          </mat-tab>
          <mat-tab aria-label="Loading statistics">
            <div fxLayout="column" fxLayoutGap="1px" class="statistics-container">
              <div fxFlex="40%" class="info" tabindex="0">
                <div [ngSwitch]="currentLoadingProgress.status" class="mat-input-warn mat-caption">
                  <span *ngSwitchCase="'N/A'">
                    {{ 'Select a data file from the left to show loading statistics.' | translate }}
                  </span>
                  <span *ngSwitchCase="'INITIAL'">
                    {{ 'Click' | translate }}
                    <mat-icon class="icon-in-hint">play_arrow</mat-icon>
                    {{ 'in the toolbar to start loading.' | translate }}
                  </span>
                  <span *ngSwitchCase="'DATA NOT MAPPED'">
                      {{ 'Please create data mapping for this data file at Map Data To Graph page first.' | translate }}
                  </span>
                </div>
                <mat-table [dataSource]="loadingProgressDataSource" class="statistics-table mat-elevation-z8">
                  <caption  class="hint-text text-left">{{ 'Data loading statistics' | translate }}</caption>
                  <ng-container matColumnDef="type">
                    <mat-header-cell *matHeaderCellDef> {{ 'Data file' | translate }} </mat-header-cell>
                    <mat-cell *matCellDef="let row"> {{ row.type }} </mat-cell>
                  </ng-container>

                  <ng-container matColumnDef="number">
                    <mat-header-cell *matHeaderCellDef> {{ currentLoadingProgress.source }} </mat-header-cell>
                    <mat-cell *matCellDef="let row">
                      <ng-template [ngIf]="row.type === 'Status'">
                        <div class="status-badge" [ngStyle]="{
                          'background-color': getBackgroundOfStatus(row.value),
                          'color': getColorOfStatus(row.value)
                        }">{{ (row.value === 'INITIAL' ? 'NOT STARTED' : row.value) | translate }}</div>
                      </ng-template>
                      <ng-template [ngIf]="row.type !== 'Status'">
                        {{row.value}}
                      </ng-template>
                    </mat-cell>
                  </ng-container>

                  <mat-header-row *matHeaderRowDef="['type', 'number']"></mat-header-row>
                  <mat-row *matRowDef="let row; columns: ['type', 'number'];"></mat-row>
                </mat-table>
              </div>

              <mat-divider *ngIf="currentLoadingProgress.loadedLines !== undefined &&
                !currentLoadingProgress.message && !isCurrentLoadingJobKafka()"></mat-divider>
              <div fxLayout="column" class="info" tabindex="0"
                [fxFlex]="setLoadingStatisticStyle('flex')"
                [ngStyle]="{'padding': setLoadingStatisticStyle('padding') }"
                [fxHide]="isCurrentLoadingJobKafka()">
                <span *ngIf="!(currentLoadingProgress.loadedLines === undefined ||
                  currentLoadingProgress.message)" class="hint-text">
                  {{'Loading speed' | translate }}
                </span>
                <canvas
                  fxFlex
                  #loadingSpeedTrend
                  [hidden]="currentLoadingProgress.loadedLines === undefined ||
                    currentLoadingProgress.message !== undefined"
                  class="graph-statistical-trend-chart mat-elevation-z8">
                  <p>The browser does not support the html canvas</p>
                </canvas>
              </div>

              <mat-divider *ngIf="currentLoadingProgress.loadedLines !== undefined &&
                !currentLoadingProgress.message"></mat-divider>

              <div fxLayout="column" class="info" tabindex="0"
                [hidden]="currentLoadingProgress.loadedLines === undefined"
                [fxFlex]="setLoadingStatisticStyle('flex')"
                [ngStyle]="{'padding': setLoadingStatisticStyle('padding')}">
                <span
                  [hidden]="currentLoadingProgress.loadedLines === undefined ||
                    currentLoadingProgress.message"
                  class="hint-text">
                  {{ 'Data lines distribution' | translate }}
                </span>
                <canvas
                  fxFlex
                  #dataLinesDistribution
                  [hidden]="currentLoadingProgress.loadedLines === undefined ||
                  currentLoadingProgress.message !== undefined"
                  class="graph-statistical-trend-chart data-lines-distribution-chart mat-elevation-z8">
                  <p>The browser does not support the html canvas</p>
                </canvas>
              </div>

              <mat-divider *ngIf="currentLoadingProgress.message"></mat-divider>

              <div fxFlex="60%" fxLayout="column" class="info" tabindex="0" *ngIf="currentLoadingProgress.message">
                <span class="hint-text">{{ 'Error message' | translate }}</span>
                <div class="loading-error-container" fxFlex>{{ currentLoadingProgress.message }}</div>
              </div>

            </div>
          </mat-tab>
        </mat-tab-group>
      </div>
    </ng-container>
  </app-adjust-horizontal>
</app-loading-indicator>

<ng-template #popupWindow>
  <div class="loading-builder-popup">
    <!-- Popup Window Toolbar -->
    <mat-toolbar color="primary" class="popup-toolbar">
      <div [ngSwitch]="selectedEvent" class="max-width dialog-title-line-height">
        <div *ngSwitchCase="toolbarKeys.AddDataFile">
          <h2 *ngIf="isPickDataSourceTypeDialogOpen" mat-dialog-title>
            {{ 'Choose a data source type' | translate }}
          </h2>
          <div [ngSwitch]="selectedDataSourceType">
            <div *ngSwitchCase="dataSourceType.File">
              <h2 *ngIf="isDataPreviewDialogOpen"
                [fxHide.lt-lg]="selectedFile"
                mat-dialog-title>
                {{ 'Files on server' | translate }}
              </h2>
            </div>
            <div *ngSwitchCase="dataSourceType.S3">
              <h2 *ngIf="isDataSourceListDialogOpen" mat-dialog-title>
                {{ 'Amazon S3 data sources' | translate }}
              </h2>
              <h2 *ngIf="isInputDataSourceInfoDialogOpen" mat-dialog-title>
                {{ 'Add data source from Amazon S3' | translate }}
              </h2>
              <h2 *ngIf="isDataPreviewDialogOpen" mat-dialog-title>
                {{ 'Amazon S3 ' | translate }} - {{ selectedDataSourceName }}
              </h2>
            </div>
            <div *ngSwitchCase="dataSourceType.GoogleCloudStorage">
              <h2 *ngIf="isDataSourceListDialogOpen" mat-dialog-title>
                {{ 'Google Cloud Storage data sources' | translate }}
              </h2>
              <h2 *ngIf="isInputDataSourceInfoDialogOpen" mat-dialog-title>
                {{ 'Add data source from Google Cloud Storage' | translate }}
              </h2>
              <h2 *ngIf="isDataPreviewDialogOpen" mat-dialog-title>
                {{ 'Google Cloud Storage ' | translate }} - {{ selectedDataSourceName }}
              </h2>
            </div>
            <div *ngSwitchCase="dataSourceType.AzureBlobStorage">
              <h2 *ngIf="isDataSourceListDialogOpen" mat-dialog-title>
                {{ 'Azure Blob Storage data sources' | translate }}
              </h2>
              <h2 *ngIf="isInputDataSourceInfoDialogOpen" mat-dialog-title>
                {{ 'Add data source from Azure Blob Storage' | translate }}
              </h2>
              <h2 *ngIf="isDataPreviewDialogOpen" mat-dialog-title>
                {{ 'Azure Blob Storage ' | translate }} - {{ selectedDataSourceName }}
              </h2>
            </div>
          </div>

          <h2 *ngIf="selectedFile && !isRemoteDataSourceType && isDataPreviewDialogOpen"
            mat-dialog-title
            class="file-source-header-title"
            fxHide
            [fxShow.lt-lg]="selectedFile">
            {{ getFileName(selectedFile) | translate }}
          </h2>

          <h2 *ngIf="isEditSchemaDialogOpen" mat-dialog-title>
            {{ 'Edit JSON Schema for ' | translate }} {{ getFileName(selectedFile) }}
          </h2>
        </div>

        <h2 *ngSwitchCase="toolbarKeys.AddTokenFunction" mat-dialog-title>
          {{ 'Add token function' | translate }}
        </h2>
        <h2 *ngSwitchCase="'addConstMapping'" mat-dialog-title>
          {{ 'Load constant' | translate }}
        </h2>
        <h2 *ngSwitchCase="toolbarKeys.WhereClause" mat-dialog-title>
          {{'Add data filter' | translate }}
        </h2>
        <h2 *ngSwitchCase="toolbarKeys.MapWidget" mat-dialog-title>
          {{ 'Add Map widget' | translate}}
        </h2>
        <h2 *ngSwitchCase="toolbarKeys.UDTWidget" mat-dialog-title>
          {{ 'Add UDT widget' | translate }}
        </h2>
        <h2 *ngSwitchCase="toolbarKeys.ShowLeftVisInfo" mat-dialog-title>
          {{ 'Left chart visual information' | translate }}
        </h2>
        <h2 *ngSwitchCase="toolbarKeys.ShowRightVisInfo" mat-dialog-title>
          {{ 'Right chart visual information' | translate }}
        </h2>
        <h2 *ngSwitchCase="toolbarKeys.ShowHint" mat-dialog-title>
          {{ 'Help' | translate }}
        </h2>
        <p *ngSwitchCase="toolbarKeys.ShowLeftVisInfo" hidden="true" id="dialogDescription">
          {{ 'This dialog contains a left chart visual information transcript' | translate }}
        </p>
        <p *ngSwitchCase="toolbarKeys.ShowRightVisInfo" hidden="true" id="dialogDescription">
          {{ 'This dialog contains a right chart visual information transcript' | translate }}
        </p>
      </div>
    </mat-toolbar>
    <!-- End of Popup Window Toolbar -->

    <!-- Add Data File -->
    <div *ngIf="selectedEvent === toolbarKeys.AddDataFile" class="data-source-popup-content">
      <app-loading-indicator [show]="loading | async">
        <mat-dialog-content>

          <!-- Choose Data Source Type -->
          <div *ngIf="isPickDataSourceTypeDialogOpen"
            fxLayoutAlign="space-evenly"
            fxLayout.lt-sm="column" fxLayout="row"
            fxLayoutAlign="center center"
            fxLayoutGap="12px">
            <button
              *ngFor="let type of dataSourceTypeButtons"
              mat-stroked-button
              class="data-source-type-button"
              [ngClass.lt-sm]="'data-source-type-button-column'"
              attr.aria-label="{{ type.title }}"
              (click)="type.disabled ? showMsg(type.title, $event) : selectDataSourceType(type.key)">
              <div fxLayout="column" fxLayoutAlign="center center">
                <mat-icon
                  attr.aria-label="{{ type.key }}"
                  [ngClass]="{'grey-icon': (type.key === 'file')}"
                  [svgIcon]="type.key">
                </mat-icon>
                <span>{{ type.title | translate }}</span>
              </div>
            </button>

            <!-- Will enable load more when we support more data source types. -->
            <!-- <div class="load-more">
              <button
                mat-button
                color="primary"
                [class.hidden]="numberOfSourceTypeShown>= dataSourceTypeButtons.length"
                (click)="$event.stopPropagation(); loadMoreDataSourceType();">
                SHOW MORE
              </button>
            </div> -->

          </div>
          <!-- End of Choose Data Source Type -->

          <div *ngIf="isInputDataSourceInfoDialogOpen" [ngSwitch]="selectedDataSourceType" class="max-width">
            <!-- Start of Upload Local File -->
            <!-- <div *ngSwitchCase="dataSourceType.File" class="local-credential-container"> -->
              <!-- Start of Drag & drop files -->
              <!-- <div class="local-credential-container max-width">
                <div
                  #dropArea
                  [ngClass]="'local-file-drop-area'"
                  (dragover)="onDragOver($event)"
                  (dragleave)="onDragLeave($event)"
                  (drop)="onDrop()">
                  <input
                    type="file"
                    title=""
                    multiple
                    #fileInput
                    id="fileInput"
                    (change)="uploadFile($event.target.files)"
                    attr.aria-label="{{ 'Upload Local File' | translate }}"/>
                  <button
                    mat-stroked-button
                    color="accent"
                    (click)="fileInput.click()">
                    {{ 'BROWSE' | translate }}
                  </button>
                  <p>
                    {{ 'or drag and drop a file here.' | translate }}
                  </p>
                </div>
              </div> -->
              <!-- End of Upload Local File -->

            <!-- Start of Input S3 credential information -->
            <div *ngSwitchCase="dataSourceType.S3" class="credential-container">
              <form
                [formGroup]="credentialForm"
                id="s3-datasource-input">
                <p>{{ 'AWS access key id' | translate }} &nbsp;
                  <a
                    href="https://docs.aws.amazon.com/powershell/latest/userguide/pstools-appendix-sign-up.html#get-access-keys"
                    target="_blank"
                    class="link">
                    {{ 'Learn more' | translate }}
                  </a>
                </p>
                <mat-form-field>
                  <input matInput type='password' formControlName="s3AccessKey" #accessKeyInput required>
                  <button
                    matSuffix
                    mat-button
                    mat-icon-button
                    matTooltip = "{{ 'Click to show or hide your AWS access key id' | translate }}"
                    (click)="showOrHidePW(accessKeyInput)">
                    <mat-icon>
                      {{ accessKeyInput.getAttribute('type') === 'password' ? 'visibility_off' : 'visibility' }}
                    </mat-icon>
                  </button>
                  <mat-error *ngIf="credentialForm.get('s3AccessKey').invalid">
                    {{ 'S3 access key is required.' | translate }}
                  </mat-error>
                </mat-form-field>
                <p>{{ 'AWS secret access key' | translate }}</p>
                <mat-form-field>
                  <input matInput type='password' formControlName="s3SecretKey" #secretKeyInput required>
                  <button
                    matSuffix
                    mat-button
                    mat-icon-button
                    matTooltip = "{{ 'Click to show or hide your AWS secret access key' | translate }}"
                    (click)="showOrHidePW(secretKeyInput)">
                    <mat-icon>
                      {{ secretKeyInput.getAttribute('type') === 'password' ? 'visibility_off' : 'visibility' }}
                    </mat-icon>
                  </button>
                  <mat-error *ngIf="credentialForm.get('s3SecretKey').invalid">
                    {{ 'S3 secret key is required.' | translate }}
                  </mat-error>
                </mat-form-field>
                <p>{{ 'Connection alias' | translate }}</p>
                <mat-form-field>
                  <input matInput formControlName="dataSourceName" required>
                  <mat-error *ngIf="credentialForm.get('dataSourceName').hasError('required')"
                    fxLayoutAlign="none center">
                    <mat-icon>error_outline</mat-icon>
                    &nbsp; {{ 'The alias cannot be empty.' | translate }}
                  </mat-error>
                  <mat-error *ngIf="credentialForm.get('dataSourceName').hasError('isNameDuplicated')"
                    fxLayoutAlign="none center">
                    <mat-icon>error_outline</mat-icon>
                    &nbsp; {{ credentialForm.get('dataSourceName').getError('isNameDuplicated') }}
                  </mat-error>
                  <mat-error *ngIf="credentialForm.get('dataSourceName').hasError('isNameValid')"
                    fxLayoutAlign="none center">
                    <mat-icon>error_outline</mat-icon>
                    &nbsp; {{ credentialForm.get('dataSourceName').getError('isNameValid') }}
                  </mat-error>
                </mat-form-field>
              </form>
            </div>
            <!-- End of Input S3 credential information -->

            <!-- Start of Input GCS credential information -->
            <div *ngSwitchCase="dataSourceType.GoogleCloudStorage" class="gcs-credential-container">
              <div class="upload-json-key-section">
                <h3>
                  {{ 'Upload Google Cloud service account key' | translate }} &nbsp;
                  <a
                    href="https://cloud.google.com/iam/docs/creating-managing-service-account-keys#getting_a_service_account_key"
                    target="_blank"
                    class="link">
                    {{ 'Learn more' | translate }}
                  </a>
                </h3>
                <div
                  #dropArea
                  [ngClass]="'drop-area'"
                  (dragover)="onDragOver($event)"
                  (dragleave)="onDragLeave($event)"
                  (drop)="onDrop()">
                  <input
                    type="file"
                    title=""
                    #fileInput
                    id="fileInput"
                    (change)="handleDataSourceAuthFileUpload($event.target.files)"
                    attr.aria-label="{{ 'Upload Google Cloud service account key' | translate }}"/>
                  <p class="mat-body-2 upload-text">{{ gcsServiceAccountKeyFile?.name }}</p>
                  <button
                    mat-stroked-button
                    color="accent"
                    (click)="fileInput.click()">
                    {{ 'BROWSE' | translate }}
                  </button>
                  <p>{{ 'or drag and drop a file here.' | translate }}</p>
                </div>
                <mat-error *ngIf="gcsServiceAccountKeyFileContent && !gcsServiceAccountKeyFormatIsValid" fxLayoutAlign="none center">
                  <mat-icon>error_outline</mat-icon>
                  &nbsp; {{ 'File format is incorrect.' | translate }}
                </mat-error>
                <mat-hint *ngIf="gcsServiceAccountKeyFileContent && gcsServiceAccountKeyFormatIsValid" fxLayoutAlign="none center">
                  <mat-icon>done</mat-icon>
                  &nbsp; {{ 'File format is correct.' | translate }}
                </mat-hint>
                <br/>
                <div class="ui-e2e-trigger-container">
                  <input
                    aria-label="service account file content"
                    tabindex="-1"
                    class="service-account-key-input-e2e-trigger"
                    [(ngModel)]="gcsServiceAccountKeyFileContent">
                  <button
                    tabindex="-1"
                    aria-label="enable next button"
                    class="enable-next-button-e2e-trigger"
                    (click)="enableNextButtonInE2Etest()">
                  </button>
                </div>
              </div>

              <form [formGroup]="credentialForm" id="gcs-datasource-input">
                <p class="data-source-name-title">{{ 'Connection alias' | translate }}</p>
                <mat-form-field class="data-source-name-form">
                  <input matInput formControlName="dataSourceName" required>
                  <mat-error *ngIf="credentialForm.get('dataSourceName').hasError('required')"
                    fxLayoutAlign="none center">
                    <mat-icon>error_outline</mat-icon>
                    &nbsp; {{ 'The alias cannot be empty.' | translate }}
                  </mat-error>
                  <mat-error *ngIf="credentialForm.get('dataSourceName').hasError('isNameDuplicated')"
                    fxLayoutAlign="none center">
                    <mat-icon>error_outline</mat-icon>
                    &nbsp; {{ credentialForm.get('dataSourceName').getError('isNameDuplicated') }}
                  </mat-error>
                  <mat-error *ngIf="credentialForm.get('dataSourceName').hasError('isNameValid')"
                    fxLayoutAlign="none center">
                    <mat-icon>error_outline</mat-icon>
                    &nbsp; {{ credentialForm.get('dataSourceName').getError('isNameValid') }}
                  </mat-error>
                  <mat-hint *ngIf="credentialForm.valid" fxLayoutAlign="none center">
                    {{ 'Success. The alias is valid.' | translate }}
                  </mat-hint>
                </mat-form-field>
              </form>
            </div>
            <!-- End of Input GCS credential information -->

            <!-- Start of Input ABS credential information -->
            <div *ngSwitchCase="dataSourceType.AzureBlobStorage" class="credential-container">
              <form
                [formGroup]="credentialForm"
                id="abs-datasource-input">
                <p class="abs-require-setting-msg">{{ 'Required: ' | translate }}
                  <a
                 href="https://learn.microsoft.com/en-us/azure/storage/blobs/soft-delete-blob-enable?tabs=azure-portal#enable-blob-soft-delete"
                 target="_blank"
                 class="link">
                 {{ 'Disable Soft Delete for blobs' | translate }}
                 </a>
               </p>
                <p>{{ 'Connection String' | translate }}
                  <a
                  href="https://learn.microsoft.com/en-us/azure/storage/common/storage-account-keys-manage?toc=%2Fazure%2Fstorage%2Fblobs%2Ftoc.json&tabs=azure-portal#view-account-access-keys"
                  target="_blank"
                  class="link">
                  {{ 'Learn more' | translate }}
                  </a>
                </p>
                <mat-form-field>
                  <input matInput type='password' formControlName="absConnectionString" #connectionStringInput required>
                  <button
                    matSuffix
                    mat-button
                    mat-icon-button
                    matTooltip = "{{ 'Click to show or hide your ABS connection string' | translate }}"
                    (click)="showOrHidePW(connectionStringInput)">
                    <mat-icon>
                      {{ connectionStringInput.getAttribute('type') === 'password' ? 'visibility_off' : 'visibility' }}
                    </mat-icon>
                  </button>
                  <mat-error *ngIf="credentialForm.get('absConnectionString').hasError('required')">
                    {{ 'Azure blob stroage connection string is required.' | translate }}
                  </mat-error>
                  <mat-error *ngIf="credentialForm.get('absConnectionString').hasError('pattern')">
                    {{ 'Please follow the connection string format. '| translate }}
                    <a
                      href="https://learn.microsoft.com/en-us/azure/storage/common/storage-configure-connection-string#configure-a-connection-string-for-an-azure-storage-account"
                      target="_blank"
                      class="link">
                      {{ 'Learn more' | translate }}
                    </a>
                  </mat-error>
                </mat-form-field>
                <p>{{ 'Connection alias' | translate }}</p>
                <mat-form-field>
                  <input matInput formControlName="dataSourceName" required>
                  <mat-error *ngIf="credentialForm.get('dataSourceName').hasError('required')"
                    fxLayoutAlign="none center">
                    <mat-icon>error_outline</mat-icon>
                    &nbsp; {{ 'The alias cannot be empty.' | translate }}
                  </mat-error>
                  <mat-error *ngIf="credentialForm.get('dataSourceName').hasError('isNameDuplicated')"
                    fxLayoutAlign="none center">
                    <mat-icon>error_outline</mat-icon>
                    &nbsp; {{ credentialForm.get('dataSourceName').getError('isNameDuplicated') }}
                  </mat-error>
                  <mat-error *ngIf="credentialForm.get('dataSourceName').hasError('isNameValid')"
                    fxLayoutAlign="none center">
                    <mat-icon>error_outline</mat-icon>
                    &nbsp; {{ credentialForm.get('dataSourceName').getError('isNameValid') }}
                  </mat-error>
                </mat-form-field>
              </form>
            </div>
            <!-- End of Input ABS credential information -->
          </div>

          <div *ngIf="isDataSourceListDialogOpen" class="max-width gcs-data-source-list-section">
            <p>{{ 'Select a data source to continue' | translate }}</p>
            <div class="gcs-list-container">
              <mat-nav-list *ngIf="dataSourceList.length > 0" dense>
                <a
                  id="data-file"
                  href="javascript:void(0)"
                  mat-list-item
                  *ngFor="let dataSource of dataSourceList;"
                  [matTooltip]="dataSource.name"
                  (click)="onDataSourceSelection(dataSource.name); selectedDataSource = source;"
                  [class.active]="dataSource === selectedDataSourceName"
                  role="tab"
                  [attr.aria-selected]="dataSource === selectedDataSourceName"
                  attr.aria-label="{{ dataSource.name }}">
                  <div matLine>
                    <button
                      mat-icon-button
                      (click)="confirmDeleteDataSource(dataSource); $event.stopPropagation()"
                      matTooltip="{{ 'Delete' | translate }} {{ dataSource.name }}"
                      attr.aria-label="{{ 'Delete' | translate }} {{ dataSource.name }}">
                      <mat-icon
                        attr.aria-label="{{ 'Delete' | translate }} {{ dataSource.name }}">
                        delete_forever
                      </mat-icon>
                    </button>
                    <span class="select-filename">{{ dataSource.name }}</span>
                  </div>
                </a>
              </mat-nav-list>
            </div>
            <div class="upload-container">
              <button
                mat-mini-fab
                color="accent"
                (click)="addNewDataSource()"
                matTooltip="{{ getAddNewDataSourceButtonTooltip() | translate }}"
                matTooltipPosition="above"
                attr.aria-label="{{ getAddNewDataSourceButtonTooltip() | translate }}">
                <mat-icon attr.aria-label="{{ getAddNewDataSourceButtonTooltip() | translate }}">add</mat-icon>
              </button>
            </div>
          </div>

          <!-- Start of Data Preview -->

          <!-- Start of Add Data Source and Data Set-->
          <div
            *ngIf="isDataPreviewDialogOpen && isRemoteDataSourceType"
            fxLayout="column"
            class="max-width">
            <div>
              <mat-label>File Type</mat-label>
              <mat-hint>
                <p>{{ 'We will parse the data files based on your selected data format.' | translate }}</p>
                <p>{{'For the JSON data format, the JSON loader requires that each input line has exactly one JSON object.' | translate }}</p>
              </mat-hint>
            </div>
            <div>
              <mat-radio-group
                aria-label="Select a file type"
                fxShow
                fxLayout.lt-sm="column"
                fxLayout="row"
                fxLayoutGap="10px"
                fxLayoutGap.lt-sm="0px"
                [(ngModel)]="selectedDataFormat">
                <mat-radio-button *ngFor="let type of getDataFormatList();" [value]="type">
                  {{ type.toUpperCase() }}
                </mat-radio-button>
              </mat-radio-group>
            </div>
            <div
              class="uri-input-form-container"
              fxShow
              fxLayout.lt-sm="column"
              fxLayout="row"
              fxLayoutAlign="center center"
              fxLayoutGap="10px"
              fxLayoutGap.lt-sm="0px">
              <form
                [formGroup]="uriInputForm"
                fxFlex="calc(100% - 100px)" class="max-width">
                <mat-form-field fxFlex="calc(100% - 100px)">
                  <mat-label>{{ dataSourceURIName }}</mat-label>
                  <input
                    formControlName="uri"
                    id="datasource-file-path-input"
                    matInput
                    placeholder="{{ dataSourceUriPlaceholder }}"
                  >
                  <mat-error *ngIf="uriInputForm.get('uri').hasError('required')"
                    fxLayoutAlign="none center">
                    {{ 'Sorry, the URI cannot be empty.' | translate }}
                  </mat-error>
                  <mat-error *ngIf="
                    !uriInputForm.get('uri').hasError('required') &&
                    uriInputForm.get('uri').hasError('invalidUri')"
                    fxLayoutAlign="none center">
                    {{ uriInputForm.get('uri').getError('invalidUri') }}
                  </mat-error>
                </mat-form-field>
              </form>
              <button
                id="datasource-connect-button"
                mat-raised-button
                color="accent"
                (click)="onFileSelection(uriInputForm.get('uri').value)"
                [disabled]="uriInputForm.get('uri').invalid"
                fxFlex="50%">
                {{ 'CONNECT' | translate }}
              </button>
            </div>
            <p *ngIf="tabularSampleData || jsonSampleData" class="hint-text text-left">
              {{ 'To parse the data correctly, please adjust the options below.' | translate }}
            </p>
          </div>
          <!-- End of Add Data Source and Data Set -->

          <div *ngIf="isDataPreviewDialogOpen"
            [ngSwitch]="selectedDataSourceType"
            fxLayout.gt-md="row"
            fxLayoutGap.gt-md="0px"
            [ngClass.gt-md]="{'file-source-container': !isRemoteDataSourceType}"
            [ngClass.lt-lg]="{'file-source-container': !selectedFile && (selectedDataSourceType === dataSourceType.File)}">
            <div
              fxShow
              [fxHide.lt-lg]="selectedFile">
              <!-- Files on server -->
              <div *ngSwitchCase="dataSourceType.File" fxLayout.lt-lg="column" fxLayout="row">
                <div
                  fxFlex.gt-md="250px"
                  fxFlex.lt-lg="100%"
                  fxLayoutGap="0px"
                  [ngClass.gt-md]="'upload-list-container'"
                  fxLayoutAlign.lt-lg="space-around start">
                  <div class="max-width">
                    <div class="list-header" fxLayout="row" fxLayoutAlign="space-between end">
                      <p>{{ 'Files' | translate }}</p>
                      <input
                        #fileInput
                        type="file"
                        (change)="uploadFile($event.target.files)"
                        accept=".csv,.tsv,.json,.jsonl"
                        multiple
                        hidden>
                      <button
                        mat-flat-button
                        (click)="fileInput.click()"
                        color="primary">
                        {{ 'Upload File' | translate }}
                      </button>
                    </div>

                    <div class="empty-upload-list" *ngIf="(fileList$ | async).length === 0">
                      <p class="hint-text">
                        {{ 'Supports CSV, TSV and ' | translate }}
                        <a
                          href="https://jsonlines.org/"
                          target="_blank"
                          class="link">
                          JSON Lines
                        </a>
                      </p>
                    </div>

                    <mat-nav-list dense class="list-container"
                      *ngIf="(fileList$ | async).length != 0">
                      <a
                        id="data-file"
                        href="javascript:void(0)"
                        mat-list-item
                        *ngFor="let file of (fileList$ | async);"
                        (click)="onFileSelection(getFileName(file))"
                        [matTooltip]="showFileInfo(file)"
                        [class.active]="getFileName(file) === selectedFile"
                        role="tab"
                        [attr.aria-selected]="getFileName(file) === selectedFile"
                        attr.aria-label="{{ getFileName(file) + ': ' + showFileInfo(file) }}">
                        <div matLine>
                          <button
                            mat-icon-button
                            (click)="confirmDeleteFile(file); $event.stopPropagation()"
                            matTooltip="{{ 'Delete' | translate }} {{getFileName(file)}}"
                            attr.aria-label="{{ 'Delete' | translate }} {{ getFileName(file) }}">
                            <mat-icon
                              attr.aria-label="{{ 'Delete' | translate }} {{ getFileName(file) }}">
                              delete_forever
                            </mat-icon>
                          </button>
                          <span class="select-filename">{{ getFileName(file) }}</span>
                        </div>
                      </a>
                    </mat-nav-list>
                  </div>
                </div>
              </div>
              <!-- End of Local File List -->
            </div>

            <!-- Parser and Sample Data -->
            <div
              *ngIf="selectedDataSourceType != dataSourceType.File || (fileList$ | async).length != 0"
              fxFlex
              fxFlex.lt-lg="grow"
              fxLayout="column"
              fxLayoutGap="0px"
              class="file-container"
              [ngClass]="{'file-container': !isRemoteDataSourceType}"
              fxShow
              [fxHide.lt-lg]="!selectedFile && (selectedDataSourceType === dataSourceType.File)"
              [ngClass.lt-lg]="'max-width'">

              <!-- Hint -->
              <div *ngIf="!(tabularSampleData || jsonSampleData)"
                fxShow
                [fxHide.lt-lg]="!selectedFile && (selectedDataSourceType === dataSourceType.File)"
                fxLayout="column"
                fxLayoutAlign="center center"
                fxLayoutGap="0px"
                class="file-hint hint-text">
                <div *ngIf="isRemoteDataSourceType">
                  <p fxLayoutAlign="left">
                    {{ 'Enter' | translate }} &nbsp;
                    <a
                      href="{{ getUriGuideLink() }}"
                      target="_blank"
                      class="link">
                      {{ dataSourceURIName| translate }}
                    </a>
                    &nbsp; {{ 'above to connect to a data file or folder.' | translate }}
                  </p>
                  <p fxLayoutAlign="left">
                    {{ 'If you want to load a folder, all of its files should have the same data schema to be loaded successfully.
                      Otherwise, you have to load the files one by one.' | translate }}
                  </p>
                </div>
                <div *ngIf="!isRemoteDataSourceType && isDataPreviewDialogOpen">
                  <p>{{ 'Select a file from the left list.' | translate }}</p>
                </div>
              </div>
              <!-- End of Hint -->

              <!-- Parser -->
              <div *ngIf="isDataPreviewDialogOpen">
                <!-- Tabular Parser -->
                <div *ngIf="tabularSampleData" class="parser-container" fxLayout="column">
                  <div
                    fxLayout="row"
                    fxLayout.lt-md="column"
                    fxLayoutAlign="center center"
                    fxLayoutGap="12px">
                    <mat-form-field fxFlex="20%">
                      <mat-select
                        [placeholder]="dataParserForm.format.placeholder"
                        [(ngModel)]="tabularParser.format"
                        (selectionChange)="getSampleData()"
                        panelClass="select-panel-container">
                        <mat-option
                          *ngFor="let option of dataParserForm.format.options"
                          [value]="option.key"
                          [disabled]="!formatFilter.get(selectedDataSourceType).includes(option.key)">
                          {{ option.value }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>

                    <mat-form-field fxFlex="20%">
                      <mat-select
                        [placeholder]="dataParserForm.separator.placeholder"
                        [(ngModel)]="tabularParser.separator"
                        (selectionChange)="getSampleData()"
                        panelClass="select-panel-container">
                        <mat-option *ngFor="let option of dataParserForm.separator.options" [value]="option.key">
                          {{ option.value }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>

                    <mat-form-field fxFlex="20%">
                      <mat-select
                        [placeholder]="dataParserForm.eol.placeholder"
                        [(ngModel)]="tabularParser.eol"
                        (selectionChange)="getSampleData()"
                        panelClass="select-panel-container">
                        <mat-option *ngFor="let option of dataParserForm.eol.options" [value]="option.key">
                          {{ option.value }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>

                    <mat-form-field fxFlex="20%">
                      <mat-select
                        [placeholder]="dataParserForm.quote.placeholder"
                        [(ngModel)]="tabularParser.quote"
                        (selectionChange)="getSampleData()"
                        panelClass="select-panel-container">
                        <mat-option *ngFor="let option of dataParserForm.quote.options" [value]="option.key">
                          {{ option.value }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>

                    <mat-checkbox
                      fxShow
                      fxHide.lt-lg
                      fxFlex="20%"
                      [(ngModel)]="tabularParser.header"
                      (change)="getSampleData()">
                      {{ dataParserForm.header.placeholder }}
                    </mat-checkbox>

                    <div fxHide fxShow.lt-lg fxLayoutAlign="center center">
                      <mat-checkbox [(ngModel)]="tabularParser.header" (change)="getSampleData()">
                        {{ dataParserForm.header.placeholder }}
                      </mat-checkbox>
                    </div>
                  </div>
                </div>
                <!-- End of Tabular Parser -->

                <!-- JSON Parser -->
                <div *ngIf="jsonSampleData" class="parser-container" fxLayout="column">
                  <div
                    fxLayout="row"
                    fxLayout.lt-md="column"
                    fxLayoutAlign="center center"
                    fxLayoutGap="12px">
                    <mat-form-field fxFlex="20%">
                      <mat-select
                        [placeholder]="dataParserForm.format.placeholder"
                        [(ngModel)]="jsonParser.format"
                        (selectionChange)="getSampleData()"
                        panelClass="select-panel-container">
                        <mat-option
                          *ngFor="let option of dataParserForm.format.options"
                          [value]="option.key"
                          [disabled]="!formatFilter.get(selectedDataSourceType).includes(option.key)">
                          {{ option.value }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>

                    <mat-form-field fxFlex="20%">
                      <mat-select
                        [placeholder]="dataParserForm.eol.placeholder"
                        [(ngModel)]="jsonParser.eol"
                        (selectionChange)="getSampleData()"
                        panelClass="select-panel-container">
                        <mat-option *ngFor="let option of dataParserForm.eol.options" [value]="option.key">
                          {{ option.value }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                  </div>
                </div>
                <!-- End of JSON Parser -->
              </div>
              <!-- End of Parser -->

              <!-- Sample Data -->
              <div *ngIf="isSampleDataDefined && isSampleDataPresent && isDataPreviewDialogOpen"
                fxFlex
                fxFlex.lt-lg="none"
                class="table-container">

                <mat-table
                  *ngIf="tabularSampleData"
                  [dataSource]="sampleDataSource"
                  class="mat-elevation-z4 sample-data-table">
                  <caption class="hint-text text-left">{{ 'Sample data' | translate }}</caption>
                  <ng-container *ngFor="let column of sampleColumnDefs" [matColumnDef]="column.id">
                    <mat-header-cell
                      *matHeaderCellDef
                      (dblclick)="editHeaderText(column)">
                      <span *ngIf="!column.editing"> {{ column.headerText }} </span>
                      <div *ngIf="column.editing" fxLayout="row" fxLayoutGap="0px" class="header-editor">
                        <mat-form-field>
                          <input matInput [(ngModel)]="newHeaderText">
                        </mat-form-field>
                        <button
                          mat-icon-button
                          class="cancel-btn"
                          matTooltip="{{ 'CANCEL' | translate }}"
                          (click)="finishHeaderEditing(column)"
                          attr.aria-label="{{ 'CANCEL' | translate }}">
                          <mat-icon attr.aria-label="{{ 'CANCEL' | translate }}">close</mat-icon>
                        </button>
                        <button
                          mat-icon-button
                          matTooltip="{{ 'Done' | translate }}"
                          color="primary"
                          (click)="finishHeaderEditing(column, true)"
                          attr.aria-label="{{ 'Done' | translate }}">
                          <mat-icon attr.aria-label="{{ 'Done' | translate }}">check</mat-icon>
                        </button>
                      </div>
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row"> {{ row[column.property] }} </mat-cell>
                  </ng-container>
                  <mat-header-row *matHeaderRowDef="sampleColumnIds"></mat-header-row>
                  <mat-row *matRowDef="let row; columns: sampleColumnIds;"></mat-row>
                </mat-table>

                <div *ngIf = "jsonSampleData">
                  <p *ngFor="let item of jsonText" class="json-sample-data-obj">
                    <app-text-editor  [config]="jsonEditorConfig" [(ngModel)]="item" #jsonViewer></app-text-editor>
                  </p>
                </div>

              </div>
              <!-- End of Sample Data -->

              <!-- Failed Sample Data -->
              <div *ngIf="isSampleDataDefined && !isSampleDataPresent && !(loading | async)"
                fxLayout="column"
                fxShow
                fxLayoutAlign="center center"
                fxLayoutGap="0px"
                class="file-hint hint-text reflow-hint-text-size failed-sample-data-container">
                <p>{{ 'Failed to get any sample data.' | translate }}</p>
                <p>{{ 'Please try other parsing options or check your file.' | translate }}</p>
              </div>
              <!-- End of Failed Sample Data -->
            </div>
            <!-- End of Parser and Sample Data-->
          </div>

          <!-- JSON Schema Preview-->
          <app-vis-json-schema-editor
            *ngIf="isEditSchemaDialogOpen"
            [standardJsonSchema]="standardJSONSchema">
          </app-vis-json-schema-editor>
          <!-- End of JSON Schema Preview -->
        </mat-dialog-content>
      </app-loading-indicator>
    </div>
    <!-- End of Add Data File -->

    <!-- Add Token Function -->
    <mat-dialog-content
      *ngIf="selectedEvent === 'addTokenFunction'"
      fxLayout.gt-sm="row"
      fxLayout ="column"
      fxLayoutGap.gt-sm="24px">
      <div fxFlex.gt-sm="40%" fxLayout="column" fxLayoutGap="0px">
        <mat-form-field class="token-select max-width">
          <mat-select
            placeholder="{{ 'Function name' | translate }}"
            [(ngModel)]="selectedTokenFunction"
            panelClass="select-panel-container">
            <mat-option *ngFor="let option of (combinedTokenFunctionList | async)" [value]="option">
              {{ option.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field class="max-width">
          <input
            matInput
            placeholder="{{ 'Input parameter number' | translate }}"
            type="number"
            [(ngModel)]="selectedTokenFunction.paramNumber"
            [disabled]="selectedTokenFunction.paramNumberFixed"
            [min]="selectedTokenFunction.paramNumberFixed ? undefined : 1">
        </mat-form-field>
      </div>
      <div fxFlex.gt-sm="60%" class="token-description">
        <span>{{ 'Description' | translate }}</span>
        <div class="box-container">
          <span *ngIf="selectedTokenFunction.doc">
            <span>{{ selectedTokenFunction.doc.description }}</span> <br/> <br/>
            <span *ngFor="let sample of selectedTokenFunction.doc.samples; let i = index">
                <span>Sample {{ i + 1 }}:</span> <br/>
              <i>Input:</i> <br/>
              <span *ngFor="let input of sample.sampleInput; let j = index" class="tab">
                  "{{ input }}" <span *ngIf="j < sample.sampleInput.length - 1">,</span>
              </span> <br/>
              <i>Output:</i> <br/>
              <span class="tab">{{ sample.sampleOutput }}</span>
              <br/> <br *ngIf="i < selectedTokenFunction.doc.samples.length - 1" />
              </span>
          </span>
          <div *ngIf="selectedTokenFunction.code" class="token-function-code">
            <pre>{{ selectedTokenFunction.code }}</pre>
          </div>
        </div>
      </div>
    </mat-dialog-content>
    <!-- End of Add Token Funcion -->

    <!-- Add Const Mapping -->
    <mat-dialog-content *ngIf="selectedEvent === 'addConstMapping'">
      <div>
        <mat-form-field class="string-input">
          <input matInput placeholder="{{ 'Input constant value' | translate }}" type="string" [(ngModel)]="inputConstString">
        </mat-form-field>
      </div>
    </mat-dialog-content>
    <!-- End of Add Const Mapping -->

    <!-- Add Where Clause -->
    <mat-dialog-content *ngIf="selectedEvent === 'addWhereClause'" fxLayout="column" fxLayoutGap="12px">
      <div></div>
      <div class="sample-data-table box-container">
        <div class="table-container">
          <mat-table [dataSource]="whereClauseDataSource" class="mat-elevation-z4">
            <caption class="hint-text text-left sample-data-caption">{{ 'Sample data' | translate }}</caption>
            <ng-container *ngFor="let column of whereClauseColumnDefs" [matColumnDef]="column.id">
              <mat-header-cell *matHeaderCellDef> {{ column.headerText }} </mat-header-cell>
              <mat-cell *matCellDef="let row"> {{ row[column.property] }} </mat-cell>
            </ng-container>

            <mat-header-row *matHeaderRowDef="whereClauseColumnIds"></mat-header-row>
            <mat-row *matRowDef="let row; columns: whereClauseColumnIds;"></mat-row>
          </mat-table>
        </div>
      </div>

      <div>
        <span class="hint-text">{{ 'Data filter' | translate }}</span>
        <div class="box-container">
          <span class="data-filter-expression-string">{{ whereClauseModel?.toString() || 'None' }}</span>
        </div>

        <div>
          <span class="hint-text">{{ 'Build data filter' | translate }}</span>
          <app-expression-form [(data)]="whereClauseModel" [config]="exprFormConfig">
          </app-expression-form>
        </div>
      </div>
    </mat-dialog-content>
    <!-- End of Add Where Clause -->

    <!-- Add Map Widget -->
    <mat-dialog-content *ngIf="selectedEvent === 'addMap'">
      <div>&nbsp;</div>
      <div class="box-container" fxLayout.lt-sm="column" fxLayout="row" fxLayoutGap="12px">
        <mat-form-field [fxFlex]="mapWidgetValueType === 'UDT' ? '33%' : '50%'">
          <mat-select placeholder="{{ 'Key type' | translate }}" [(ngModel)]="mapWidgetKeyType" panelClass="select-panel-container">
            <mat-option *ngFor="let option of supportedMapKeyTypes" [value]="option">
              {{ option }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field [fxFlex]="mapWidgetValueType === 'UDT' ? '33%' : '50%'">
          <mat-select placeholder="{{ 'Value type' | translate }}" [(ngModel)]="mapWidgetValueType" panelClass="select-panel-container">
            <mat-option *ngFor="let option of supportedContainerValueTypes" [value]="option">
              {{ option }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field *ngIf="mapWidgetValueType === 'UDT'" fxFlex="33%">
          <mat-select
            placeholder="{{ 'UDT name' | translate }}"
            [(ngModel)]="widgetUDTName"
            panelClass="select-panel-container">
            <mat-option *ngFor="let option of udtList" [value]="option.name">
              {{ option.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </mat-dialog-content>
    <!-- End of Add Map Widget -->

    <!-- Add Tuple Widget -->
    <mat-dialog-content *ngIf="selectedEvent === 'addTuple'">
      <div>&nbsp;</div>
      <div class="box-container">
        <mat-form-field fxFlex="100%">
          <mat-select
            placeholder="{{ 'UDT name' | translate }}"
            [(ngModel)]="widgetUDTName"
            panelClass="select-panel-container">
            <mat-option *ngFor="let option of udtList" [value]="option.name">
              {{ option.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </mat-dialog-content>
    <!-- End of Add Tuple Widget -->

    <!-- Left Chart Visual Information Content -->
    <mat-dialog-content *ngIf="selectedEvent === toolbarKeys.ShowLeftVisInfo">
      <br/>

      <!-- Vertex Information -->
      <h3>{{ 'Vertex information' | translate }}:</h3>
      <mat-list role="list" attr.aria-label="{{ 'Vertex information' | translate }}" tabindex="0">
        <ng-container *ngFor="let vertex of leftChartGraphRef.current.getNodes()">
          <mat-list-item *ngIf="vertex.type !== 'gs_file' &&
            vertex.type !== 'gs_s3' &&
            vertex.type !== 'gs_gcs' &&
            vertex.type !== 'gs_abs' &&
            vertex.type !== 'gs_url' &&
            vertex.type !== 'gs_virtual'
          "
          tabindex="0">
            <div matSubHeader matLine role="listitem">{{ ('Vertex' | translate) + ': ' + vertex.type }}</div>
            <div matLine *ngFor="let attr of vertex.attrs | keyvalue">
              <div matLine role="listitem">{{ attr.key + ': ' + attr.value }}</div>
            </div>
            <hr matLine/>
          </mat-list-item>
        </ng-container>
      </mat-list>
      <!-- End of Vertex Information -->

      <!-- Edge Information -->
      <h3>{{ 'Edge information' | translate }}:</h3>
      <mat-list role="list" attr.aria-label="{{ 'Edge information' | translate }}" tabindex="0">
        <ng-container *ngFor="let edge of leftChartGraphRef.current.getLinks()">
          <mat-list-item *ngIf="!edge.type.includes('gs_data_source_map')" tabindex="0">
            <div matSubHeader matLine role="listitem">{{ ('Edge' | translate) + ': ' + edge.type }}</div>
            <div matLine role="listitem">{{ ('Source' | translate) + ': ' + edge.source.type }}</div>
            <div matLine role="listitem">{{ ('Target' | translate) + ': ' + edge.target.type }}</div>
            <div matLine *ngFor="let attr of edge.attrs | keyvalue">
              <div matLine role="listitem">{{ attr.key + ': ' + attr.value }}</div>
            </div>
            <hr matLine/>
          </mat-list-item>
        </ng-container>
      </mat-list>
      <!-- End of Edge Information -->

      <!-- Data Source and Loading Job Information -->
      <h3>{{ 'Data source and loading job information' | translate }}:</h3>
      <mat-list role="list" attr.aria-label="{{ 'Data source and loading job information' | translate }}" tabindex="0">
        <ng-container *ngFor="let lj of logicService.loadingJobs; let ljIndex = index">
          <mat-list-item role="listitem" tabindex="0">
            <div matSubHeader matLine role="listitem" style="overflow-wrap: break-word;">
              {{ ('Data source name' | translate) + ': ' + lj.dataSet.uri }}
            </div>
            <div matLine role="listitem">{{ ('Data source type' | translate) + ': ' + lj.dataSource.type }}</div>
            <div *ngIf="getLatestLoadingProgress(lj.jobName) as loadProg; else notMapped" matLine>
              <div matLine role="listitem">
                {{
                  ('Status' | translate) + ': ' + (loadProg.status === 'INITIAL' ? ('NOT STARTED' | translate) : (loadProg.status | translate ))
                }}
              </div>
              <div *ngIf="lj.dataSource.type === 'file'" matLine role="listitem">{{ ('Loading percentage' | translate) + ': ' + loadProg.percentage }}</div>
            </div>
            <ng-template #notMapped>
              <div  matLine role="listitem" >{{ 'Status' | translate }}: {{ 'Data not mapped' | translate }}</div>
            </ng-template>
            <div matLine *ngFor="let ls of lj.loadingStatements; let lsIndex = index">
              <div matLine role="listitem">{{ 'Data source mapping' | translate }} {{ lsIndex + 1 }}:</div>
              <div *ngIf="ls.vertexName" matLine role="listitem">
                {{ ('Map to vertex' | translate) + ': ' + ls.vertexName }}
              </div>
              <div *ngIf="ls.edgeName" matLine role="listitem">
                {{ ('Map to edge' | translate) + ': ' + ls.edgeName }}
              </div>
              <div *ngIf="logicService.semanticCheckResults[ljIndex].message[lsIndex].length > 0 ; else whenSuccess">
                <div matLine role="listitem">{{ 'Semantic errors' | translate }}</div>
                <div matLine role="listitem">
                  {{ ('Error' | translate) + ': ' + logicService.semanticCheckResults[ljIndex].message[lsIndex] }}
                </div>
              </div>
              <ng-template #whenSuccess matLine role="listitem">{{ 'No errors found' | translate }}</ng-template>
              <br/>
            </div>
            <div *ngIf="lj.dataSource.type === 'file'" matLine>
              <div *ngFor="let vertex of leftVisService.graphRef.current.getNodesByTypes(['gs_file'])">
                <div *ngIf="vertex.id === ljIndex + ''">
                  <div *ngIf="vertex.others.fileInfo as fileInfo;
                    else noFileInfo;">
                    <div matLine role="listitem">
                      {{ ('Last modified time' | translate) + ': ' + fileInfo.modTime }}
                    </div>
                    <div matLine role="listitem">
                      {{ ('Size' | translate) + ': ' + fileInfo.size }}
                    </div>
                  </div>
                  <ng-template #noFileInfo matLine role="listitem">
                    {{ 'Warning' | translate }}: {{ 'This file is not on server any more.' | translate }}
                  </ng-template>
                </div>
              </div>
            </div>
            <hr matLine/>
          </mat-list-item>
        </ng-container>
      </mat-list>
      <!-- End of Data Source and Loading Job Information -->

    </mat-dialog-content>
    <!-- End of Left Chart Visual Information Content -->

    <!-- Right Chart Visual Information Content -->
    <mat-dialog-content *ngIf="selectedEvent === toolbarKeys.ShowRightVisInfo">
      <br/>

      <!-- Loading Statement Information-->
      <h3>{{ 'Loading statement' | translate }}</h3>
      <!-- Data Source Information -->
      <div *ngIf="logicService.currentDataSourceTitleHeaderAndSampleData">
        <mat-list role="list" attr.aria-label="{{ 'Data source information' | translate }}" tabindex="0">
          <mat-list-item tabindex="0">
            <div matSubHeader matLine role="listitem">
              {{ ('Data source name' | translate) + ': ' + logicService.currentDataSourceTitleHeaderAndSampleData.title }}
            </div>
          </mat-list-item>
          <ng-container *ngFor="let header of logicService.currentDataSourceTitleHeaderAndSampleData.header; let i = index">
            <mat-list-item tabindex="0">
              <div matLine role="listitem">{{ 'Column ' + (i + 1) + ' header: ' + header }}</div>
              <div matLine role="listitem" *ngIf="logicService.currentDataSourceTitleHeaderAndSampleData.sampleData && logicService.currentDataSourceTitleHeaderAndSampleData.sampleData.length > 0">
                {{
                  'Column ' + (i + 1) + ' first row data: ' +
                  logicService.currentDataSourceTitleHeaderAndSampleData.sampleData[0][i]
                }}
              </div>
            </mat-list-item>
          </ng-container>
        </mat-list>
        <hr />
      </div>
      <!-- End of Data Source Information-->

      <br />

      <div *ngIf="logicService.currentLoadingStatement">
        <div *ngIf="logicService.currentLoadingStatement.vertexName">
          <mat-list role="list" attr.aria-label="{{ 'Vertex information' | translate }}" tabindex="0">
            <!-- Vertex Information -->
            <mat-list-item tabindex="0">
              <div matSubHeader matLine role="listitem">
                {{ ('Load to vertex' | translate) + ': ' + logicService.currentLoadingStatement.vertexName }}
              </div>
              <div matLine role="listitem">{{ 'Attributes' | translate }}:</div>
              <div matLine *ngFor="let vertex of leftChartGraphRef.current.getNodes()">
                <div *ngIf="vertex.type === logicService.currentLoadingStatement.vertexName &&
                  vertex.id === logicService.currentLoadingStatement.vertexName
                ">
                  <ng-container matLine *ngFor="let attr of vertex.attrs | keyvalue;">
                    <div matLine role="listitem">{{ attr.key + ': ' + attr.value }}</div>
                  </ng-container>
                </div>
              </div>
            </mat-list-item>
            <!-- End of Vertex Information -->
          </mat-list>
          <hr />

          <!-- Mapping Information -->
          <h3>{{ 'Mapping to vertex' | translate }}:</h3>
          <mat-list role="list" attr.aria-label="{{ 'Mapping to vertex' | translate }}" tabindex="0">
            <ng-container *ngFor="let map of logicService.currentLoadingStatement.mappings;
              let mapIndex = index">
              <mat-list-item *ngIf="mapIndex === 0" tabindex="0">
                <div *ngIf="map.sourceType === 0" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1) + ': ' +
                    'From data source column "' +
                    logicService.currentDataSourceTitleHeaderAndSampleData.header[map.index] +
                    '" to primary id "' +
                    logicService.graph.getVertex(logicService.currentLoadingStatement.vertexName).primaryId.name +
                    '"'
                  }}
                </div>
                <div *ngIf="map.sourceType === 1" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1) + ': ' +
                    'Widget "' +
                    logicService.currentLoadingStatement.mappingWidgets[map.index].funcName +
                    '\'s" output to primary id "' +
                    logicService.graph.getVertex(logicService.currentLoadingStatement.vertexName).primaryId.name +
                    '"'
                  }}
                </div>
                <div *ngIf="map.sourceType === 2" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1) + ': ' + 'Unassigned mapping to primary id'
                  }}
                  </div>
                <div *ngIf="map.sourceType === 3" matLine role="listitem">
                  {{
                    'Mapping ' + mapIndex + ': ' + 'Literal ' + map.index
                  }}
                </div>
              </mat-list-item>
              <mat-list-item *ngIf="mapIndex > 0" tabindex="0">
                <div *ngIf="map.sourceType === 0" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1) + ': ' +
                    'From data source column "' +
                    logicService.currentDataSourceTitleHeaderAndSampleData.header[map.index] +
                    '" to vertex attribute "' +
                    logicService.graph.getVertex(logicService.currentLoadingStatement.vertexName).attributes[mapIndex - 1].name +
                    '"'
                  }}
                </div>
                <div *ngIf="map.sourceType === 1" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1) + ': ' +
                    'Widget "' +
                    logicService.currentLoadingStatement.mappingWidgets[map.index].funcName +
                    '\'s" output to vertex attribute "' +
                    logicService.graph.getVertex(logicService.currentLoadingStatement.vertexName).attributes[mapIndex - 1].name +
                    '"'
                  }}
                </div>
                <div *ngIf="map.sourceType === 2" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1) + ': ' + 'Unassigned mapping'
                  }}
                </div>
                <div *ngIf="map.sourceType === 3" matLine role="listitem">
                  {{
                    'Mapping' + (mapIndex + 1) + ': ' + 'Literal ' + map.index
                  }}
                </div>
              </mat-list-item>
            </ng-container>
          </mat-list>
          <hr />
          <!-- End of Mapping Information -->
        </div>

        <div *ngIf="logicService.currentLoadingStatement.edgeName">
          <!-- Edge Information -->
          <mat-list role="list" attr.aria-label="{{ 'Edge information' | translate }}" tabindex="0">
            <mat-list-item tabindex="0">
              <div matSubHeader matLine role="listitem">
                {{ ('Load to edge' | translate) + ': ' + logicService.currentLoadingStatement.edgeName }}
              </div>
              <div matLine role="listitem">
                {{ ('Source vertex' | translate) + ': ' + logicService.currentLoadingStatement.fromVertexType }}
              </div>
              <div matLine role="listitem">
                {{ ('Target vertex' | translate) + ': ' + logicService.currentLoadingStatement.toVertexType }}
              </div>
              <div matLine role="listitem">{{ 'Attributes' | translate }}:</div>
              <div *ngFor="let edge of leftChartGraphRef.current.getLinks()">
                <div *ngIf="edge.type === logicService.currentLoadingStatement.edgeName &&
                  edge.source.type === logicService.currentLoadingStatement.fromVertexType &&
                  edge.source.id === logicService.currentLoadingStatement.fromVertexType &&
                  edge.target.type === logicService.currentLoadingStatement.toVertexType &&
                  edge.target.id === logicService.currentLoadingStatement.toVertexType
                ">
                  <ng-container matLine *ngFor="let attr of edge.attrs | keyvalue;">
                    <div matLine role="listitem">{{ attr.key + ': ' + attr.value }}</div>
                  </ng-container>
                </div>
              </div>
            </mat-list-item>
          </mat-list>
          <hr />
          <!-- End of Edge Information -->

          <!-- Mapping Information -->
          <h3>{{ 'Mapping to edge' | translate }}:</h3>
          <mat-list role="list" attr.aria-label="{{ 'Mapping to edge' | translate }}" tabindex="0">
            <ng-container *ngFor="let map of logicService.currentLoadingStatement.mappings;
              let mapIndex = index">
              <mat-list-item *ngIf="mapIndex === 0" tabindex="0">
                <div *ngIf="map.sourceType === 0" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1) + ': ' +
                    'From data source column "' +
                    logicService.currentDataSourceTitleHeaderAndSampleData.header[map.index] +
                    '" to source vertex'
                  }}
                </div>
                <div *ngIf="map.sourceType === 1" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1) + ': ' +
                    'Widget "' +
                    logicService.currentLoadingStatement.mappingWidgets[map.index].funcName +
                    '\'s" output to source vertex'
                  }}
                </div>
                <div *ngIf="map.sourceType === 2" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1)  + ': ' +
                    'Unassigned mapping to source vertex'
                  }}
                </div>
                <div *ngIf="map.sourceType === 3" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1) + ': ' + 'Literal ' + map.index
                  }}
                </div>
              </mat-list-item>
              <mat-list-item *ngIf="mapIndex === 1" tabindex="0">
                <div *ngIf="map.sourceType === 0" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1)  + ': ' +
                    'From data source column "' +
                    logicService.currentDataSourceTitleHeaderAndSampleData.header[map.index] +
                    '" to target vertex'
                  }}
                </div>
                <div *ngIf="map.sourceType === 1" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1)  + ': ' +
                    'Widget "' +
                    logicService.currentLoadingStatement.mappingWidgets[map.index].funcName +
                    '\'s" output to target vertex'
                  }}
                </div>
                <div *ngIf="map.sourceType === 2" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1)  + ': ' +
                    'Unassigned mapping to target vertex'
                  }}
                </div>
                <div *ngIf="map.sourceType === 3" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1)  + ': ' + 'Literal ' + map.index
                  }}
                </div>
              </mat-list-item>
              <mat-list-item *ngIf="mapIndex > 1" tabindex="0">
                <div *ngIf="map.sourceType === 0" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1)  + ': ' +
                    'From data source column "' +
                    logicService.currentDataSourceTitleHeaderAndSampleData.header[map.index] +
                    '" to edge attribute "' +
                    logicService.graph.getEdge(logicService.currentLoadingStatement.edgeName).attributes[mapIndex - 2].name +
                    '"'
                  }}
                </div>
                <div *ngIf="map.sourceType === 1" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1)  + ': ' +
                    'Widget "' +
                    logicService.currentLoadingStatement.mappingWidgets[map.index].funcName +
                    '" to edge attribute "' +
                    logicService.graph.getEdge(logicService.currentLoadingStatement.edgeName).attributes[mapIndex - 2].name +
                    '"'
                  }}
                </div>
                <div *ngIf="map.sourceType === 2" matLine role="listitem">
                  {{
                    'Mapping ' + (mapIndex + 1)  + ': ' + 'Unassigned mapping'
                  }}
                </div>
                <div *ngIf="map.sourceType === 3" matLine role="listitem">
                  {{
                    'Mapping' + (mapIndex + 1)  + ': ' + 'Literal ' + map.index
                  }}
                </div>
              </mat-list-item>
            </ng-container>
          </mat-list>
          <hr />
          <!-- End of Mapping Information -->
        </div>

        <br />

        <!-- Mapping Widget Information -->
        <h3>{{ 'Mapping widget' | translate }}:</h3>
        <mat-list role="list" attr.aria-label="{{ 'Mapping widget' | translate }}" tabindex="0">
          <ng-container *ngFor="let mapWidget of logicService.currentLoadingStatement.mappingWidgets;">
            <mat-list-item tabindex="0">
              <div *ngIf="mapWidget.funcName" matSubHeader matLine role="listitem">{{ 'Function name: ' + mapWidget.funcName }}</div>
              <div matLine *ngFor="let param of mapWidget.params ; let paramIndex = index">
                <div *ngIf="param.sourceType === 0" matLine role="listitem">
                  {{
                    'Parameter ' + (paramIndex + 1) + ': ' +
                    'Input from data source column "' +
                    logicService.currentDataSourceTitleHeaderAndSampleData.header[param.index] +
                    '"'
                  }}
                </div>
                <div *ngIf="param.sourceType === 1" matLine role="listitem">
                  {{
                    'Parameter ' + (paramIndex + 1) + ': ' +
                    'Input from widget "' +
                    logicService.currentLoadingStatement.mappingWidgets[param.index].funcName +
                    '"'
                  }}
                </div>
                <div *ngIf="param.sourceType === 2" matLine role="listitem">
                  {{
                    'Parameter ' + (paramIndex + 1) + ': ' + 'Unassigned mapping'
                  }}
                </div>
                <div *ngIf="param.sourceType === 3" matLine role="listitem">
                  {{
                    'Parameter ' + (paramIndex + 1) + ': ' + 'Literal ' + param.index
                  }}
                </div>
              </div>
            </mat-list-item>
          </ng-container>
        </mat-list>
        <hr />
        <!-- End of Mapping Widget Information -->

        <h3 *ngIf="logicService.currentLoadingStatement.style.whereClauseJson">
          {{
            ('Where clause' | translate ) + ': ' + currentWhereClause
          }}
        </h3>
        <br />
      </div>
    </mat-dialog-content>

    <!-- Hint Content -->
    <mat-dialog-content *ngIf="selectedEvent === toolbarKeys.ShowHint">
      <!-- Hint -->
      <div fxLayoutAlign="center center" class="hint-container hint-text">
        <div class="hint-table" fxLayout="column" tabindex="0">
          <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
            <p class="hint-table-left">{{ 'Add data file' | translate }}</p>
            <p class="hint-table-right">
              <span>{{ 'Click' | translate}}</span>
              <mat-icon
                class="icon-in-instruction"
                attr.aria-label="{{ 'Add data file' | translate }}">
                note_add
              </mat-icon>
              <span>{{ 'in toolbar' | translate }}</span>
            </p>
          </div>
          <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
            <p class="hint-table-left">{{ 'Add data mapping' | translate }}</p>
            <p class="hint-table-right">
              <span>{{ 'Click' | translate}}</span>
              <mat-icon
                class="icon-in-instruction"
                attr.aria-label="{{ 'Add data mapping' | translate }}">
                shuffle
              </mat-icon>
              <span>{{ 'then click data source then click vertex or edge' | translate }}</span>
            </p>
          </div>
          <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
            <p class="hint-table-left">{{ 'Edit data mapping' | translate }}</p>
            <p class="hint-table-right">
              {{ 'Choose dashed link between data source and graph schema' | translate }}
            </p>
          </div>
          <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
            <p class="hint-table-left">{{ 'Map columns to attributes' | translate }}</p>
            <p class="hint-table-right">
              {{ 'Drag from data column, then drop on the vertex or edge attribute' | translate }}
            </p>
          </div>
          <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
            <p class="hint-table-left">{{ 'Delete data file or data mapping' | translate }}</p>
            <p class="hint-table-right">
              <span>{{ 'Choose data file or data mapping to delete then click' | translate }}</span>
              <mat-icon
                class="icon-in-instruction"
                attr.aria-label="{{ 'Delete data file or data mapping' | translate }}">
                delete
              </mat-icon>
              <span>{{'in toolbar' | translate }}</span>
            </p>
          </div>
          <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
            <p class="hint-table-left">{{ 'Publish changes' | translate }}</p>
            <p class="hint-table-right">
              <span>{{ 'Click' | translate }}</span>
              <mat-icon
                class="icon-in-instruction"
                attr.aria-label="{{ 'Publish changes' | translate }}">
                save
              </mat-icon>
              <span>{{ 'in toolbar' | translate }}</span>
            </p>
          </div>
        </div>
      </div>
      <!-- End of Hint -->
    </mat-dialog-content>
    <!-- End of Right Chart Visual Information Content -->

    <!-- Action Buttons -->
    <mat-dialog-actions
      [ngClass]="{
        'data-source-list-dialog-actions-container': (selectedEvent === toolbarKeys.AddDataFile) &&
          !isPickDataSourceTypeDialogOpen,
        'pop-up-dialog-actions-container': (selectedEvent !== toolbarKeys.AddDataFile) ||
          isPickDataSourceTypeDialogOpen}">
      <button
        *ngIf="(selectedEvent === toolbarKeys.AddDataFile) && !isPickDataSourceTypeDialogOpen"
        mat-button
        class="back-btn"
        attr.aria-label="{{ 'Go back to previous page' | translate }}"
        (click)="goToPreviousAddDataSourcePage()"
        [disabled]="loading | async">
        {{ 'BACK' | translate }}
      </button>
      <div fxLayout="row">
        <button
          mat-button
          class="cancel-btn"
          (click)="closePopup()"
          [disabled]="loading | async">
          {{ (showingVisInfo() ? 'CLOSE' : 'CANCEL') | translate }}
        </button>
        <div *ngIf="selectedEvent === toolbarKeys.AddDataFile">
          <button *ngIf="isDataPreviewDialogOpen && isTabularFormat"
            mat-button
            class="add-btn"
            color="primary"
            [disabled]="
            !canWriteLoadingJob ||
            (loading | async) ||
            !isSampleDataDefined ||
            (isSampleDataDefined && !isSampleDataPresent) ||
            !(parsingSucceed | async)"
            (click)="addDataFile()">
            {{ 'ADD' | translate }}
          </button>
          <button *ngIf="isDataPreviewDialogOpen && isJSONDataFormat"
            mat-button
            class="add-btn"
            color="primary"
            [disabled]="
            !canWriteLoadingJob ||
            (loading | async) ||
            !isSampleDataDefined ||
            (isSampleDataDefined && !isSampleDataPresent) ||
            !(parsingSucceed | async)"
            (click)="showJSONSchema()">
            {{ 'NEXT' | translate }}
          </button>
          <button *ngIf="isEditSchemaDialogOpen"
            mat-button
            class="add-btn"
            color="primary"
            [disabled]="
            !canWriteLoadingJob ||
            (loading | async) ||
            !selectedFile ||
            !(parsingSucceed | async)"
            (click)="addDataFile()">
            {{ 'ADD' | translate }}
          </button>
          <button *ngIf="isInputDataSourceInfoDialogOpen &&
            (selectedDataSourceType === dataSourceType.S3 ||
            selectedDataSourceType === dataSourceType.AzureBlobStorage)"
            mat-button
            class="add-btn"
            color="primary"
            [disabled]="credentialForm.invalid"
            (click)="confirmAddNewDataSource()">
            {{ ('ADD') | translate }}
          </button>
          <button *ngIf="isInputDataSourceInfoDialogOpen && selectedDataSourceType === dataSourceType.GoogleCloudStorage"
            mat-button
            class="add-btn"
            color="primary"
            [disabled]="
            credentialForm.invalid ||
            !gcsServiceAccountKeyFile ||
            !gcsServiceAccountKeyFormatIsValid"
            (click)="confirmAddNewDataSource()">
            {{ ('ADD') | translate }}
          </button>
          <button *ngIf="isInputDataSourceInfoDialogOpen && selectedDataSourceType === dataSourceType.File"
            mat-button
            class="add-btn"
            color="primary"
            [disabled]="(fileList$ | async).length === 0"
            (click)="showDataFormatDialog()">
            {{ 'NEXT' | translate }}
          </button>
        </div>
      </div>
      <button *ngIf="selectedEvent === toolbarKeys.AddTokenFunction"
        mat-button
        class="add-btn"
        color="primary"
        [disabled]="disableAddToken"
        (click)="addTokenFunction()">
        {{ 'ADD' | translate }}
      </button>
      <button *ngIf="selectedEvent === 'addConstMapping'"
        mat-button
        class="add-btn"
        color="primary"
        (click)="addConstMapping()">
        {{ 'ADD' | translate }}
      </button>
      <button *ngIf="selectedEvent === toolbarKeys.WhereClause"
        mat-button
        class="add-btn"
        color="primary"
        [disabled]="disableAddWhereClause()"
        (click)="addWhereClause()">
        {{ 'ADD' | translate }}
      </button>
      <button
        mat-button
        class="add-btn"
        *ngIf="selectedEvent === toolbarKeys.MapWidget"
        color="primary"
        [disabled]="mapWidgetValueType === 'UDT' && !widgetUDTName"
        (click)="addMapWidget()">
        {{ 'ADD' | translate }}
      </button>
      <button *ngIf="selectedEvent === toolbarKeys.UDTWidget"
        mat-button
        class="add-btn"
        color="primary"
        [disabled]="!widgetUDTName"
        (click)="addTupleWidget()">
        {{ 'ADD' | translate }}
      </button>
    </mat-dialog-actions>
    <!-- End of Action Buttons -->
  </div>
</ng-template>

<ng-template #previewFilesWindow>
  <h2 mat-dialog-title class="mat-input-error">{{ 'Warning' | translate }}</h2>
  <mat-dialog-content>
    <div class="overwrite-file-container">
      <div *ngIf="existingFiles.length > 0">
        {{ 'Following file(s) already exist(s) on server:' | translate }}
        <p class="mat-input-warn">{{ 'Please delete exising files before re-upload' | translate }}</p>
      </div>
      <div *ngFor="let fileName of existingFiles" class="mat-input-warn tab">
        <div>- {{ fileName }}</div>
      </div>
      <div class="new-upload-file-list">
        <mat-hint *ngIf="newFiles.length === 0">
          {{ 'No valid new files to upload, please re-select.' |  translate }}
        </mat-hint>
        <p *ngIf="newFiles.length > 0">
          {{ 'Following are new file(s) to be uploaded:' | translate }}
        </p>
        <div *ngFor="let file of newFiles" class="tab">
          <mat-checkbox checked="true" disabled="true"></mat-checkbox>
          <span class="file-name-label">{{ file.name }}</span>
        </div>
      </div>
      <div class="new-upload-file-list">
        <mat-hint *ngIf="unsupportedTypeFiles.length > 0">
          {{ 'Unsupported type file(s) will be skipped:' | translate }}
        </mat-hint>
        <div *ngFor="let fileName of unsupportedTypeFiles" class="tab">
          <mat-hint>{{ fileName }}</mat-hint>
        </div>
      </div>
      <div class="new-upload-file-list">
        <mat-hint *ngIf="filesLessThan1Byte.length > 0">
          {{ 'File(s) less than 1 byte will be skipped:' | translate }}
        </mat-hint>
        <div *ngFor="let fileName of filesLessThan1Byte" class="tab">
          <mat-hint>{{ fileName }}</mat-hint>
        </div>
      </div>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button
      mat-button
      class="cancel-btn"
      [mat-dialog-close]="0">
      {{ 'CANCEL' | translate }}
    </button>
    <button
      mat-button
      class="cancel-btn"
      color="primary"
      [disabled]="disableUploadInPreviewPopup()"
      [mat-dialog-close]="1">
      {{ 'UPLOAD' | translate }}
    </button>
  </mat-dialog-actions>
</ng-template>

<ng-template #updateLoadingJobsProgressWindow>
  <mat-dialog-content fxLayout="column" fxLayoutGap="5px">
    <div fxFlex="48px" class="hint-text">
      <span fxFlex fxLayoutAlign="start">
        {{ 'Installing data mapping.' | translate }}<br/>
        {{ 'Takes about' | translate }} {{ publishDataMappingTime }} {{ 'sec(s)' | translate }} ...
      </span>
    </div>
    <mat-progress-bar mode="determinate" [value]="updateLoadingJobsProgress | async"></mat-progress-bar>
  </mat-dialog-content>
</ng-template>

<ng-template #loadingWindow>
  <mat-dialog-content fxLayout="column" fxLayoutGap="5px">
    <div fxLayout="row" fxLayoutAlign="space-between">
      <p><span>{{ 'Uploading:' | translate }}</span><br/>
        <span
          *ngFor="let file of filesToUpload, let isLast = last;"
          [ngClass]="{'hint-text': !file.uploaded}">
          - {{ file.fileName }}<br *ngIf="!isLast"/>
          <span *ngIf="file.error" class="mat-input-error">
            <br *ngIf="isLast"/>
            <mat-icon attr.aria-label="{{ 'Warning' | translate }}" class="warning-icon">
              priority_high
            </mat-icon>
            {{ file.error }}
            <br *ngIf="!isLast"/>
          </span>
        </span>
      </p>
      <div fxFlex="24px">
        <mat-icon *ngIf="(uploadStatus | async) === 'error'" attr.aria-label="{{ 'Error' | translate }}" class="mat-input-error">
          error_outline
        </mat-icon>
      </div>
    </div>
    <mat-progress-bar mode="determinate" [value]="uploadProgress | async"></mat-progress-bar>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button
      mat-button
      class="cancel-btn"
      color="warn"
      (click)="cancelUpload()"
      [disabled]="(uploadStatus | async) !== 'uploadStart' && (uploadStatus | async) !== 'progress'">
      {{ 'CANCEL' | translate }}
    </button>
    <button
      mat-button
      class="cancel-btn"
      color="primary"
      (click)="closeUploading()"
      [disabled]="(uploadStatus | async) !== 'error'">
      {{ 'CLOSE' | translate }}
    </button>
  </mat-dialog-actions>
</ng-template>
