import {
  AfterViewInit, Component, ElementRef, OnDestroy,
  OnInit, TemplateRef, ViewChild, HostListener,
  ChangeDetectorRef
} from '@angular/core';
import { NestedTreeControl } from '@angular/cdk/tree';
import { Breakpoints, BreakpointObserver } from '@angular/cdk/layout';
import { HttpErrorResponse } from '@angular/common/http';
import { FormGroup } from '@angular/forms';
import { MatDialog, MatDialogConfig, MatDialogRef, MatDialogState } from '@angular/material/dialog';
import { MatIconRegistry } from '@angular/material/icon';
import { MatMenuTrigger } from '@angular/material/menu';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatTreeNestedDataSource } from '@angular/material/tree';
import { Edge, EdgeView, Graph as X6Graph, Node, NodeView } from '@antv/x6';
import {
  GraphChartSetting,
} from '@tigergraph/tools-models/gvis/insights';
import { Chart, ChartConfiguration } from 'chart.js';
import { StatisticsDatabase, StatisticsDataSource } from './shared';
import {
  GraphChartDataNode,
  GraphChartDataLink,
  createGraphChartDataNode,
  createGraphChartDataLink,
} from '@tigergraph/tools-models/gvis/insights';
import { cloneDeep, filter, findIndex, isEqual } from 'lodash';
import {
  Observable, Subject, BehaviorSubject,
  concat, forkJoin, interval, of, timer
} from 'rxjs';
import {
  catchError, concatMap, distinctUntilChanged,
  finalize, map, mapTo, takeUntil,
  tap
} from 'rxjs/operators';

import { DialogData, DialogComponent } from '@app/core/components/dialog';
import { AuthService, FileUploadService, Logger, MessageBus } from '@app/core/services';
import { AdjustHorizontalModel } from '@app/shared/components/adjust';
import { ExpressionFormConfig } from '@app/shared/components/expression-form';
import { ToolbarConfig, ToolbarInteraction } from '@app/shared/components/toolbar-container';
import { SchemaGraphWrapperComponent } from '@app/shared/components/schema-graph-chart/schemaGraphWrapper';
import { ButtonBase } from '@tigergraph/tools-models/button';
import { HttpSourceErrorResponse } from '@tigergraph/tools-models/error';
import { BaseCondition, parseExprJson } from '@tigergraph/tools-models/expression';
import {
  builtinTokenFunctionNames, builtinTokenFunctionDocument,
  builtinTokenFunctionParamNumber, builtinTokenFunctionReturnType,
  combinedTokenFunctionList, DataSourceLogic, DBLoadingJobJson,
  GSQLLoadingJobJson, LoadingJobData,
  OneColumnMapping, OneColumnMappingDataSourceColumnSource,
  OneColumnMappingGraphEntityTarget, OneColumnMappingTargetType,
  OneColumnMappingMappingWidgetSource, OneColumnMappingMappingWidgetTarget,
  SourceType, TokenFunctionSignature,
  LoadToEdgeData, LoadToVertexData, GSQLDataSourceLogic,
  Metadata,
} from '@tigergraph/tools-models/loading-job';
import { AppTheme } from '@tigergraph/tools-models/theme/app-theme.model';
import {
  DBGraphStyleJson, Graph, GSQLGraphJson, GSQLUdtJson,
  supportedMapKeyTypes, supportedContainerValueTypes
} from '@tigergraph/tools-models/topology';
import {
  dataSourceNodeType, dataSourceEdgeType,
  LoadingDesignerLogicService,
  LoadingJobVisService, virtualNodeType,
  StatusColorLight, StatusBackgroundColorLight, StatusBackgroundColorDark,
  TerminalState, VolatileState
} from '@app/shared/services';
import { GLOBAL_GRAPH_NAME, GSQLPrivilege } from '@tigergraph/tools-models/utils';
import { HelperFunctions } from '@app/shared/utils';
import type { GraphRef } from '@tigergraph/tools-ui/types/graph/type';

import {
  LoadingBuilderService, DataSourceTypeButton,
  DataParserForm, SampleData,
  ToolbarButtons, MappingToolbarButtons, ToolbarKeys, TableSampleData, JSONSampleData
} from './loading-builder.service';
import {
  LoadingExecutorService, GraphStatistics,
  LoadingProgress
} from './loading-executor.service';
import {
  LoadingMappingVisService,
  SelectedMappingsAndMappingWidgets, SampleDatabase, SampleDataSource
} from './shared';
import {
  GSQLLoadingJobChange,
  GSQLLoadingJobLogic,
} from '@tigergraph/tools-models/loading-job/gsql-loading-job-logic.model';

import { DataMappingConifg, VisualDataMappingLogic } from './vis-data-mapping-logic.model';
import { DataSource, DataSourceType } from '@tigergraph/tools-models/loading-job/data-source.interface';
import {
  DataSet, DataFormat,
  FileFormat, FileInfo, TabularParser, JSONParser, JSONDataFieldSchema, ParsingOptions, JSONDataType
} from '@tigergraph/tools-models/loading-job/data-set.interface';
import { DataSetLogic, INVALID_JSON_SAMPLE_DATA_IDENTIFIER } from '@tigergraph/tools-models/loading-job/data-set-logic.model';
import {
  LoadingJobChange, LoadingJobChangeLogic, LoadingJobChangeStatus,
} from '@tigergraph/tools-models/loading-job/loading-job-change-logic';
import { DataFilterLogic } from '@tigergraph/tools-models/loading-job/data-filter-logic.model';
import { EditorConfiguration } from 'codemirror';
import { VisJsonSchemaEditorComponent } from './shared/vis-json-schema-editor/vis-json-schema-editor.component';
import { PostMessageService } from '@app/shared/services/post-message.service';

import { ExternalGraph, ExternalNode, ExternalLink } from '@tigergraph/tools-models/gvis/insights';
import { GraphEvents } from '@tigergraph/tools-ui/types/graph/type';
import { GRAPH_ICON } from '@tigergraph/tools-ui/esm/graph/popper/styledGraphIcon';
import { Shortcut } from '@tigergraph/app-ui-lib/shortcuts-menu';

const defaultPopupWidth = '648px'; // 600px for width plus the left and right padding which is 24px each.
const default2ndPopupWidth = '600px';
const dataPopupWidth = 'auto';

// Mock X6 port double click event with 500ms click gap.
const DOUBLE_CLICK_FREQUENCY = 500;

/**
 * Different multi-view split status:
 * - Left: left panel full size
 * - Split: left panel and right panel split
 * - Right: right panel full size
 *
 * @enum {number}
 */
const enum SplitViewStatus {
  Left,
  Split,
  Right
}

/**
 * Different Add Data source pages:
 * - PickDataSourceType: show page which lists the data source types.
 * - DataSourceList: show page which list of existing projects.
 * - InputDataSourceInfo: show page which ask for data source info input.
 * - FilePreview: show page which previews input uri files.
 *
 * @enum {number}
 */
export const enum AddDataFilePage {
  PickDataSourceType,
  DataSourceList,
  InputDataSourceInfo,
  DataPreview,
  EditSchema
}

interface FileDisplayInfo {
  fileName: string;
  uploaded?: boolean;
  error?: string;
}

/**
 * Object node used for mat-tree.
 *
 * @export
 * @class ObjectNode
 */
export class ObjectNode {
  name: string;
  type: string;
  objectPath: string;
  childNodes?: ObjectNode[];

  constructor(name: string) {
    this.name = name;
  }
}

interface HeaderColumn {
  id: string;
  property: string;
  headerText: string;
  editing: boolean;
}

const fileExtensionToFormatMap: [string, FileFormat][] = [
  ['.zip', FileFormat.ZIP],
  ['.tar.gz', FileFormat.GZIP],
  ['.tgz', FileFormat.GZIP],
  ['.tar', FileFormat.TAR],
];

const supportedArchiveFileExt = ['none', 'zip', 'gzip', 'tar'];

const supportedFileType = [
  'application/json',
  'text/csv',
  'text/tab-separated-values',
  'text/plain'
];

const supportedFileTypeExtension = [
  '.json',
  '.jsonl',
  '.csv',
  '.tsv',
  '.txt'
];

enum StatisticsTab {
  GraphStatistics = 0,
  LoadingStatistics = 1,
}

/**
 * Log stream for standard and error output.
 *
 * @interface LogStream
 */
interface LogStream {
  stdContent: string;
  errContent: string;
  stdStream: BehaviorSubject<string>;
  errStream: BehaviorSubject<string>;
}

/**
 * Component to render the loading builder interface.
 * Usage example:
 *   <app-loading-builder></app-loading-builder>
 *
 * @export
 * @class LoadingBuilderComponent
 * @implements {AfterViewInit}
 * @implements {OnDestroy}
 * @implements {OnInit}
 */
@Component({
  selector: 'app-loading-builder',
  templateUrl: './loading-builder.component.html',
  styleUrls: ['./loading-builder.component.scss'],
  providers: [
    LoadingJobVisService,
    LoadingBuilderService,
    LoadingExecutorService,
    LoadingMappingVisService,
    LoadingDesignerLogicService,
    VisualDataMappingLogic,
    VisJsonSchemaEditorComponent
  ]
})
export class LoadingBuilderComponent
  implements AfterViewInit, OnDestroy, OnInit {
  @ViewChild('updateLoadingJobsProgressWindow', { static: true })
  updateLoadingJobsProgressWindow: TemplateRef<any>;
  @ViewChild('popupWindow', { static: true }) popupWindow: TemplateRef<any>;
  @ViewChild('credentialPopupWindow', { static: true }) credentialPopupWindow: TemplateRef<any>;
  @ViewChild('loadingWindow', { static: true }) loadingWindow: TemplateRef<any>;
  @ViewChild('fileInput') fileInput: ElementRef;
  @ViewChild('dropArea') dropArea: ElementRef;
  @ViewChild('previewFilesWindow', { static: true }) previewFilesWindow: TemplateRef<any>;
  @ViewChild(MatMenuTrigger) menuTrigger: MatMenuTrigger;
  @ViewChild(VisJsonSchemaEditorComponent) jsonSchemaEditor: VisJsonSchemaEditorComponent;
  @ViewChild('schemaGraphContainer', { static: true }) schemaGraphContainer: SchemaGraphWrapperComponent;
  @ViewChild('graphStatisticalTrend', { static: true }) graphStatisticalTrendContainer: ElementRef;
  @ViewChild('graphStatisticalTrendReflow') graphStatisticalTrendReflowContainer: ElementRef;
  @ViewChild('loadingSpeedTrend') loadingSpeedTrendContainer: ElementRef;
  @ViewChild('dataLinesDistribution') dataLinesDistributionContainer: ElementRef;
  @ViewChild('adjustHorizontal') adjustHorizontal: ElementRef;

  toolbarConfig: ToolbarConfig;
  mappingToolbarConfig: ToolbarConfig;
  rightChartConfig: GraphChartSetting;
  exprFormConfig: ExpressionFormConfig;

  adjustHorizontalModel: AdjustHorizontalModel;
  showLeft = true;
  showRight = true;

  loading = new BehaviorSubject<boolean>(true);
  showLeftHint = new BehaviorSubject<boolean>(false);
  showRightHint = new BehaviorSubject<boolean>(true);

  parsingSucceed = new BehaviorSubject<boolean>(false);
  jsonEditorConfig: EditorConfiguration;
  jsonText = [];

  currentAddDataFilePage = AddDataFilePage.PickDataSourceType;

  dataSourceTypeButtons: DataSourceTypeButton[];
  selectedDataSourceType = DataSourceType.None;
  selectedDataFormat = DataFormat.None;
  numberOfSourceTypeShown = 3;

  dataParserForm: DataParserForm;
  filesToUpload: FileDisplayInfo[];
  unsupportedTypeFiles: string[] = [];
  existingFiles: string[] = [];
  filesLessThan1Byte: string[] = [];
  newFiles: File[] = [];
  fileList$ = new BehaviorSubject<string[]>([]);

  dataSourceList: DataSource[] = [];
  gcsServiceAccountKeyFile: File;
  gcsServiceAccountKeyFileContent: string;
  gcsServiceAccountKeyFormatIsValid: boolean;

  uploadStatus = new BehaviorSubject<string>('');
  uploadProgress = new BehaviorSubject<number>(0);
  inputConstString: string;

  selectedEvent: string;
  selectedFile: string;
  selectedDataSourceName: string;
  tabularParser: TabularParser;
  jsonParser: JSONParser;
  credentialForm: FormGroup;
  uriInputForm: FormGroup;

  nestedTreeControl = new NestedTreeControl<ObjectNode>((node: ObjectNode) => node.childNodes);
  s3DataSource = new MatTreeNestedDataSource<ObjectNode>();
  s3ObjectData: ObjectNode[] = [];

  tabularSampleData: TableSampleData;
  jsonSampleData: JSONSampleData;
  sampleDataSource: SampleDataSource;
  sampleColumnDefs: HeaderColumn[] = [];
  sampleColumnIds: string[] = [];
  newHeaderText = '';

  whereClauseDataSource: SampleDataSource;
  whereClauseColumnDefs: {
    id: string;
    property: string;
    headerText: string;
  }[] = [];
  whereClauseColumnIds: string[] = [];

  combinedTokenFunctionList = new BehaviorSubject<TokenFunctionSignature[]>([]);
  selectedTokenFunction: TokenFunctionSignature;

  updateLoadingJobsProgress = new BehaviorSubject<number>(0);

  authorizationWarning = new BehaviorSubject<string>(undefined);
  unsavedWarning = new BehaviorSubject<string>(undefined);

  formatFilter = new Map<string, string[]>([
    [DataSourceType.File, ['none']],
    [DataSourceType.S3, supportedArchiveFileExt],
    [DataSourceType.GoogleCloudStorage, supportedArchiveFileExt],
    [DataSourceType.AzureBlobStorage, supportedArchiveFileExt],
    [DataSourceType.Snowflake, ['none']]
  ]);

  udtList: GSQLUdtJson[] = [];

  supportedMapKeyTypes = supportedMapKeyTypes;
  supportedContainerValueTypes = supportedContainerValueTypes;

  mapWidgetKeyType = supportedMapKeyTypes[0];
  mapWidgetValueType = supportedContainerValueTypes[0];
  widgetUDTName = '';

  reflow = false;
  publishDataMappingTime: number;
  graphStatisticalTrendAriaLabel = 'Graph statistics trend line chart';

  currentWhereClause: string;

  dropdownDataTypes: string[] = [
    JSONDataType.Null, JSONDataType.Object, JSONDataType.String, JSONDataType.Number,
    JSONDataType.Integer, JSONDataType.Boolean, JSONDataType.Array
  ];

  private graphName: string;
  canWriteLoadingJob: boolean;
  private canWriteSchema: boolean;

  private ngUnsubscribe: Subject<any> = new Subject<any>();
  private progressUnsubscribe: Subject<any> = new Subject<any>();

  private popupConfig: MatDialogConfig;
  private credentialPopupConfig: MatDialogConfig;
  private loadingConfig: MatDialogConfig;
  private previewFilesWindowConfig: MatDialogConfig;
  private widgetWindowConfig: MatDialogConfig;
  private popupWindowRef: MatDialogRef<TemplateRef<any>>;
  private credentialPopupWindowRef: MatDialogRef<TemplateRef<any>>;
  private loadingWindowRef: MatDialogRef<TemplateRef<any>>;

  private updateLoadingJobsProgressConfig: MatDialogConfig;
  private updateLoadingJobsProgressWindowRef: MatDialogRef<TemplateRef<any>>;

  private leftChartGraphRef: React.MutableRefObject<GraphRef>;
  private rightChart: X6Graph;
  private userUploadedIcons: string[];
  private portClickCounter = 0;

  private isLoading = true;

  private fileList: string[] = [];
  private fileParsingDict: Map<string, TabularParser | JSONParser>
    = new Map<string, TabularParser | JSONParser>();
  private hasErrorWhenUpload = false;
  private uploadFileList: File[];
  private fileInfoMap = new Map<string, FileInfo>();
  private metadata: Metadata;

  private sampleDatabase = new SampleDatabase();
  private sampleTableId = 0;

  private standardJSONSchema: any;
  private jsonSchema: JSONDataFieldSchema[];

  private whereClauseDatabase = new SampleDatabase();
  private whereClauseTableId = 0;

  private isAddingDataSource = false;
  private isAddingTokenFunction = false;
  private isDataMapping = false;
  private isAddingWhereClause = false;
  private selectedElement: GraphChartDataNode | GraphChartDataLink;
  private dataVertex: GraphChartDataNode;

  private toolbarDisabledStatus: boolean[] = [];

  private literalTarget: OneColumnMappingGraphEntityTarget | OneColumnMappingMappingWidgetTarget;

  private whereClauseModel: BaseCondition;
  private whereClauseSampleData: TableSampleData;

  // Always edit the mapping
  isLoadingMapping = false;
  selectedMappingsAndMappingWidgets: SelectedMappingsAndMappingWidgets;
  private mappingSource:
    | OneColumnMappingDataSourceColumnSource
    | OneColumnMappingMappingWidgetSource;
  private mappingTarget:
    | OneColumnMappingGraphEntityTarget
    | OneColumnMappingMappingWidgetTarget;

  private isMovingLeft = false;

  private savedLoadingJobs: GSQLLoadingJobJson[] = [];
  private savedGraphStyle: DBGraphStyleJson;
  private savedLoadingJobsStyle: DBLoadingJobJson[] = [];

  private currentTheme = 'dark';

  schema: Graph;
  loadingJobGraph: ExternalGraph;
  forceUpdateLoadingStatus: boolean;
  renderLeftChartTimer: NodeJS.Timer;
  forceUpdateLoadingStatusTimer: number;

  shortcuts: Shortcut[];
  graphEvents: GraphEvents = {
    onClick: (item: ExternalNode | ExternalLink | undefined) => {
      this.isLoadingMapping = false;
      if (item) {
        // Single click a vertex
        if ('id' in item) {
          const dataNode = createGraphChartDataNode(item);
          if (this.isDataMapping) {
            this.handleLeftGraphChartPointer(dataNode);
          } else {
            this.updateStatisticsPanel(dataNode);
          }
        // Single click an edge
        } else if ('source' in item) {
          const dataLink = createGraphChartDataLink(item);
          if (!this.isDataMapping) {
            this.updateStatisticsPanel(dataLink);
          }
          this.handleLeftGraphChartPointer(dataLink);
        }
      } else {
        this.updateStatisticsPanel(undefined);
      }
    },
    onPositionChange: () => {
      this.isMovingLeft = true;
      this.handleLeftGraphChartPointer();
    },
    onCreateLink: (source: ExternalNode, target: ExternalNode | ExternalLink) => {
      this.isDataMapping = true;
      this.toggleButtonFocus(ToolbarKeys.AddDataMapping);
      this.dataVertex = createGraphChartDataNode(source);
      let dataTarget = null;
      if ('id' in target) {
        dataTarget = createGraphChartDataNode(target);
      } else if ('source' in target) {
        dataTarget = createGraphChartDataLink(target);
      }
      this.handleLeftGraphChartPointer(dataTarget);
      this.isDataMapping = false;
    },
    showEdgeHandler: (node: ExternalNode) => {
      return dataSourceNodeType.includes(node.type) &&
        !this.isDataMapping &&
        !this.initialLoadingProgress;
    },
    onSelect: () => {
      this.selectedLoadingJobs = filter(
        this.leftChartGraphRef?.current?.selectedNodes().map(n => createGraphChartDataNode(n)),
        (element: any) => element && 'jobName' in element.others
      );
      this.setToolbarMode();
    },
    onMouseOver: (item: ExternalNode | ExternalLink) => {
      if (
        !this.enableWriteDataSource() ||
        this.initialLoadingProgress ||
        this.isDataMapping
      ) {
        return;
      }
      if ('id' in item && dataSourceNodeType.includes(item.type)) {
        this.leftChartGraphRef.current?.displayNodeIcon(item, GRAPH_ICON.delete);
      } else if ('source' in item && item.type.includes(dataSourceEdgeType)) {
        this.leftChartGraphRef.current?.displayLinkIcon(item, GRAPH_ICON.delete);
      }
    },
    onMouseOut: (item: ExternalNode | ExternalLink) => {
      if ('id' in item && dataSourceNodeType.includes(item.type)) {
        this.leftChartGraphRef.current?.removeNodeIcon(item);
      } else if ('source' in item && item.type.includes(dataSourceEdgeType)) {
        this.leftChartGraphRef.current?.removeLinkIcon(item);
      }
    },
    onDelete: (item: ExternalNode | ExternalLink) => {
      this.confirmDelete(item);
    }
  };

  // Start loading executor

  currentStatisticsTab = StatisticsTab.GraphStatistics;

  graphStatisticsDataSource: StatisticsDataSource;
  loadingProgressDataSource: StatisticsDataSource;

  currentLoadingProgress: LoadingProgress;

  private graphStatisticsDatabase = new StatisticsDatabase();
  private loadingProgressDatabase = new StatisticsDatabase();

  private fileInfos = new Map<string, FileInfo>();

  private getLoadingProgressInProgress = false;
  private initialLoadingProgress = true;
  private loadingProgressTimestamp = 0;

  private checkGraphStatisticsInterval = 10000;
  private graphStatistics: GraphStatistics;
  private disableStartLoading = false;
  private disablePauseLoading = true;
  private disableStopLoading = true;

  private checkLoadingProgressInterval = 5000;
  private currentLoadingJob: string;

  private graphStatisticalTrendLineChart: Chart;
  private graphStatisticalTrendLineChartConfig: ChartConfiguration;

  private loadingSpeedChart: Chart;
  private loadingSpeedChartConfig: ChartConfiguration;

  private dataLinesChart: Chart;
  private dataLinesChartConfig: ChartConfiguration;

  private selectedLoadingJobs: GraphChartDataNode[] = [];

  // Loading progress map.
  private loadingProgressMap = new Map<string, {
    timestamp: number,
    progress: LoadingProgress
  }[]>();

  private logs = new Map<string, LogStream>();

  warning = new BehaviorSubject<string>(undefined);

  showGraphStatsReflow = false;
  showSingleLoadingStatsReflow = false;

  // End loading executor

  constructor(
    private breakpointObserver: BreakpointObserver,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private authService: AuthService,
    private fileUploadService: FileUploadService,
    private bus: MessageBus,
    private logicService: LoadingDesignerLogicService,
    private leftVisService: LoadingJobVisService,
    private rightVisService: LoadingMappingVisService,
    private loadingBuilderService: LoadingBuilderService,
    private loadingExecutorService: LoadingExecutorService,
    private matIconRegistry: MatIconRegistry,
    private logger: Logger,
    private postMessageService: PostMessageService,
    private cdr: ChangeDetectorRef,
  ) {
    this.popupConfig = {
      width: defaultPopupWidth,
      panelClass: 'popup-dialog-panel',
      autoFocus: false
    };
    this.credentialPopupConfig = {
      width: default2ndPopupWidth,
      minHeight: '250px',
      panelClass: 'credential-dialog-panel'
    };
    this.loadingConfig = {
      disableClose: true,
      width: defaultPopupWidth
    };
    this.updateLoadingJobsProgressConfig = {
      disableClose: true,
      width: defaultPopupWidth
    };
    this.previewFilesWindowConfig = {
      width: default2ndPopupWidth
    };
    this.widgetWindowConfig = {
      width: 'auto',
      autoFocus: false
    };
    this.jsonEditorConfig = {
      cursorHeight: 0,
      mode: 'application/json',
      readOnly: true,
      tabindex: -1,
      autofocus: false
    };

    this.adjustHorizontalModel = {
      leftDefaultWidth: 50,
      leftDefaultWidthUnit: '%',
      leftMinWidthPx: 160,
      rightMinWidthPx: 160,
      from: 'LoadingBuilder'
    };

    // Init graph statistics.
    this.graphStatistics = {
      totalVertexNumber: 0,
      totalEdgeNumber: 0,
      vertexNumbers: [],
      edgeNumbers: []
    };

    // Init loading statistics.
    this.currentLoadingProgress = {
      source: 'No source selected.',
      status: 'N/A'
    };

    this.toolbarConfig = this.loadingBuilderService.getToolbarConfig();
    this.mappingToolbarConfig = this.loadingBuilderService.getMappingToolbarConfig();
    this.shortcuts = this.loadingBuilderService.getShortcuts();

    this.exprFormConfig = this.loadingBuilderService.getExpressionFormConfig();

    this.dataSourceTypeButtons = this.loadingBuilderService.getDataSourceTypeButtons();
    this.dataParserForm = this.loadingBuilderService.getDataParserForm();

    this.savedGraphStyle = new Graph().getGraphStyle().dumpToDBJson();

    this.graphStatisticalTrendLineChartConfig = this.loadingExecutorService.getGraphStatisticsLineChartConfig();
    this.loadingSpeedChartConfig = this.loadingExecutorService.getLoadingStatisticsLineChartConfig();
    this.dataLinesChartConfig = this.loadingExecutorService.getDataLinesDistributionPieChartConfig();

    this.attachSubjectCleanUp();
    this.initializeDataParsers();
    this.initializeDataMapping();
    this.initializeLoadingMapping();
    this.adjustElementAppearancesBaseOnUserPrivilege();
    this.adjustElementAppearancesBaseOnUserPrivilegeOfLoadingExecutor();
  }

  ngOnInit() {
    this.canWriteLoadingJob = this.authService.hasPrivilege(GSQLPrivilege.WriteLoadingJob);
    this.canWriteSchema = this.authService.hasPrivilege(GSQLPrivilege.WriteSchema);
    this.sampleDataSource = new SampleDataSource(this.sampleDatabase);
    this.whereClauseDataSource = new SampleDataSource(this.whereClauseDatabase);
    this.fileUploadService.init('loading_data');
    this.handleFileUploadResponse();
    this.initializeTokenFunction();
    this.getFileList();
    this.adjustElementAppearancesBaseOnUserPrivilege();
    this.adjustElementAppearancesBaseOnUserPrivilegeOfLoadingExecutor();

    this.breakpointObserver.observe([Breakpoints.Large, Breakpoints.XLarge])
      .pipe(
        takeUntil(this.ngUnsubscribe),
        distinctUntilChanged()
      )
      .subscribe(state => {
        this.reflow = !state.matches;
        switch (this.selectedDataSourceType) {
          case DataSourceType.File:
          case DataSourceType.S3:
            // Hide the sample data and parser when reflow is triggered.
            break;
          default:
            // TODO: Support more data source types.
            break;
        }
      });
    // start loading executor
    this.graphStatisticsDataSource = new StatisticsDataSource(this.graphStatisticsDatabase);
    this.loadingProgressDataSource = new StatisticsDataSource(this.loadingProgressDatabase);
    this.getFileInfo();
    this.graphName = this.authService.getCurrentGraph();
    this.checkGraphStatisticsInterval = this.authService.getGraphStatCheckIntervalMS();

    this.bus.to<boolean>('HasUnsavedChanges').next(false);
    // end loading executor
    this.loadingBuilderService.logPageView();
    // Avoid ExpressionChangedAfterItHasBeenCheckedError
    this.schema = this.logicService.graph.clone();
    this.loadingJobGraph = this.leftVisService.loadingJobGraph;
  }

  ngAfterViewInit() {
    // Give vis services access to chart instances.
    this.leftVisService.graphRef = this.leftChartGraphRef = this.schemaGraphContainer.graphRef;
    this.rightChart = this.rightVisService.initGraphChart();

    // Expose graphchart instance to make it testable in e2e test.
    (<any>window).globalDataMappingGraphRef = this.leftChartGraphRef;
    (<any>window).globalDataMappingGraphEvents = this.graphEvents;
    (<any>window).oneDataMappingGraphChart = this.rightChart;
    (<any>window).loadingExecutorVisService = this.leftVisService;

    /**
     * Since server call happens to late in the life cycle check, we need to wait one tick
     * to avoid unidirectional-data-flow-violation error.
     * This technique is discussed here https://angular.io/guide/component-interaction#parent-calls-an-viewchild.
     */
    timer()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.getSchemaAndLoadingJobs();
        this.bus.from<boolean>('SwitchGraph')
          .pipe(takeUntil(this.ngUnsubscribe))
          .subscribe(() => {
            this.switchGraph();
            this.bus.resetChannel('SwitchGraph');
          });

        this.bus.from<boolean>('AppReflow')
          .pipe(
            takeUntil(this.ngUnsubscribe),
            distinctUntilChanged()
          )
          .subscribe(shouldReflow => {
            this.reflow = shouldReflow;
            if (!shouldReflow) {
              this.showGraphStatsReflow = false;
              this.showSingleLoadingStatsReflow = false;
            }
            this.initialGraphStatisticalTrendLineChart();
            this.updateChartConfigs();
          });
      });

    this.initialGraphStatisticalTrendLineChart();

    this.loadingSpeedChart = new Chart(this.loadingSpeedTrendContainer.nativeElement, this.loadingSpeedChartConfig);

    this.dataLinesChart = new Chart(this.dataLinesDistributionContainer.nativeElement, this.dataLinesChartConfig);

    // Init loading progress
    timer()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => this.initLoadingProgress());

    // Start getting graph statistics
    this.getGraphStatistics();

    // Start getting loading progress.
    interval(this.checkLoadingProgressInterval)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => this.getLoadingProgress(true));

    // Change line chart config when switching theme
    this.updateChartConfigs();

    this.handleRightChartEvent();

    // Change graph chart config when switching theme
    this.updateChartConfigs();
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
    (<any>window).globalDataMappingGraphRef.current = null;
    (<any>window).globalDataMappingGraphRef = null;
    (<any>window).globalDataMappingGraphEvents = null;
    (<any>window).oneDataMappingGraphChart = null;
    (<any>window).loadingExecutorVisService = null;
    this.leftChartGraphRef.current = null;
    this.leftChartGraphRef = null;
    this.leftVisService.graphRef.current = null;
    this.leftVisService.graphRef = null;
    this.schemaGraphContainer.graphRef = null;
  }

  get toolbarKeys(): typeof ToolbarKeys {
    return ToolbarKeys;
  }

  get dataSourceType(): typeof DataSourceType {
    return DataSourceType;
  }

  get canEdit(): boolean {
    return this.canWriteSchema && this.canWriteLoadingJob;
  }

  private isGlobalDesigner(): boolean {
    return this.authService.getUserRoles().includes('globaldesigner');
  }

  /**
   * Disable creating new data source if user does not have write data source privilege.
   *
   * Special case globaldesigner:
   * Since globaldesigner can only create/drop/set local data source (not global ones),
   * write data source prililege is not added to globaldesigner's permission list but will
   * do additional permission check during operation in GSQL side.
   * However in GraphStudio, user can only add new data source under a local graph, which
   * means they can only create local data source. So we will enable add data source button
   * for globaldesigner.
   *
   * @returns {boolean}
   * @memberof LoadingBuilderComponent
   */
  enableWriteDataSource(): boolean {
    return this.authService.hasPrivilege(GSQLPrivilege.WriteDataSource) || this.isGlobalDesigner();
  }

  /**
   * Adjust elements' appearance based on current user's privilege.
   *
   * @memberof LoadingBuilderComponent
   */
  adjustElementAppearancesBaseOnUserPrivilege() {
    if (!this.canWriteLoadingJob) {
      // Disable all buttons if user cannot create loading jobs.
      this.toolbarConfig.top.forEach((button, i) => {
        if (i > ToolbarButtons.Separator1 && i < ToolbarButtons.Separator2) {
          button.disabled = true;
        }
      });
      // Show hint to user
      this.authorizationWarning.next(`You don't have privilege to modify data mapping for ` +
        `${this.authService.getCurrentGraph()}.`);

      // Enable add data source button if user can write data source.
      if (this.enableWriteDataSource()) {
        this.toolbarConfig.top[ToolbarButtons.AddDataFile].disabled = false;
      }
    } else {
      // Enable all buttons except loading related buttons.
      this.toolbarConfig.top.forEach((button, i) => {
        if (i < ToolbarButtons.StartLoading || i > ToolbarButtons.StopLoading) {
          button.disabled = false;
        }
      });
      this.mappingToolbarConfig.top[MappingToolbarButtons.AddTokenFunction].disabled = true;
      this.mappingToolbarConfig.top[MappingToolbarButtons.AutoMapping].disabled = true;
      this.mappingToolbarConfig.top[MappingToolbarButtons.WhereClause].disabled = true;
      this.mappingToolbarConfig.top[MappingToolbarButtons.More].disabled = true;
      // Clear hint for user
      this.authorizationWarning.next(undefined);
    }
  }


  /**
   * Adjust elements' appearance based on adjust horizontal window.
   *
   * @memberof LoadingBuilderComponent
   */
  onAdjustInteraction(event: boolean[]) {
    [this.showLeft, this.showRight] = event;
    if (this.showLeft && this.showRight) {
      this.mappingToolbarConfig.top[MappingToolbarButtons.Undo].show = false;
      this.mappingToolbarConfig.top[MappingToolbarButtons.Redo].show = false;
      this.mappingToolbarConfig.top[MappingToolbarButtons.Separator2].show = false;
    } else {
      this.mappingToolbarConfig.top[MappingToolbarButtons.Undo].show = true;
      this.mappingToolbarConfig.top[MappingToolbarButtons.Redo].show = true;
      this.mappingToolbarConfig.top[MappingToolbarButtons.Separator2].show = true;
    }
  }

  /**
   * Warn the user if there is unsaved data mapping changes.
   *
   * @private
   * @returns {boolean}
   * @memberof LoadingBuilderComponent
   */
  @HostListener('window:beforeunload')
  private onBeforeUnload(): boolean {
    if (this.postMessageService.isInIframe()) {
      return true;
    }
    return !this.canEdit || this.getLoadingJobChange().status === LoadingJobChangeStatus.NoChange;
  }

  @HostListener('window:keydown', ['$event'])
  keydownEvent(event: KeyboardEvent) {
    if (
      this.dialog.openDialogs.length > 0 ||
      !this.enableWriteDataSource() ||
      this.initialLoadingProgress ||
      this.isDataMapping ||
      this.popupWindowRef?.getState() === MatDialogState.OPEN ||
      this.credentialPopupWindowRef?.getState() === MatDialogState.OPEN ||
      this.loadingWindowRef?.getState() === MatDialogState.OPEN ||
      this.updateLoadingJobsProgressWindowRef?.getState() === MatDialogState.OPEN
    ) {
      return;
    }
    if ((event.metaKey || event.ctrlKey) && event.key.toLowerCase() === 's') {
      this.onInteraction({ key: ToolbarKeys.Publish });
      event.preventDefault();
    } else if ((event.metaKey || event.ctrlKey) && event.shiftKey && event.key.toLowerCase() === 'z') {
      this.onInteraction({ key: ToolbarKeys.Redo });
    } else if ((event.metaKey || event.ctrlKey) && event.key.toLowerCase() === 'z') {
      this.onInteraction({ key: ToolbarKeys.Undo });
    } else if ((event.metaKey || event.ctrlKey) && event.shiftKey && event.key.toLowerCase() === 'r') {
      this.leftChartGraphRef.current?.centerGraph();
      event.preventDefault();
    } else if (event.key.toLowerCase() === 'backspace' || event.key.toLowerCase() === 'delete') {
      this.onInteraction({ key: ToolbarKeys.Delete });
    }
  }

  /**
   * Determine if the current page can be deactivated.
   * Popup a warning to the user if there is pending job.
   *
   * @param {boolean} skipCheck
   * @returns {Observable<boolean>}
   * @memberof LoadingBuilderComponent
   */
  canDeactivate(skipCheck: boolean): Observable<boolean> {
    if (skipCheck) {
      return of(true);
    }

    if (!this.canEdit) {
      return of(true);
    }

    if (this.getLoadingJobChange().status === LoadingJobChangeStatus.NoChange) {
      return of(true);
    }

    const data: DialogData = {
      title: 'Warning',
      messages:
        ['You have unpublished changes for data mapping. ' +
          'Do you still want to leave?'],
      actions: [
        { label: 'STAY', value: 0 },
        { label: 'LEAVE', value: 1, color: 'warn' }
      ]
    };

    const dialogRef = this.dialog
      .open(DialogComponent, { data: data, autoFocus: false });

    dialogRef
      .keydownEvents()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(event => {
        if (event.key.toLowerCase() === 'enter') {
          dialogRef.close(1);
        }
      });

    return dialogRef
      .afterClosed()
      .pipe(
        takeUntil(this.ngUnsubscribe),
        map(value => {
          if (value === 1) {
            this.postMessageService.postCanDeactivate(true);
          }
          return value === 1;
        })
      );
  }

  /**
   * Switch graph triggers reset states and reload graph schema and loading jobs.
   *
   * @memberof LoadingBuilderComponent
   */
  switchGraph() {
    this.canWriteLoadingJob = this.authService.hasPrivilege(GSQLPrivilege.WriteLoadingJob);
    this.canWriteSchema = this.authService.hasPrivilege(GSQLPrivilege.WriteSchema);
    this.adjustElementAppearancesBaseOnUserPrivilege();
    this.handleRightChartEvent();

    // start loading executor
    this.adjustElementAppearancesBaseOnUserPrivilegeOfLoadingExecutor();

    this.graphStatistics = {
      totalVertexNumber: 0,
      totalEdgeNumber: 0,
      vertexNumbers: [],
      edgeNumbers: []
    };
    // Reset selected loading jobs
    this.selectedLoadingJobs = [];
    this.graphStatisticsDatabase.addGraphStatisticsData(this.graphStatistics);
    if (this.graphStatisticalTrendLineChart.data.datasets && this.graphStatisticalTrendLineChart.data.datasets[0]) {
      this.graphStatisticalTrendLineChart.data.datasets[0].data = [];
      this.graphStatisticalTrendLineChart.data.datasets[1].data = [];
    }
    this.graphStatisticalTrendLineChart.data.labels = [];
    this.graphStatisticalTrendLineChart.update();
    this.addGraphStatisticTrendChartData();

    this.initLoadingProgress();

    this.graphName = this.authService.getCurrentGraph();
    this.getSchemaAndLoadingJobs();
    // Reset tool bar status
    this.setToolbarMode();
    // end loading executor
  }

  /**
   * Handler for the user's interactions with the toolbar.
   *
   * @param {ToolbarInteraction} event
   * @memberof LoadingBuilderComponent
   */
  onInteraction(event: ToolbarInteraction) {
    switch (event.key) {
      case ToolbarKeys.Publish:
        if (this.logicService.semanticCheckAllLoadingJobs()) {
          this.handleSave();
          // Log save to analytics
          this.loadingBuilderService.logPublishDataMappingSuccess(this.getLoadingJobChange().status
          !== LoadingJobChangeStatus.NoChange);
        } else {
          // Log save failure to analytics
          this.loadingBuilderService.logPublishDataMappingFailure();
          this.handleError(
            'The data mapping design has some issues. Please follow ' +
            'the highlighted information on each mapping link from data files to graph schema ' +
            'to fix them before save.',
            'Please fix loading design issues first'
          );
        }
        break;
      case ToolbarKeys.AddDataFile:
        this.popupConfig.width = dataPopupWidth;
        this.openPopup(event.key);
        this.toggleAddDataSourceMode();
        this.loadingBuilderService.logFileAddStart();
        break;
      case ToolbarKeys.AddDataMapping:
        this.toggleDataMappingMode();
        this.loadingBuilderService.logLoadingStatementAddToggle(this.isDataMapping);
        break;
      // case 'addLoadingMapping':
      //   this.toggleLoadingMappingMode();
      //   break;
      case ToolbarKeys.StartOrResumeLoading: {
        this.handleStartAndResume();
        break;
      }
      case ToolbarKeys.PauseLoading: {
        this.handlePause();
        this.loadingExecutorService.logPauseLoading();
        break;
      }
      case ToolbarKeys.StopLoading: {
        this.handleStop();
        this.loadingExecutorService.logStopLoading();
        break;
      }
      case ToolbarKeys.AddTokenFunction:
        this.openPopup(event.key);
        this.toggleAddTokenFunctionMode();
        this.loadingBuilderService.logTokenFunctionAddStart();
        break;
      case ToolbarKeys.AutoMapping:
        this.logicService.autoMapping();
        this.renderLeftChart(true);
        this.renderRightChart();
        this.setWarningForUnsavedChanges();
        this.loadingBuilderService.logAutoMapping();
        break;
      case ToolbarKeys.WhereClause:
        this.openPopup(event.key);
        switch (this.logicService.currentDataFormat) {
          case DataFormat.CSV:
          case DataFormat.TSV:
            // TODO: Deprecate sample data in the future.
            this.whereClauseSampleData = {
              header: this.logicService.currentDataSourceTitleHeaderAndSampleData.header,
              data: this.logicService.currentDataSourceTitleHeaderAndSampleData.sampleData.slice(0, 1),
              json: false,
            };
            break;
          case DataFormat.JSON:
            this.whereClauseSampleData = {
              header: this.logicService.currentDataSchema.map(item => item.name),
              data: [[]],
              json: true,
            };
            break;
        }
        this.addWhereClauseSampleData(this.whereClauseSampleData);
        // TODO: Update expression form to show the path of JSON instead of index.
        this.exprFormConfig.columnList = this.whereClauseSampleData.header;
        this.whereClauseModel = <BaseCondition>parseExprJson(
          this.logicService.currentLoadingStatement.style.whereClauseJson
        );
        switch (this.logicService.currentDataFormat) {
          case DataFormat.JSON:
            DataFilterLogic.convertJSONExprModelForVisualExprForm(
              this.whereClauseModel,
              this.logicService.currentDataSchema
            );
            break;
        }
        this.toggleAddWhereClauseMode();
        this.loadingBuilderService.logDataFilterAddStart();
        break;
      case ToolbarKeys.MapWidget:
        this.openPopup(event.key, this.widgetWindowConfig);
        this.loadingBuilderService.logMapWidgetAddStart();
        break;
      case ToolbarKeys.UDTWidget: {
        this.openPopup(event.key, this.widgetWindowConfig);
        this.loadingBuilderService.logUDTWidgetAddStart();
        break;
      }
      case ToolbarKeys.Delete:
        this.handleDelete();
        break;
      case ToolbarKeys.Undo:
        this.initializeLoadingMapping();
        this.logicService.undo();
        this.renderLeftChart(true, true);
        this.renderRightChart();
        this.setWarningForUnsavedChanges();
        break;
      case ToolbarKeys.Redo:
        this.initializeLoadingMapping();
        this.logicService.redo();
        this.renderLeftChart(true, true);
        this.renderRightChart();
        this.setWarningForUnsavedChanges();
        break;
      case 'addConstMapping':
      case ToolbarKeys.ShowLeftVisInfo:
        this.openPopup(event.key);
        break;
      case ToolbarKeys.ShowRightVisInfo:
        if (!this.logicService.currentLoadingStatement) {
          this.handleError('Please select a loading statement to check its information.');
        } else {
          this.currentWhereClause =
            this.logicService.currentLoadingStatement.style.whereClauseJson ?
              parseExprJson(this.logicService.currentLoadingStatement.style.whereClauseJson).toString() :
              '';
          this.openPopup(event.key);
        }
        break;
      case ToolbarKeys.ShowHint:
        this.openPopup(event.key, this.widgetWindowConfig);
        break;
    }

    if (this.popupWindowRef) {
      this.popupWindowRef
        .afterClosed()
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe(() => this.toggleButtonStatus());
    }
  }

  private openPopup(event: string, popupConfig = this.popupConfig) {
    this.selectedEvent = event;
    this.popupWindowRef = this.dialog.open(
      this.popupWindow,
      popupConfig
    );

    this.popupWindowRef
      .keydownEvents()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(e => {
        if (e.key.toLowerCase() === 'enter') {
          if (this.selectedEvent === ToolbarKeys.AddDataFile) {
            if (this.isDataPreviewDialogOpen && this.isTabularFormat) {
              this.addDataFile();
            } else if (this.isDataPreviewDialogOpen && this.isJSONDataFormat) {
              this.showJSONSchema();
            } else if (this.isEditSchemaDialogOpen) {
              this.addDataFile();
            } else if (
              this.isInputDataSourceInfoDialogOpen &&
              (this.selectedDataSourceType === this.dataSourceType.S3 ||
              this.selectedDataSourceType === this.dataSourceType.GoogleCloudStorage ||
              this.selectedDataSourceType === this.dataSourceType.AzureBlobStorage ||
              this.selectedDataSourceType === this.dataSourceType.Snowflake)
            ) {
              this.confirmAddNewDataSource();
            }
          } else if (this.selectedEvent === ToolbarKeys.AddTokenFunction) {
            this.addConstMapping();
          } else if (this.selectedEvent === ToolbarKeys.WhereClause) {
            this.addWhereClause();
          } else if (this.selectedEvent === ToolbarKeys.MapWidget) {
            this.addMapWidget();
          } else if (this.selectedEvent === ToolbarKeys.UDTWidget) {
            this.addTupleWidget();
          }
        }
      });
  }

  /**
   * Goes to the previous data source dialog page.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private goToPreviousAddDataSourcePage() {
    if (this.isInputDataSourceInfoDialogOpen) {
      if (this.selectedDataSourceType === DataSourceType.GoogleCloudStorage) {
        this.gcsServiceAccountKeyFile = undefined;
        this.gcsServiceAccountKeyFileContent = '';
        this.gcsServiceAccountKeyFormatIsValid = false;
      }

      if (this.getDataSourceListLength() > 0) {
        this.currentAddDataFilePage -= 1;
      } else {
        this.selectedDataSourceType = DataSourceType.None;
        this.currentAddDataFilePage -= 2;
      }
    } else if (this.isDataSourceListDialogOpen) {
      this.selectedDataSourceType = DataSourceType.None;
      this.currentAddDataFilePage -= 1;
    } else if (this.isDataPreviewDialogOpen) {
      if (this.selectedDataSourceType === DataSourceType.File) {
        this.selectedDataSourceType = DataSourceType.None;
        this.selectedDataFormat = DataFormat.None;
        this.currentAddDataFilePage = AddDataFilePage.PickDataSourceType;
      } else {
        this.selectedDataSourceName = undefined;
        this.currentAddDataFilePage -= 2;
      }
      this.resetSelectedFile();
    } else if (this.isEditSchemaDialogOpen) {
      this.currentAddDataFilePage -= 1;
    }
  }

  /**
   * Checks if the current dialog shows the pick data source type page.
   *
   * @return {boolean}
   * @memberof LoadingBuilderComponent
   */
  get isPickDataSourceTypeDialogOpen(): boolean {
    return this.currentAddDataFilePage === AddDataFilePage.PickDataSourceType;
  }

  /**
   * Checks if the current dialog shows the GCS project list page.
   *
   * @return {boolean}
   * @memberof LoadingBuilderComponent
   */
  get isDataSourceListDialogOpen(): boolean {
    return this.currentAddDataFilePage === AddDataFilePage.DataSourceList;
  }

  /**
   * Checks if the current dialog shows the input data source info page.
   *
   * @return {boolean}
   * @memberof LoadingBuilderComponent
   */
  get isInputDataSourceInfoDialogOpen(): boolean {
    return this.currentAddDataFilePage === AddDataFilePage.InputDataSourceInfo;
  }

  /**
   * Checks if the current dialog shows the add data source file preview page.
   *
   * @return {boolean}
   * @memberof LoadingBuilderComponent
   */
  get isDataPreviewDialogOpen(): boolean {
    return this.currentAddDataFilePage === AddDataFilePage.DataPreview;
  }

  /**
   * Checks if the current dialog shows the JSON schema preview page.
   *
   * @return {boolean}
   * @memberof LoadingBuilderComponent
   */
  get isEditSchemaDialogOpen(): boolean {
    return this.currentAddDataFilePage === AddDataFilePage.EditSchema;
  }

  /**
   * Get current data source list length.
   *
   * @private
   * @return {number}
   * @memberof LoadingBuilderComponent
   */
  private getDataSourceListLength(): number {
    return this.dataSourceList.length;
  }

  /**
   * Show warning message for unsupported data source type.
   *
   * @param {string} typeName
   * @param {Event} [event]
   * @memberof LoadingBuilderComponent
   */
  showMsg(typeName: string, event?: Event) {
    if (event) {
      event.stopPropagation();
    }
    this.handleError(
      `${typeName} is not supported in GraphStudio now.`,
      'Coming soon'
    );
  }

  /**
   * Upload the selected file to server.
   *
   * @param {FileList} files
   * @memberof LoadingBuilderComponent
   */
  uploadFile(files: FileList) {
    if (files !== undefined) {
      const fileList = Array.from(files);
      fileList.forEach(file => {
        if (file.size < 1) {
          this.filesLessThan1Byte.push(file.name);
          return;
        }
        const extensionStart = file.name.lastIndexOf('.');
        if (
          !supportedFileType.includes(file.type) &&
          !supportedFileTypeExtension.includes(file.name.slice(extensionStart))
        ) {
          this.unsupportedTypeFiles.push(file.name);
          return;
        }
        if (this.fileList.includes(file.name)) {
          this.existingFiles.push(file.name);
          return;
        }
        this.newFiles.push(file);
      });

      if (
        this.filesLessThan1Byte.length > 0 ||
        this.existingFiles.length > 0 ||
        this.unsupportedTypeFiles.length > 0
      ) {
        this.dialog.open(
          this.previewFilesWindow,
          this.previewFilesWindowConfig
        )
          .afterClosed()
          .pipe(
            takeUntil(this.ngUnsubscribe),
            finalize(() => {
              this.fileInput.nativeElement.value = '';
              this.existingFiles = [];
              this.newFiles = [];
              this.unsupportedTypeFiles = [];
              this.filesLessThan1Byte = [];
            })
          )
          .subscribe(value => {
            if (value === 1) {
              this.filesToUpload = this.newFiles.map(file => ({ fileName: file.name }));
              this.openUploading();
              this.uploadFileList = this.newFiles;
              this.uploadFileListInOrder();
            }
          });
      } else {
        this.filesToUpload = this.newFiles.map(file => ({ fileName: file.name }));
        this.uploadFiles(this.newFiles);
      }
    }
  }

  private uploadFiles(files: File[]) {
    this.newFiles = [];
    this.unsupportedTypeFiles = [];
    this.fileInput.nativeElement.value = '';

    if (files.length === 0) {
      return;
    }
    this.openUploading();
    this.uploadFileList = files;
    this.uploadFileListInOrder();
  }

  /** Disable upload button in preview popup if no files to upload. */
  disableUploadInPreviewPopup(): boolean {
    return this.newFiles.length === 0;
  }

  /** Upload files in order. */
  uploadFileListInOrder() {
    const file = this.uploadFileList.shift();
    this.fileUploadService.upload(file);
  }

  /**
   * Cancel uploading.
   *
   * @memberof LoadingBuilderComponent
   */
  cancelUpload() {
    this.fileUploadService.cancel();
    this.closeUploading();
  }

  /** Open uploading files popup. */
  private openUploading() {
    this.loadingWindowRef = this.dialog.open(
      this.loadingWindow,
      this.loadingConfig
    );
  }

  /** Close uploading files popup. */
  closeUploading() {
    if (this.loadingWindowRef) {
      this.hasErrorWhenUpload = false;
      this.loadingWindowRef.close();
    }
  }

  /**
   * Record the selected file and call server for sample data from that file.
   *
   * @param {string} file
   * @memberof LoadingBuilderComponent
   */
  onFileSelection(file: string) {
    if (!file) {
      return;
    }

    this.initializeDataParsers();

    switch (this.selectedDataSourceType) {
      case DataSourceType.File:
        // Set file data format based on file extension.
        if (file.endsWith('.csv')) {
          this.selectedDataFormat = DataFormat.CSV;
          this.tabularParser.separator = ',';
        }
        if (file.endsWith('.tsv')) {
          this.selectedDataFormat = DataFormat.TSV;
          this.tabularParser.separator = '\\t';
        }
        if (file.endsWith('.jsonl') || file.endsWith('.json')) {
          this.selectedDataFormat = DataFormat.JSON;
        }
        break;
      case DataSourceType.Snowflake:
        this.tabularParser.separator = '|';
        break;
    }

    // Set file format based on file name extension.
    if (this.selectedDataSourceType !== DataSourceType.File) {
      fileExtensionToFormatMap.forEach(extension => {
        if (file && file.endsWith(extension[0])) {
          switch (this.selectedDataFormat) {
            case DataFormat.CSV:
            case DataFormat.TSV:
              this.tabularParser.format = extension[1];
              break;
            case DataFormat.JSON:
              this.jsonParser.format = extension[1];
          }
        }
      });
    }

    if (this.fileParsingDict.has(`${file}_${this.selectedDataFormat}`)) {
      const parsingOptions = this.fileParsingDict.get(`${file}_${this.selectedDataFormat}`);
      switch (this.selectedDataFormat) {
        case DataFormat.CSV:
        case DataFormat.TSV:
          const csvParsingParams = <TabularParser>parsingOptions;
          this.tabularParser.eol = csvParsingParams.eol;
          this.tabularParser.separator = csvParsingParams.separator;
          this.tabularParser.header = csvParsingParams.header;
          this.tabularParser.quote = csvParsingParams.quote || '';
          break;
        case DataFormat.JSON:
          const jsonParsingParams = <JSONParser>parsingOptions;
          this.jsonParser.eol = jsonParsingParams.eol;
          this.jsonParser.json = true;
          break;
        default:
          // TODO: Add support for more data types in the future
          break;
      }
    }
    this.selectedFile = file;

    this.getSampleData();
  }

  /**
   * Popup confirm window for user to delete selected file.
   *
   * @param {string} file
   * @memberof LoadingBuilderComponent
   */
  confirmDeleteFile(file: string) {
    const data: DialogData = {
      title: 'Warning',
      messages: [
        `After deleting file "${file}", you also need to remove corresponding data mapping.`,
        `Do you want to delete "${file}"?`
      ],
      actions: [
        { label: 'CANCEL', value: 0 },
        { label: 'DELETE', value: 1, color: 'warn' }
      ]
    };

    const dialogRef = this.dialog
      .open(DialogComponent, { data: data, autoFocus: false });

    dialogRef
      .keydownEvents()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(event => {
        if (event.key.toLowerCase() === 'enter') {
          dialogRef.close(1);
        }
      });

    dialogRef
      .afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(value => {
        if (value === 1) {
          this.loadingBuilderService.deleteFile(file)
            .pipe(takeUntil(this.ngUnsubscribe))
            .subscribe(
              () => {
                if (this.selectedFile === file) {
                  this.resetSelectedFile();
                }
                this.updateFileList();
              },
              (err: HttpSourceErrorResponse) =>
                this.handleError(err.error, err.statusText)
            );
        }
      });
  }

  /**
   * Get the tooltip message for 'add new data source' button.
   *
   * @returns {string}
   */
  getAddNewDataSourceButtonTooltip(): string {
    switch (this.selectedDataSourceType) {
      case DataSourceType.S3:
        return 'Add new Amazon S3 data source';
      case DataSourceType.GoogleCloudStorage:
        return 'Add new google cloud service data source';
      case DataSourceType.AzureBlobStorage:
        return 'Add new azure blob storage data source';
      case DataSourceType.Snowflake:
        return 'Add new snowflake data source';
      default:
        // TODO: Add more data sources here.
        return '';
    }
  }

  /**
   * Add new data source.
   *
   * @memberof LoadingBuilderComponent
   */
  addNewDataSource() {
    if (!this.graphName) {
      return;
    }

    switch (this.selectedDataSourceType) {
      case DataSourceType.S3:
      case DataSourceType.GoogleCloudStorage:
      case DataSourceType.AzureBlobStorage:
      case DataSourceType.Snowflake:
        this.credentialForm = this.loadingBuilderService.buildCredentialForm(
          this.selectedDataSourceType,
          this.dataSourceList.map((dataSource) => dataSource.name)
        );
        this.showInputDataSourceInfo();
        break;
      default:
        // TODO: Support more data source types.
        break;
    }
  }

  /**
   * Show or hide password.
   *
   * @param {HTMLElement} element
   * @memberof LoadingBuilderComponent
   */
  showOrHidePW(element: HTMLElement) {
    if (element.getAttribute('type') === 'password') {
      element.setAttribute('type', 'text');
    } else {
      element.setAttribute('type', 'password');
    }
  }

  /**
   * Confirm to add a new data source.
   *
   * @memberof LoadingBuilderComponent
   */
  confirmAddNewDataSource() {
    // Check if the name has already been used in gsql.
    const name = this.credentialForm.get('dataSourceName').value;
    this.selectedDataSourceName = name;
    const dataSourceJobs: Observable<any>[] = [];
    let dataSourceCredentialInfo = '';

    switch (this.selectedDataSourceType) {
      case DataSourceType.S3:
        dataSourceCredentialInfo = JSON.stringify({
          'file.reader.settings.fs.s3a.access.key': this.credentialForm.get('s3AccessKey').value,
          'file.reader.settings.fs.s3a.secret.key': this.credentialForm.get('s3SecretKey').value
        });
        break;
      case DataSourceType.GoogleCloudStorage:
        dataSourceCredentialInfo = this.gcsServiceAccountKeyFileContent;
        break;
      case DataSourceType.AzureBlobStorage:
        dataSourceCredentialInfo = JSON.stringify(
          DataSourceLogic.getABSCredentials(
            this.credentialForm.get('absConnectionString').value
          )
        );
        break;
      case DataSourceType.Snowflake:
        dataSourceCredentialInfo = JSON.stringify({
          'connection.user': this.credentialForm.get('username').value,
          'connection.password': this.credentialForm.get('password').value,
          'connection.url': this.credentialForm.get('url').value,
        });
        break;
      default:
        // TODO: Support more data source types.
        break;
    }

    this.setLoadingStatus(true);
    this.loadingBuilderService
      .createDataSource(
        this.graphName,
        this.selectedDataSourceType,
        name,
        dataSourceCredentialInfo
      )
      .pipe(
        takeUntil(this.ngUnsubscribe),
        concatMap(_ =>
          this.getDataSourceList(this.selectedDataSourceType)
            .pipe(
              takeUntil(this.ngUnsubscribe),
              finalize(() => this.setLoadingStatus(false))
            )
        )
      )
      .subscribe(
        () => {
          this.selectDataFormat(DataFormat.CSV);
          this.gcsServiceAccountKeyFile = undefined;
          this.gcsServiceAccountKeyFileContent = '';
          this.gcsServiceAccountKeyFormatIsValid = false;
        },
        (err: HttpErrorResponse) => {
          this.setLoadingStatus(false);
          this.handleError(err.error, err.statusText);
        }
      );
  }

  /**
   * Confirm delete a data source.
   *
   * @param {DataSource} dataSource
   * @memberof LoadingBuilderComponent
   */
  confirmDeleteDataSource(dataSource: DataSource) {
    // User without global level write data source privilege cannot delete global data source.
    if (
      !dataSource.isLocal &&
      !this.authService.hasPrivilege(GSQLPrivilege.WriteDataSource, GLOBAL_GRAPH_NAME)
    ) {
      this.handleError('You don\'t have the privilege to delete global data source.');
      return;
    }

    // User need to be globaldesigner or have write data source privilege
    // to delete a local data source.
    if (
      dataSource.isLocal &&
      !this.isGlobalDesigner() &&
      !this.authService.hasPrivilege(GSQLPrivilege.WriteDataSource)
    ) {
      this.handleError('You don\'t have the privilege to delete local data source.');
      return;
    }

    const dataSourceName = dataSource.name;
    const data: DialogData = {
      title: 'Warning',
      messages: [
        `After deleting data source "${dataSourceName}", you also need to remove corresponding data mapping.`,
        `Do you want to delete "${dataSourceName}"?`
      ],
      actions: [
        { label: 'CANCEL', value: 0 },
        { label: 'DELETE', value: 1, color: 'warn' }
      ]
    };

    const dialogRef = this.dialog
      .open(DialogComponent, { data: data, autoFocus: false });

    dialogRef
      .keydownEvents()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(event => {
        if (event.key.toLowerCase() === 'enter') {
          dialogRef.close(1);
        }
      });

    dialogRef
      .afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(value => {
        if (value !== 1) {
          return;
        }

        this.setLoadingStatus(true);
        this.loadingBuilderService.deleteDataSource(
          this.graphName,
          this.selectedDataSourceType,
          dataSourceName
        )
          .pipe(
            takeUntil(this.ngUnsubscribe),
            concatMap(_ =>
              this.getDataSourceList(this.selectedDataSourceType)
                .pipe(
                  takeUntil(this.ngUnsubscribe),
                  finalize(() => this.setLoadingStatus(false))
                )
            )
          )
          .subscribe(
            () => {},
            (err: HttpSourceErrorResponse) => {
              this.setLoadingStatus(false);
              this.handleError(err.error, err.statusText);
            }
          );
      });
  }

  /**
   * Select certain type data source.
   *
   * @param {string} dataSourceName
   * @memberof LoadingBuilderComponent
   */
  onDataSourceSelection(dataSourceName: string) {
    this.selectedDataSourceName = dataSourceName;
    this.resetSelectedFile();
    this.selectDataFormat(DataFormat.CSV);
  }

  /**
   * Check if a object node is not a file type node.
   *
   * @memberof LoadingBuilderComponent
   */
  isNotObjectNode = (_: number, node: ObjectNode) => node.type !== 'file';

  /**
   * Retrieve sample data from a data source with its data parser selections.
   *
   * @memberof LoadingBuilderComponent
   */
  getSampleData() {
    let dataParser: TabularParser | JSONParser;
    /**
     * this.dataParser is bind with loading options on page, and
     * getSampleData() will be triggered on this.dataParser change.
     * Thus, we need to record the change in fileInfo as well. User's
     * choice would be more likely to be correct.
     */
    switch (this.selectedDataFormat) {
      case DataFormat.CSV:
      case DataFormat.TSV:
        dataParser = cloneDeep(this.tabularParser);
        break;
      case DataFormat.JSON:
        dataParser = cloneDeep(this.jsonParser);
        break;
    }
    this.fileParsingDict.set(
      `${this.selectedFile}_${this.selectedDataFormat}`,
      dataParser
    );

    const fileURI = this.selectedDataSourceType === 'file'
      ? this.fileInfoMap.get(this.selectedFile).name
      : this.selectedFile;

    this.setLoadingStatus(true);
    this.loadingBuilderService
      .getSampleData(
        this.graphName,
        fileURI,
        this.selectedDataSourceType,
        this.selectedDataSourceName,
        dataParser,
        this.selectedDataFormat
      )
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.setLoadingStatus(false))
      )
      .subscribe(
        sampleData => {
          if (sampleData.data.length === 0) {
            this.parsingSucceed.next(false);
          } else {
            this.parsingSucceed.next(true);
          }
          this.addSampleData(sampleData);
        },
        (err: HttpSourceErrorResponse | string) => {
          this.parsingSucceed.next(false);
          if (typeof err === 'string') {
            this.handleError(err);
          } else {
            this.handleError(err.error, err.statusText);
          }
        }
      );
  }

  /**
   * Return the file name from the file path.
   *
   * @param {string} file
   * @returns {string}
   * @memberof LoadingBuilderComponent
   */
  getFileName(file: string): string {
    return file.lastIndexOf('loading_data/') > -1 ?
      file.substring(file.lastIndexOf('loading_data/') + 1) : HelperFunctions.retrieveSubFolderAndFileNameFromPath(file);
  }

  /**
   * Get token function by name
   *
   * @memberof LoadingBuilderComponent
   */
  getTokenFunction(name: string): TokenFunctionSignature {
    return combinedTokenFunctionList.find(func => func.name === name);
  }

  showingVisInfo(): boolean {
    return this.selectedEvent === ToolbarKeys.ShowLeftVisInfo ||
      this.selectedEvent === ToolbarKeys.ShowRightVisInfo;
  }

  /**
   * Check if the selected data format is CSV.
   *
   * @readonly
   * @type {boolean}
   * @memberof LoadingBuilderComponent
   */
  get isTabularFormat(): boolean {
    return this.selectedDataFormat === DataFormat.CSV
      || this.selectedDataFormat === DataFormat.TSV;
  }

  /**
   * Check if the selected data format is JSON.
   *
   * @readonly
   * @type {boolean}
   * @memberof LoadingBuilderComponent
   */
  get isJSONDataFormat(): boolean {
    return this.selectedDataFormat === DataFormat.JSON;
  }

  /**
   * Close the popup window.
   *
   * @memberof LoadingBuilderComponent
   */
  closePopup() {
    if (this.popupWindowRef) {
      this.popupWindowRef.close();
    }
  }

  toggleButtonStatus() {
    // Reset the popup window width to default if the current event is adding data source.
    if (this.selectedEvent === ToolbarKeys.AddDataFile) {
      this.toggleAddDataSourceMode();
      this.popupConfig.width = defaultPopupWidth;
    }
    if (this.selectedEvent === ToolbarKeys.AddTokenFunction) {
      this.toggleAddTokenFunctionMode();
    }
    if (this.selectedEvent === ToolbarKeys.WhereClause) {
      this.toggleAddWhereClauseMode();
    }
  }

  /**
   * Close the adding config popup window.
   *
   * @memberof LoadingBuilderComponent
   */
  closeConfigPopup() {
    if (this.credentialPopupWindowRef) {
      this.credentialPopupWindowRef.close();
    }
  }

  /**
   * Get the type of uri for a given data source type.
   *
   * @returns {string}
   */
  get dataSourceURIName(): string {
    return DataSourceLogic.getURIName(this.selectedDataSourceType);
  }

  /**
   * Get the uri placeholder for a given data source type.
   *
   * @returns {string}
   */
  get dataSourceUriPlaceholder(): string {
    return DataSourceLogic.getURIPrefixPlaceholder(this.selectedDataSourceType);
  }

  /**
   * Get the link to get S3, gsutil or abs URI.
   *
   * @returns {string}
   */
  getUriGuideLink(): string {
    return DataSourceLogic.getGuideLink(this.selectedDataSourceType);
  }

  /**
   * Add new data source.
   * Close the popup after adding.
   *
   * @memberof LoadingBuilderComponent
   */
  addDataFile() {
    // Create a data source.
    let dataSourceName: string;
    switch (this.selectedDataSourceType) {
      case DataSourceType.File:
        dataSourceName = undefined;
        break;
      default:
        dataSourceName = this.selectedDataSourceName;
        break;
    }
    const dataSource = DataSourceLogic.createDataSource(
      dataSourceName,
      this.selectedDataSourceType,
    );

    // Create a data file.
    let parsingOptions: ParsingOptions;
    let dataSet: DataSet;
    switch (this.selectedDataFormat) {
      case DataFormat.CSV:
      case DataFormat.TSV:
        parsingOptions = DataSetLogic.createTabularParsingOptions(
          this.tabularParser
        );

        const dataSchema = DataSetLogic.createTabularDataSchema(
          this.sampleColumnDefs.map(header => header.headerText),
        );

        dataSet = {
          uri: this.selectedFile,
          parsingOptions: parsingOptions,
          dataFormat: DataFormat.CSV,
          dataSchema: dataSchema,
          // TODO: Deprecate sample data in the future.
          sampleData: this.tabularSampleData.data,
        };
        break;
      case DataFormat.JSON:
        const error = this.jsonSchemaEditor.getSchemaErrorMessage();
        if (error.length !== 0) {
          this.handleError(error);
          return;
        }
        parsingOptions = DataSetLogic.createJSONParsingOptions(
          this.jsonParser
        );

        this.standardJSONSchema = this.jsonSchemaEditor.getStandardJsonSchema();

        this.jsonSchema = DataSetLogic.convertStandardSchemaToInMemorySchema(
          this.standardJSONSchema,
        );

        dataSet = {
          uri: this.selectedFile,
          parsingOptions: parsingOptions,
          dataFormat: DataFormat.JSON,
          dataSchema: this.jsonSchema,
        };
        break;
      default:
        // TODO: Support more data formats in the future.
        break;
    }

    this.logicService.createLoadingJob(
      dataSource,
      dataSet,
    );

    this.setWarningForUnsavedChanges();
    this.renderLeftChart(true);
    this.centerGraph();
    this.closePopup();

    this.loadingBuilderService.logFileAddSuccess(this.selectedDataFormat, this.selectedDataSourceType);
  }

  /**
   * Show the JSON schema and allow the user to edit it.
   */
  showJSONSchema() {
    this.standardJSONSchema = DataSetLogic.convertSampleDataToStandardSchema(
      this.jsonSampleData.data
    );
    this.jsonSchemaEditor = new VisJsonSchemaEditorComponent();
    this.currentAddDataFilePage = AddDataFilePage.EditSchema;
  }

  /**
   * Add new token function.
   * Close the popup after adding.
   *
   * @memberof LoadingBuilderComponent
   */
  addTokenFunction() {
    this.logicService.addTokenFunction(
      this.selectedTokenFunction.name,
      this.selectedTokenFunction.paramNumber
    );
    this.renderRightChart();
    this.initializeLoadingMapping();
    this.setWarningForUnsavedChanges();
    this.closePopup();
    this.loadingBuilderService.logTokenFunctionAddSuccess(
      this.selectedTokenFunction.name,
      this.selectedTokenFunction.paramNumber
    );
  }

  /**
   * Add WHERE clause.
   * Close the popup after finish.
   *
   * @memberof LoadingBuilderComponent
   */
  addWhereClause() {
    this.logicService.addWhereClause(this.whereClauseModel);
    this.renderRightChart();
    this.initializeLoadingMapping();
    this.setWarningForUnsavedChanges();
    this.closePopup();
    this.loadingBuilderService.logDataFilterAddSuccess();
  }

  /**
   * Disable add button in Add Data Filter popup when:
   * - Data filter model is undefined.
   * - Data filter model cannot pass semantic check if it is not null condition.
   *
   * NOTE: If switching to null condition means to delete data filter.
   */
  disableAddWhereClause(): boolean {
    return !this.whereClauseModel ||
      (
        !this.whereClauseModel.semanticCheck().success &&
        this.whereClauseModel.toJson().type !== 'NullCondition'
      );
  }

  /**
   * Add literal loading.
   * Close the popup after finish.
   *
   * @memberof LoadingBuilderComponent
   */
  addConstMapping() {
    if (this.inputConstString !== '' && this.inputConstString !== undefined) {
      try {
        this.logicService.addStringLiteralMapping(
          this.literalTarget, this.inputConstString
        );
      } catch (error) {
        this.handleError(error.toString());
      }
    } else {
      this.logicService.removeLoadingMappings([this.literalTarget]);
    }
    this.renderLeftChart(true);
    this.renderRightChart();
    this.initializeLoadingMapping();
    this.setWarningForUnsavedChanges();
    this.closePopup();
  }

  /**
   * Add new map widget.
   * Close the popup after adding.
   *
   * @memberof LoadingBuilderComponent
   */
  addMapWidget() {
    this.logicService.addMap(
      this.mapWidgetKeyType, this.mapWidgetValueType, this.widgetUDTName
    );
    this.renderRightChart();
    this.initializeLoadingMapping();
    this.setWarningForUnsavedChanges();
    this.closePopup();
    this.loadingBuilderService.logMapWidgetAddSuccess(
      this.mapWidgetKeyType,
      this.mapWidgetValueType,
      this.widgetUDTName
    );
  }

  /**
   * Add new udt widget.
   * Close the popup after adding.
   *
   * @memberof LoadingBuilderComponent
   */
  addTupleWidget() {
    // Only add existing tuple.
    const tuple = this.udtList.find(udt => udt.name === this.widgetUDTName);
    if (!tuple) {
      this.handleError(`Tuple ${this.widgetUDTName} doesn't exist.`);
      return;
    }
    this.logicService.addTuple(tuple);
    this.renderRightChart();
    this.initializeLoadingMapping();
    this.setWarningForUnsavedChanges();
    this.closePopup();
    this.loadingBuilderService.logUDTWidgetAddSuccess(this.widgetUDTName);
  }

  /**
   * Check if an icon is svg format.
   *
   * @param {string} iconName
   * @returns {Observable<boolean>}
   * @memberof LoadingBuilderComponent
   */
  isSvgIcon(iconName: string): Observable<boolean> {
    return this.matIconRegistry.getNamedSvgIcon(iconName)
      .pipe(
        mapTo(true),
        catchError(() => of(false))
      );
  }

  /**
   * Get the name of data source type.
   *
   * @param {string} key
   * @returns {string}
   * @memberof LoadingBuilderComponent
   */
  getSelectedDataSourceName(key: string): string {
    return this.dataSourceTypeButtons.find(type => type.key === key).title;
  }

  /**
   * Show 3 more data source type in menu.
   *
   * @memberof LoadingBuilderComponent
   */
  loadMoreDataSourceType() {
    this.numberOfSourceTypeShown += 3;
  }

  /**
   * Check if the current data source type is a kafka based data source.
   *
   * @returns {boolean}
   */
  get isRemoteDataSourceType(): boolean {
    return DataSourceLogic.isRemoteDataSourceType(this.selectedDataSourceType);
  }

  /**
   * Check if the current data source type is a kafka based data source.
   *
   * @returns {boolean}
   */
  get isRemoteWarehouseType(): boolean {
    return DataSourceLogic.isRemoteWarehouseType(this.selectedDataSourceType);
  }

  /**
   * Select a data source type and go the corresponding content.
   *
   * @param {DataSourceType} dataSourceType
   * @memberof LoadingBuilderComponent
   */
  selectDataSourceType(dataSourceType: DataSourceType) {
    if (this.selectedDataSourceType === dataSourceType) {
      return;
    }

    this.selectedDataSourceName = undefined;
    this.resetSelectedFile();
    this.selectedDataSourceType = dataSourceType;
    // Set the popup window to auto width for more space.
    // this.popupWindowRef.updateSize(dataPopupWidth); // this will be used if we have a data source selection step.
    /**
     * Since we jump directly to file data source window, the popup does not exist at this point in time.
     * Thus we need to set the popup window's width statically instead of dynamically.
     */

    switch (dataSourceType) {
      case DataSourceType.File:
        this.loadingBuilderService
          .getFileList()
          .pipe(takeUntil(this.ngUnsubscribe))
          .subscribe(
            fileInfoList => {
              this.fileList = Array.from(fileInfoList.keys()).sort();
              this.fileList$.next(this.fileList);
              this.fileInfoMap = fileInfoList;
              this.showDataPreviewDialog();
            },
            (err: HttpErrorResponse) => this.handleError(err.error, err.statusText)
          );
        break;
      case DataSourceType.S3:
      case DataSourceType.GoogleCloudStorage:
      case DataSourceType.AzureBlobStorage:
      case DataSourceType.Snowflake:
        this.getDataSourceList(this.selectedDataSourceType)
          .pipe(takeUntil(this.ngUnsubscribe))
          .subscribe(
            (dataSourceNames) => {
              if (dataSourceNames.length > 0) {
                this.showDataSourceListDialog();
                return;
              }

              this.credentialForm = this.loadingBuilderService.buildCredentialForm(
                this.selectedDataSourceType,
                dataSourceNames
              );
              this.showInputDataSourceInfo();
            },
            (error: HttpErrorResponse) => this.handleError(error.error, error.statusText)
          );
        break;
      default:
        // TODO: Support more data source types.
        break;
    }
  }

  /**
   * Select a data format
   *
   * @param {DataFormat} dataFormat
   * @memberof LoadingBuilderComponent
   */
  selectDataFormat(dataFormat: DataFormat) {
    if (this.selectedDataSourceType === DataSourceType.Snowflake) {
      this.setLoadingStatus(true);
      this.getMetadata(this.selectedDataSourceName);
    }
    switch (this.selectedDataSourceType) {
      case DataSourceType.File:
        break;
      case DataSourceType.S3:
      case DataSourceType.GoogleCloudStorage:
      case DataSourceType.AzureBlobStorage:
      case DataSourceType.Snowflake:
        this.uriInputForm = this.loadingBuilderService.buildUriInputForm(this.selectedDataSourceType);
        this.selectedDataFormat = dataFormat;
        this.showDataPreviewDialog();
        break;
      default:
        // TODO: Support more data source types.
        break;
    }
  }

  /**
   * Show data source List dialog.
   *
   * @memberof LoadingBuilderComponent
   */
  showDataSourceListDialog() {
    this.currentAddDataFilePage = AddDataFilePage.DataSourceList;
  }

  /**
   * Show Input data source dialog.
   *
   * @memberof LoadingBuilderComponent
   */
  showInputDataSourceInfo() {
    this.currentAddDataFilePage = AddDataFilePage.InputDataSourceInfo;
  }

  /**
   * Show data preview dialog.
   *
   * @memberof LoadingBuilderComponent
   */
  showDataPreviewDialog() {
    this.currentAddDataFilePage = AddDataFilePage.DataPreview;
  }

  /**
   * Check if element name is overflow.
   *
   * @param {HTMLElement} element
   * @returns {boolean}
   * @memberof LoadingBuilderComponent
   */
  isOverflow(element: HTMLElement): boolean {
    return element.clientWidth < element.scrollWidth;
  }

  showFileInfo(fileName: string): string {
    const fileInfo = this.fileInfoMap.get(fileName);
    // TODO: Check how to customize tooltip with HTML.
    const lastModified = HelperFunctions.epochToDatetime(fileInfo.modTime);
    const size = HelperFunctions.abbreviateByte(fileInfo.size);
    return `Last Modified: ${lastModified}\nSize: ${size}`;
  }

  /**
   * Check if the token function input is satisfied.
   *
   * @readonly
   * @type {boolean}
   * @memberof LoadingBuilderComponent
   */
  get disableAddToken(): boolean {
    // Token function that allow user to input param number is disabled if the number is smaller than 1.
    return !this.selectedTokenFunction.paramNumber || this.selectedTokenFunction.paramNumber < 1;
  }

  /**
   * Handle mouse pointer up interaction on the left graph chart.
   *
   * @param {(GraphChartDataNode | GraphChartDataLink)} item
   * @memberof LoadingBuilderComponent
   */
  handleLeftGraphChartPointer(item?: GraphChartDataNode | GraphChartDataLink) {
    // If user cannot create loading jobs, the only interaction supported is choosing loading statement to show.
    if (!this.canWriteLoadingJob) {
      this.selectedElement = item;
      if (
        item &&
        'isLink' in item &&
        item.exType.includes(dataSourceEdgeType)
      ) {
        // Render the right chart if the user click on a data mapping edge.
        const edge = <GraphChartDataLink>item;
        const loadingJobIndex = edge.source.exID;
        const loadingStatementIndex = edge.others.statementId;

        this.logicService.selectCurrentLoadingStatement(
          +loadingJobIndex,
          +loadingStatementIndex
        );
        this.renderLeftChart(true);
        this.showRightHint.next(false);
        this.renderRightChart();
        this.setWarningForUnsavedChanges();
      }
    } else {
      // If clicked virtual vertex, don't do anything
      if (item && 'exType' in item && item.exType === virtualNodeType) {
        return;
      }

      this.selectedElement = item;
      if (
        item &&
        'isLink' in item &&
        item.exType.includes(dataSourceEdgeType)
      ) {
        // If adding data mapping, cannot select loading statement
        if (this.isDataMapping) {
          return;
        }

        this.isLoadingMapping = true;
        // Render the right chart if the user click on a data mapping edge.
        const edge = <GraphChartDataLink>item;
        const loadingJobIndex = edge.source.exID;
        const loadingStatementIndex = edge.others.statementId;

        this.logicService.selectCurrentLoadingStatement(
          +loadingJobIndex,
          +loadingStatementIndex
        );

        this.showRightHint.next(false);
        this.renderRightChart();
        // When switch current loading statement, need clear already selected mapping data.
        // Otherwise will try to build mapping with index of another loading statement.
        this.initializeLoadingMapping();
        if (this.canEdit) {
          this.setWarningForUnsavedChanges();
        }
      } else if (item) {
        // Otherwise, trigger data mapping handler.
        this.handleDataMapping();
      }
      this.updateVerticesPositions(item);
      if (this.canEdit) {
        this.setWarningForUnsavedChanges();
      }
    }
  }

  /**
   * Set warning text if user has unsaved loading jobs or loading jobs style changes.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private setWarningForUnsavedChanges() {
    const sink = this.bus.to<boolean>('HasUnsavedChanges');
    let isChanged = false;
    let text: string;

    const loadingJobChange = this.getLoadingJobChange();
    this.logicService.markUnsavedLoadingJobs(loadingJobChange.jobNames);

    switch (loadingJobChange.status) {
      case LoadingJobChangeStatus.DataMappingChange:
        isChanged = true;
        text = 'You have not published your changes to the data mapping. Please publish changes before loading';
        break;
      case LoadingJobChangeStatus.StyleChange:
        isChanged = true;
        text = 'You have not published your changes to the data mapping style.';
        break;
      default:
        break;
    }
    this.postMessageService.postCanDeactivate(!isChanged);
    sink.next(isChanged);
    this.unsavedWarning.next(text);
  }

  /**
   * Get the loading job change.
   *
   * @private
   * @returns {LoadingJobChange}
   * @memberof LoadingBuilderComponent
   */
  private getLoadingJobChange(): LoadingJobChange {
    // Retrieve loading jobs and loading job styles.
    let inMemoryGSQLLoadingJobs: GSQLLoadingJobJson[];
    let inMemoryLoadingJobsStyle: DBLoadingJobJson[];
    [
      inMemoryGSQLLoadingJobs,
      inMemoryLoadingJobsStyle
    ] = this.logicService.getGSQLLoadingJobJsonAndDBLoadingJobJson();

    // Retrieve graph style.
    const inMemoryGraphStyle = this.logicService.getDBGraphStyleJson();

    return LoadingJobChangeLogic.getLoadingJobChange(
      this.savedLoadingJobs,
      inMemoryGSQLLoadingJobs,
      this.savedLoadingJobsStyle,
      inMemoryLoadingJobsStyle,
      this.savedGraphStyle,
      inMemoryGraphStyle
    );
  }

  /**
   * Get the schema from the server and load it to the visualization.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private getSchemaAndLoadingJobs() {
    this.setLoadingStatus(true);

    const curGraph = this.authService.getCurrentGraph();
    // Get graph schema, loading jobs and UDT list.
    forkJoin(
      [
        this.loadingBuilderService.getSchema(curGraph),
        this.loadingBuilderService.getLoadingJobs(curGraph),
        this.loadingBuilderService.getGSQLUdtList(curGraph)
      ]
    )
      .pipe(takeUntil(this.ngUnsubscribe))
      // Here the type is referred as GSQLGraphJson[], so type mismatch error. Use any to walk around
      .subscribe(
        ([schema, loadingJobs, udts]: any) => {
          if (schema !== undefined) {
            this.graphName = schema.GraphName;
          }
          if (udts && udts.length > 0) {
            this.udtList = udts;
            this.widgetUDTName = this.udtList[0].name;
          } else {
            this.widgetUDTName = undefined;
          }

          if (schema && loadingJobs) {
            // Get graph schema style.
            this.loadingBuilderService
              .getSchemaStyle(this.graphName)
              .pipe(takeUntil(this.ngUnsubscribe))
              .subscribe(
                style => {
                  if (style) {
                    this.getLoadingJobsInformation(schema, style, loadingJobs);
                    this.savedGraphStyle = style;
                  } else {
                    this.getLoadingJobsInformation(
                      schema,
                      undefined,
                      loadingJobs
                    );
                  }
                },
                (err: HttpErrorResponse) =>
                  this.handleError(err.error, err.statusText)
              );
          } else {
            this.showLeftHint.next(true);
            this.setLoadingStatus(false);
          }
        },
        (err: HttpErrorResponse) => this.handleError(err.error, err.statusText)
      );
  }

  /**
   * Get loading jobs information and load it to visualization.
   *
   * @private
   * @param {GSQLGraphJson} schema
   * @param {DBGraphStyleJson} style
   * @param {GSQLLoadingJobJson[]} loadingJobs
   * @memberof LoadingBuilderComponent
   */
  private getLoadingJobsInformation(
    schema: GSQLGraphJson,
    style: DBGraphStyleJson,
    loadingJobs: GSQLLoadingJobJson[]
  ) {
    // Get loading jobs information.
    this.loadingBuilderService
      .getLoadingJobsInfo(this.graphName)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(
        loadingJobsInfo => {
          try {
            if (loadingJobsInfo) {
              loadingJobsInfo.forEach(loadingJobInfo => delete loadingJobInfo.dataSourceJson.auth);
              this.savedLoadingJobsStyle = loadingJobsInfo;
              this.logicService.setSchemaLoadingJobAndStyle(
                schema,
                style,
                loadingJobs,
                loadingJobsInfo,
                this.udtList
              );
            } else {
              this.logicService.setSchemaLoadingJobAndStyle(
                schema,
                style,
                loadingJobs,
                undefined,
                this.udtList
              );
            }
            this.savedLoadingJobs = this.logicService.getGSQLLoadingJobJsonAndDBLoadingJobJson()[0];
            // Assign new style for vertices and edges not having a style.
            this.leftVisService.setStylesForGraphSchema(this.logicService.graph);
            // Do semantic check for all loading jobs.
            this.logicService.semanticCheckAllLoadingJobs();
            const hasFileTypeLoadingJobs =
              this.leftVisService.filterLoadingJobBasedOnType(this.logicService.loadingJobs, 'file')
                .length > 0;
            if (hasFileTypeLoadingJobs) {
              this.logicService.loadingJobs.forEach(loadingJob => {
                const fileInfo = this.fileInfos.get(loadingJob.dataSet.uri);
                if (fileInfo) {
                  loadingJob.dataSet.info = fileInfo;
                }
              });
            }
            this.renderLeftChart(
              style &&
              style.vertexStyles &&
              schema.VertexTypes.filter(
                vt => Object.keys(style.vertexStyles).includes(vt.Name)
              ).length > 0
            );
            this.renderRightChart();
            if (this.canEdit) {
              this.setWarningForUnsavedChanges();
            }
            this.setLoadingStatus(false);

            // Handle if there are loading jobs need to be migrated.
            this.bus.from<LoadingJobData[]>('MigratedLoadingJobs')
              .pipe(takeUntil(this.ngUnsubscribe))
              .subscribe((migratedLoadingJobs) => {
                // Remove the channel so next time the migration won't be triggered again.
                this.bus.removeChannel('MigratedLoadingJobs');
                // Replace with migrated loading jobs.
                this.logicService.replaceWithMigratedLoadingJobs(migratedLoadingJobs);
                // Do semantic check for all loading jobs.
                this.logicService.semanticCheckAllLoadingJobs();
                this.renderLeftChart(
                  style &&
                  style.vertexStyles &&
                  schema.VertexTypes.filter(
                    vt => Object.keys(style.vertexStyles).includes(vt.Name)
                  ).length > 0
                );
                this.renderRightChart();
                if (this.canEdit) {
                  this.setWarningForUnsavedChanges();
                }
                this.setLoadingStatus(false);
              });
          } catch (error) {
            this.handleError(error.message);
          }
        },
        (err: HttpErrorResponse) => this.handleError(err.error, err.statusText)
      );
  }

  /**
   * Handler for right chart event.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private handleRightChartEvent() {
    // In right chart, all interactions are disabled if user doesn't have write loading job privilege.
    if (!this.canWriteLoadingJob) {
      this.rightChart.freeze();
      return;
    }

    // Handle data table position when data filter table being moved.
    this.rightChart.on('node:moving', ({ node }) => {
      this.rightVisService.handlePositionMoving(node);
    });

    // Handle node position in loading statement update when its position being changed.
    this.rightChart.on('node:moved', ({ node }) => {
      const {
        dataSourcePosition,
        graphEntityPosition,
        mappingWidgetPosition
      } = this.rightVisService.handlePositionChange(node);

      this.logicService.updateCurrentLoadingStatementPositions(
        dataSourcePosition,
        graphEntityPosition,
        mappingWidgetPosition
      );
    });

    // Handle edge creation.
    this.rightChart.on('edge:connected', ({ edge }) => {
      const { mappingSource, mappingTarget } = VisualDataMappingLogic.createMappingSourceAndTarget(edge);
      this.mappingSource = mappingSource;
      this.mappingTarget = mappingTarget;
      this.handleLoadingMapping();
    });

    this.rightChart.on('port:click', ({ node, y }) => {
      if (!this.canWriteLoadingJob) {
        return;
      }
      this.onPortClick(node, y);
    });

    // Handle node deletion.
    this.rightChart.on('node:mouseenter', ({ node }) => {
      if (VisualDataMappingLogic.isMappingWidgetCell(node)) {
        this.onNodeHover(node);
      }
    });
    this.rightChart.on('node:mouseleave', ({ node }) => {
      node.removeTools();
    });

    // Handle edge hovering event.
    this.rightChart.on('edge:mouseenter', ({ e, edge }) => {
      this.onEdgeHover(edge, e.offsetX - 30, e.offsetY - 30);
    });
    this.rightChart.on('edge:mouseleave', ({ edge }) => {
      edge.removeTools();
      this.rightVisService.hideTooltip();
    });

    // Handle editting data filter.
    this.rightChart.on('edge:dblclick', ({ edge }) => {
      if (edge.id === 'dataFilterEdge') {
        this.onInteraction({ key: ToolbarKeys.WhereClause });
      }
    });

    // Handle unselecting left chart elements.
    this.rightChart.on('graph:mouseenter', () => { });

    // Handle unselecting left chart elements.
    this.rightChart.on('blank:click', () => { });
  }

  /**
   * Set the initial value of constant input.
   *
   * @private
   * @param {OneColumnMapping} item
   * @returns {string}
   * @memberof LoadingBuilderComponent
   */
  private setInitialLiteral(item: OneColumnMapping): string {
    if (item.sourceType === SourceType.Literal) {
      return item.literal;
    } else {
      return '';
    }
  }

  /**
   * Check if sample data is defined.
   *
   * @readonly
   * @type {boolean}
   * @memberof LoadingBuilderComponent
   */
  get isSampleDataDefined(): boolean {
    switch (this.selectedDataFormat) {
      case DataFormat.CSV:
      case DataFormat.TSV:
        return this.tabularSampleData !== undefined;
      case DataFormat.JSON:
        return this.jsonSampleData !== undefined;
    }
  }

  /**
   * Assume sample data is defined, check if the sample data is empty.
   *
   * @readonly
   * @type {boolean}
   * @memberof LoadingBuilderComponent
   */
  get isSampleDataPresent(): boolean {
    switch (this.selectedDataFormat) {
      case DataFormat.CSV:
      case DataFormat.TSV:
        return this.tabularSampleData.data.length !== 0;
      case DataFormat.JSON:
        return this.jsonSampleData.data.length !== 0;
    }
  }

  /**
   * Add sample data to the data source of the table.
   *
   * @private
   * @param {TableSampleData | JSONSampleData} sampleData
   * @memberof LoadingBuilderComponent
   */
  private addSampleData(sampleData: TableSampleData | JSONSampleData) {
    switch (this.selectedDataFormat) {
      case DataFormat.CSV:
      case DataFormat.TSV:
        this.tabularSampleData = <TableSampleData>sampleData;
        this.sampleDatabase.addData(this.tabularSampleData, this.sampleTableId);
        this.sampleColumnDefs = [];
        this.sampleColumnIds = [];
        /**
         * Build the column definitions and ids.
         * Material optimizes the table by calculating a diff between old and new set of columns.
         * If we use the same ids, the column headers will not be updated, thus we append an
         * incremental tableId to create a difference between each table rendering.
         */
        this.tabularSampleData.header.forEach((header, i) =>
          this.sampleColumnDefs.push({
            id: `${i}_${this.sampleTableId}`,
            property: `${i}_${this.sampleTableId}`,
            headerText: header,
            editing: false
          })
        );
        this.sampleColumnIds = this.sampleColumnDefs.map(
          columnDef => columnDef.id
        );
        this.sampleTableId++;
        break;
      case DataFormat.JSON:
        this.jsonSampleData = sampleData;
        this.jsonText = [];
        this.jsonSampleData.data.forEach(data => {
          if (INVALID_JSON_SAMPLE_DATA_IDENTIFIER in data) {
            this.jsonText.push((data[INVALID_JSON_SAMPLE_DATA_IDENTIFIER]));
          } else {
            this.jsonText.push(JSON.stringify(data, undefined, 2));
          }
        });
        break;
      default:
        // TODO: Support more data formats in the future.
        break;
    }
  }

  /** Enable to edit sample data header. */
  editHeaderText(column: HeaderColumn) {
    this.sampleColumnDefs.forEach(col => col.editing = false);
    column.editing = true;
    this.newHeaderText = column.headerText;
  }


  /** Confirm to change sample data header or cancel change. */
  finishHeaderEditing(column: HeaderColumn, change = false) {
    if (change) {
      column.headerText = this.newHeaderText;
    }
    this.newHeaderText = '';
    column.editing = false;
  }

  /**
   * Add where clause sample data to the data source of the table.
   *
   * @private
   * @param {*} sampleData
   * @memberof LoadingBuilderComponent
   */
  private addWhereClauseSampleData(sampleData: TableSampleData) {
    this.whereClauseDatabase.addData(sampleData, this.whereClauseTableId);
    this.whereClauseColumnDefs = [];
    this.whereClauseColumnIds = [];

    /**
     * Build the column definitions and ids.
     * Material optimizes the table by calculating a diff between old and new set of columns.
     * If we use the same ids, the column headers will not be updated, thus we append an
     * incremental tableId to create a difference between each table rendering.
     */
    sampleData.header.forEach((header, i) =>
      this.whereClauseColumnDefs.push({
        id: `${i}_${this.whereClauseTableId}`,
        property: `${i}_${this.whereClauseTableId}`,
        headerText: header
      })
    );

    this.whereClauseColumnIds = this.whereClauseColumnDefs.map(
      columnDef => columnDef.id
    );

    this.whereClauseTableId++;
  }

  /**
   * Get data source list of a given type from app server.
   *
   * @memberof LoadingBuilderComponent
   */
  getDataSourceList(type: DataSourceType): Observable<string[]> {
    if (!this.graphName) {
      return of([]);
    }

    this.setLoadingStatus(true);
    return this.loadingBuilderService
      .getDataSourceNames(this.graphName, type)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.setLoadingStatus(false)),
        map(dataSourceNames => {
          this.dataSourceList = dataSourceNames.map(dataSourceName => {
              return {
                name: dataSourceName,
                type: type,
                isLocal: true
              };
            });
          return dataSourceNames;
        })
      );
  }

  /**
   * Retrieve the list of files from the server.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private getFileList() {
    this.loadingBuilderService
      .getFileList()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(
        fileInfoList => {
          this.fileList = Array.from(fileInfoList.keys()).sort();
          this.fileList$.next(this.fileList);
          this.fileInfoMap = fileInfoList;
        },
        (err: HttpErrorResponse) => this.handleError(err.error, err.statusText)
      );
  }

  /**
   * Retrieve the metadata of a data source from the server.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private getMetadata(dataSourceName: string) {
    this.metadata = undefined;
    timer()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.setLoadingStatus(true);
      });
    this.loadingBuilderService
      .getMetadata(dataSourceName)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.setLoadingStatus(false)))
      .subscribe(
        metadata => {
          this.metadata = metadata;
        },
        (err: HttpErrorResponse) => this.handleError(err.error, err.statusText)
      );
  }

  /**
   * Retrieve the list of DataFormat.
   *
   * @private
   * @return {string[]}
   * @memberof LoadingBuilderComponent
   */
  private getDataFormatList(): string[] {
    return ['csv', 'json'];
  }

  /**
   * Reset selected file to empty, and clear sample data.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private resetSelectedFile() {
    this.selectedFile = undefined;
    this.tabularSampleData = undefined;
    this.jsonSampleData = undefined;
  }

  /**
   * Handle response for file upload.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private handleFileUploadResponse() {
    this.fileUploadService.response$
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(response => {
        const index = this.filesToUpload.length - this.uploadFileList.length - 1;
        switch (response.status) {
          case 'oneSingleFileComplete':
            this.filesToUpload[index].uploaded = true;
            if (this.filesToUpload[index].error === undefined) {
              this.snackBar.open(
                'Upload ' + this.filesToUpload[index].fileName + ' successfully',
                'DISMISS',
                { duration: 3000 }
              );
            }
            if (this.uploadFileList.length > 0) {
              this.uploadFileListInOrder();
            } else {
              this.uploadStatus.next('complete');
              if (!this.hasErrorWhenUpload) {
                this.closeUploading();
              } else {
                this.uploadStatus.next('error');
              }
              this.updateFileList();
            }
            break;
          case 'cancel':
            this.updateFileList();
            break;
          case 'error':
            this.filesToUpload[index].error = response.message;
            this.snackBar.open(
              'Fail to ' + this.filesToUpload[index].fileName,
              'DISMISS',
              { duration: 3000 }
            );
            this.hasErrorWhenUpload = true;
            if (this.uploadFileList.length > 0) {
              this.uploadFileListInOrder();
            } else {
              this.uploadStatus.next('error');
              this.updateFileList();
            }
            break;
          case 'uploadStart':
            this.uploadProgress.next(0);
            this.uploadStatus.next(response.status);
            break;
          case 'progress':
            this.uploadProgress.next(response.progress);
            this.uploadStatus.next(response.status);
            break;
        }
      });
  }

  /** Update file list and render chart. */
  private updateFileList() {
    this.loadingBuilderService
      .getFileList()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(
        fileInfoList => {
          this.fileList = Array.from(fileInfoList.keys()).sort();
          this.fileList$.next(this.fileList);
          this.fileInfoMap = fileInfoList;
          if (this.hasFileTypeLoadingJob()) {
            this.renderLeftChart(true);
          }
        },
        (err: HttpErrorResponse) => this.handleError(err.error, err.statusText)
      );
  }

  /**
   * Toggle the add data source mode by setting a flag.
   * Also change the add data source button color to accent or default depends on mode.
   *
   * @private
   *
   * @memberof LoadingBuilderComponent
   */
  private toggleAddDataSourceMode() {
    const dataSourceButton = <ButtonBase>this.toolbarConfig.top.find(
      button => button.key === ToolbarKeys.AddDataFile
    );
    this.isAddingDataSource = !this.isAddingDataSource; // turn add data source mode on or off.

    if (this.isAddingDataSource) {
      dataSourceButton.color = 'accent';
    } else {
      dataSourceButton.color = '';
    }
  }

  /**
   * Toggle the add token function mode by setting a flag.
   * Also change the add token function button color to accent or default depends on mode.
   *
   * @private
   *
   * @memberof LoadingBuilderComponent
   */
  private toggleAddTokenFunctionMode() {
    const dataSourceButton = <ButtonBase>this.mappingToolbarConfig.top.find(
      button => button.key === ToolbarKeys.AddTokenFunction
    );
    this.isAddingTokenFunction = !this.isAddingTokenFunction; // turn add token function mode on or off.

    if (this.isAddingTokenFunction) {
      dataSourceButton.color = 'accent';
    } else {
      dataSourceButton.color = '';
    }
  }

  /**
   * Toggle the data mapping by setting a flag.
   * Also change the button color to accent or default depends on mode.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private toggleDataMappingMode() {
    this.toggleButtonFocus(ToolbarKeys.AddDataMapping);
    this.isDataMapping = !this.isDataMapping; // turn add data mapping mode on or off.

    if (this.isDataMapping) {
      // this.handleDataMapping();
      // Save toolbar disabled status and disable action buttons
      this.saveToolbarDisabledStatus();
      this.toolbarConfig.top[ToolbarButtons.AddDataFile].disabled = true;
      this.toolbarConfig.top[ToolbarButtons.Delete].disabled = true;
      this.toolbarConfig.top[ToolbarButtons.Undo].disabled = true;
      this.toolbarConfig.top[ToolbarButtons.Redo].disabled = true;
      this.toolbarConfig.top[ToolbarButtons.StartLoading].disabled = true;
      this.toolbarConfig.top[ToolbarButtons.PauseLoading].disabled = true;
      this.toolbarConfig.top[ToolbarButtons.StopLoading].disabled = true;
      this.mappingToolbarConfig.top[MappingToolbarButtons.AddTokenFunction].disabled = true;
      this.mappingToolbarConfig.top[MappingToolbarButtons.AutoMapping].disabled = true;
      this.mappingToolbarConfig.top[MappingToolbarButtons.WhereClause].disabled = true;
      this.mappingToolbarConfig.top[MappingToolbarButtons.More].disabled = true;
    } else {
      // Load toolbar disabled status
      this.loadToolbarDisabledStatus();

      if (this.dataVertex) {
        // Refresh loading label
        this.loadingJobGraph = { ...this.loadingJobGraph };
        this.forceUpdateLoadingStatus = true;
      }
      this.initializeDataMapping(); // prepare for a new data mapping action.
    }
  }

  /**
   * Toggle the add data filter mode by setting a flag.
   * Also change the add data filter button color to accent or default depends on mode.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private toggleAddWhereClauseMode() {
    const addExprButton = <ButtonBase>this.mappingToolbarConfig.top.find(
      button => button.key === ToolbarKeys.WhereClause
    );
    this.isAddingWhereClause = !this.isAddingWhereClause;

    if (this.isAddingWhereClause) {
      addExprButton.color = 'accent';
    } else {
      addExprButton.color = '';
    }
  }

  /**
   * Handler for data mapping action.
   * Set the data source and target element accordingly and add the mapping if both are set.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private handleDataMapping() {
    if (this.isDataMapping && this.selectedElement) {
      if (
        'isNode' in this.selectedElement &&
        'jobName' in this.selectedElement.others
      ) {
        // Ignore if the selected element is the same as the current data vertex.
        if (this.dataVertex === this.selectedElement) {
          return;
        }

        this.dataVertex = <GraphChartDataNode>this.selectedElement;
        this.leftVisService.addDataMappingLabel(this.dataVertex); // add the data mapping label.
      } else if (this.dataVertex) {
        // Construct the using clauses.
        const usingClauses = {};
        const parsingOptions = this.dataVertex.others.dataSet.parsingOptions;

        usingClauses['EOL'] = DataSourceLogic.eols.filter(
          eol => eol[1] === parsingOptions.eol
        )[0][1];

        switch (this.dataVertex.others.dataSet.dataFormat) {
          case DataFormat.CSV:
          case DataFormat.TSV:
            usingClauses['SEPARATOR'] = DataSourceLogic.separators.filter(
              sep => sep[1] === parsingOptions.separator
            )[0][1];
            usingClauses['HEADER'] = parsingOptions.header ? 'true' : 'false';
            if (parsingOptions.quote !== '') {
              usingClauses['QUOTE'] = DataSourceLogic.quotes.filter(
                quote => quote[1] === (parsingOptions.quote || '')
              )[0][1];
            }
            if (parsingOptions.separator === '\\u0020') {
              usingClauses['SEPARATOR'] = ' ';
            }
            break;
          case DataFormat.JSON:
            usingClauses['JSON_FILE'] = 'true';
            break;
        }

        // FIXME: special case for whitespace for now.
        if (parsingOptions.eol === '\\u0020') {
          usingClauses['EOL'] = ' ';
        }

        // If the data source and target element is selected, add the mapping.
        if ('isNode' in this.selectedElement) {
          this.isLoadingMapping = true;
          const element = <GraphChartDataNode>this.selectedElement;
          this.logicService.createLoadToVertexStatement(
            +this.dataVertex.exID,
            element.exType,
            usingClauses,
          );
          this.loadingBuilderService.logLoadingStatementAddSuccess('Node');
        } else {
          const element = <GraphChartDataLink>this.selectedElement;
          // If the edge is reverse edge, report error.
          if (!this.logicService.graph.getEdge(element.exType)) {
            this.handleError(
              'You cannot map data to reverse edge type directly, ' +
              'please map to the forward edge type.'
            );
            return;
          }
          this.isLoadingMapping = true;
          this.logicService.createLoadToEdgeStatement(
            +this.dataVertex.exID,
            element.exType,
            element.source.exType,
            element.target.exType,
            usingClauses,
          );
          this.loadingBuilderService.logLoadingStatementAddSuccess('Edge');
        }

        // Exit adding data mapping mode
        this.toggleDataMappingMode();

        this.renderLeftChart(true);
        this.showRightHint.next(false);
        this.renderRightChart();
        this.initializeDataMapping();
        this.initializeLoadingMapping();
        this.setWarningForUnsavedChanges();
      }
    }
  }

  /**
   * Handler for loading mapping action.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private handleLoadingMapping() {
    // Add the mapping when both source and target are selected.
    try {
      this.logicService.addLoadingMappings([
        [this.mappingSource, this.mappingTarget]
      ]);
      this.renderLeftChart(true);
      this.renderRightChart();
      this.setWarningForUnsavedChanges();
    } catch (error) {
      this.renderRightChart();
      this.handleError(error.message);
    }

    this.initializeLoadingMapping(); // prepare for a new loading mapping action.
  }

  /**
   * Save the loading jobs, loading jobs style, new schema style (might moved) to the server.
   * Notify the user if success or failure.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private handleSave() {
    const jobs: Observable<any>[] = [];

    let gsqlLoadingJobs: GSQLLoadingJobJson[];
    let dbLoadingJobs: DBLoadingJobJson[];
    [
      gsqlLoadingJobs,
      dbLoadingJobs
    ] = this.logicService.getGSQLLoadingJobJsonAndDBLoadingJobJson();

    // Collect the jobs that save GSQL loading job.
    const gsqlLoadingJobChange = this.handleSaveGSQLLoadingJob(jobs, gsqlLoadingJobs);

    // Collect the jobs that save loading job style.
    jobs.push(this.loadingBuilderService.saveLoadingJobsInfo(this.graphName, dbLoadingJobs)
      .pipe(
        map(() => 'saveLoadingJobsInfo')
      ));

    // Collect the jobs that save graph schema style.
    const graphStyle = this.logicService.getDBGraphStyleJson();
    jobs.push(this.loadingBuilderService.saveSchemaStyle(this.graphName, graphStyle)
      .pipe(
        map(() => 'saveSchemaStyle')
      ));

    // Finish collecting jobs.
    jobs.push(of('finish'));

    // Open progress window.
    this.updateLoadingJobsProgressWindowRef = this.dialog.open(
      this.updateLoadingJobsProgressWindow,
      this.updateLoadingJobsProgressConfig
    );

    let progress = 0;
    this.updateLoadingJobsProgress.next(progress);
    const progressIncreaseUnit = 100 / jobs.length;

    concat(...jobs)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((response) => {
        if (response === 'finish') {
          // Finish saving loading job style.
          this.savedLoadingJobsStyle = dbLoadingJobs;

          // Finish saving graph schema style.
          this.savedGraphStyle = graphStyle;

          this.progressUnsubscribe.next();
          if (this.updateLoadingJobsProgressWindowRef) {
            this.updateLoadingJobsProgressWindowRef.close();
          }

          if (gsqlLoadingJobChange.isChanged) {
            // Finish saving GSQL loading job.
            this.savedLoadingJobs = gsqlLoadingJobs;
            this.snackBar.open(
              'Data mapping is updated',
              'DISMISS',
              { duration: 3000 }
            );
          } else {
            this.snackBar.open(
              'Data mapping style is updated',
              'DISMISS',
              { duration: 3000 }
            );
          }

          this.renderLeftChart(true);
          this.setWarningForUnsavedChanges();
          this.updateChartConfigs();
        } else {
          // Update progress.
          if (response === 'deleteLoadingJobs') {
            this.savedLoadingJobs = this.savedLoadingJobs.filter(loadingJob => {
              return gsqlLoadingJobChange.loadingJobNamesToDelete.find(loadingJobName =>
                loadingJobName === loadingJob.JobName) === undefined;
            });
          }

          progress += progressIncreaseUnit;
          this.updateLoadingJobsProgress.next(progress);
          if (progress >= 80) {
            this.progressUnsubscribe.next();
          }
        }
      },
        (err: HttpErrorResponse) => {
          this.progressUnsubscribe.next();
          if (this.updateLoadingJobsProgressWindowRef) {
            this.updateLoadingJobsProgressWindowRef.close();
          }

          this.handleError(err.error, err.statusText);
        });
  }

  /**
   * Save the GSQL loading job.
   *
   * @private
   * @param {Observable<any>[]} jobs
   * @param {GSQLLoadingJobJson[]} gsqlLoadingJobs
   * @returns {boolean}
   */
  private handleSaveGSQLLoadingJob(
    jobs: Observable<any>[],
    gsqlLoadingJobs: GSQLLoadingJobJson[],
  ): GSQLLoadingJobChange {
    const gsqlLoadingJobChange = GSQLLoadingJobLogic.getGSQLLoadingJobChange(
      this.savedLoadingJobs, gsqlLoadingJobs
    );

    this.publishDataMappingTime = 0;

    if (gsqlLoadingJobChange.loadingJobNamesToDelete.length > 0) {
      // Collect the job that delete GSQL loading jobs and swallow the errors.
      jobs.push(
        this.loadingBuilderService.deleteLoadingJobs(
          this.graphName,
          gsqlLoadingJobChange.loadingJobNamesToDelete,
        ).pipe(map(() => 'deleteLoadingJobs'), catchError((err) => {
          this.logger.error(err.error);
          return of('deleteLoadingJobs');
        }))
      );

      // Assume each deletion job takes about 3 secs.
      this.publishDataMappingTime = 2 * 3;
    }

    if (gsqlLoadingJobChange.loadingJobsToAdd.length > 0) {
      // Collect the job that creates GSQL loading jobs.
      jobs.push(
        this.loadingBuilderService.saveLoadingJobs(
          gsqlLoadingJobChange.loadingJobsToAdd
        )
      );

      // Assume each creation job takes about 4 secs.
      this.publishDataMappingTime += 2 * 4;
    }

    return gsqlLoadingJobChange;
  }

  /**
   * Delete a loading statement or mapping.
   *
   * @param {ExternalNode | ExternalLink} item
   * @memberof LoadingBuilderComponent
   */
  confirmDelete(item: ExternalNode | ExternalLink) {
    const dataType = 'id' in item ? 'data source' : 'data mapping';
    const data: DialogData = {
      title: 'Warning',
      messages: [
        `Are you sure you want to delete the ${dataType}?`
      ],
      actions: [
        { label: 'CANCEL', value: 0 },
        { label: 'DELETE', value: 1, color: 'warn' }
      ]
    };
    const dialogRef = this.dialog
      .open(DialogComponent, { data: data, autoFocus: false });

    dialogRef
      .keydownEvents()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(event => {
        if (event.key.toLowerCase() === 'enter') {
          dialogRef.close(1);
        }
      });

    dialogRef
      .afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(value => {
        if (value === 1) {
          this.leftChartGraphRef.current?.unselectElements();
          if ('id' in item && dataSourceNodeType.includes(item.type)) {
            this.leftChartGraphRef.current?.selectNodes([item]);
          } else if ('source' in item && item.type.includes(dataSourceEdgeType)) {
            this.leftChartGraphRef.current?.selectEdges([item]);
          }
          this.handleDelete();
        }
      });
  }

  /**
   * Handle delete loading statements and mapping.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private handleDelete() {
    /**
     * Delete the selected loading jobs and statements.
     * Then re-render the left chart.
     */
    const loadingJobIndices: number[] = [];
    const loadingStatementIndices: [number, number][] = [];
    const selection = this.leftChartGraphRef?.current?.selectedElements();
    if (selection) {
      selection.nodes.forEach(node => {
        if (dataSourceNodeType.includes(node.type)) {
          loadingJobIndices.push(+node.id);
        }
      });
      selection.links.forEach(link => {
        if (link.type.includes(dataSourceEdgeType)) {
          loadingStatementIndices.push([
            +link.source.id,
            +link.others.statementId
          ]);
        }
      });
    }

    // Only do deletion if there is something to delete.
    if (loadingJobIndices.length > 0 || loadingStatementIndices.length > 0) {
      this.logicService.deleteLoadingJobsAndLoadingStatements(
        loadingJobIndices,
        loadingStatementIndices
      );
      this.renderLeftChart(true, true);
      this.renderRightChart();
      this.setWarningForUnsavedChanges();
    }
  }

  /**
   * Update schema and data source positions for left chart.
   *
   * @private
   * @param {(GraphChartDataNode | GraphChartDataLink)} element
   * @memberof LoadingBuilderComponent
   */
  private updateVerticesPositions(
    element?: GraphChartDataNode | GraphChartDataLink
  ) {
    if (!element && this.isMovingLeft) {
      const positions: [string, number, number][] = this.leftChartGraphRef?.current?.graphPositions();
      const vertexTypes = this.logicService.graph.getAllVertexTypes();
      const vertexPositions: [string, number, number][] = [];
      const dataSourcePositions: [number, number, number][] = [];

      positions.forEach(p => {
        const [type, id] = p[0].split('#');
        if (vertexTypes.includes(type)) {
          vertexPositions.push([type, p[1], p[2]]);
        } else if (dataSourceNodeType.includes(type)) {
          dataSourcePositions.push([+id, p[1], p[2]]);
        }
      });

      this.logicService.updateDataSourceAndVertexPositions(
        dataSourcePositions,
        vertexPositions
      );
    }

    this.isMovingLeft = false;
  }

  /** Check if there is file type loading job. */
  private hasFileTypeLoadingJob(): boolean {
    return this.leftVisService
      .filterLoadingJobBasedOnType(this.logicService.loadingJobs, 'file').length > 0;
  }

  /**
   * Render the left chart through vis service.
   *
   * @private
   * @param {boolean} hasGraphStyle
   * @memberof LoadingBuilderComponent
   */
  private renderLeftChart(hasGraphStyle: boolean, forceUpdateLoadingStatus?: boolean) {
    // Debounce rendering when changing quickly
    if (this.renderLeftChartTimer) {
      clearTimeout(this.renderLeftChartTimer);
    }
    this.renderLeftChartTimer = setTimeout(() => {
      this.forceUpdateLoadingStatusTimer = setTimeout(() => {
        if (this.forceUpdateLoadingStatusTimer) {
          clearTimeout(this.forceUpdateLoadingStatusTimer);
        }
        if (forceUpdateLoadingStatus) {
          this.schema = this.schema.clone();
        }
        this.forceUpdateLoadingStatus = !!forceUpdateLoadingStatus;
      });
      this.renderLeftChartOnce(hasGraphStyle);
    }, 200);
    this.selectedLoadingJobs = [];
    if (this.canWriteLoadingJob) {
      this.enableDrawMode();
    }
  }

  /**
   * Render left chart once.
   *
   * @private
   * @param {boolean} hasGraphStyle
   * @memberof LoadingBuilderComponent
   */
  private renderLeftChartOnce(hasGraphStyle: boolean) {
    let loadingJobs = cloneDeep(this.logicService.loadingJobs);
    if (this.leftVisService.filterLoadingJobBasedOnType(loadingJobs, 'file').length > 0) {
      loadingJobs = loadingJobs.map(loadingJob => {
        switch (loadingJob.dataSource.type) {
          case DataSourceType.File:
            const fileInfo = this.fileInfoMap.get(loadingJob.dataSet.uri);
            if (fileInfo) {
              loadingJob.dataSet.info = fileInfo;
            } else {
              delete loadingJob.dataSet['info'];
            }
            break;
        }
        return loadingJob;
      });
    }

    // Render the chart twice after 200ms to make sure edge has correct positions
    this.leftVisService.renderChart(
      this.ngUnsubscribe,
      this.logicService.graph,
      hasGraphStyle,
      loadingJobs,
      this.logicService.semanticCheckResults,
      this.logicService.currentLoadingStatementIndex !== undefined
        ? this.logicService.currentLoadingJobIndex +
        '_' +
        this.logicService.currentLoadingStatementIndex
        : undefined
    );
    this.putLoadingStatusTagOnEachLoadingJob();
    this.schema = this.logicService.graph;
  }

  /**
   * Render the right chart through vis service.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private renderRightChart() {

    this.rightVisService.renderChart(
      this.logicService.currentDataSourceTitleHeaderAndSampleData,
      this.logicService.currentDataFormat,
      this.logicService.currentDataSchema,
      this.logicService.currentLoadingStatement,
      this.logicService.graph
    );

    const disableRightChartButtons =
      this.logicService.currentLoadingStatement === undefined;
    if (
      !this.isDataMapping &&
      this.canWriteLoadingJob
    ) {
      // Enable token function, auto mapping and data filter if user has the related privilege.
      this.mappingToolbarConfig.top[MappingToolbarButtons.AddTokenFunction].disabled = disableRightChartButtons;
      this.mappingToolbarConfig.top[MappingToolbarButtons.AutoMapping].disabled = disableRightChartButtons;
      this.mappingToolbarConfig.top[MappingToolbarButtons.WhereClause].disabled = disableRightChartButtons;
      this.mappingToolbarConfig.top[MappingToolbarButtons.More].disabled = disableRightChartButtons;
    }

    // Show hint or not depending on right chart empty or not
    this.showRightHint.next(disableRightChartButtons);
  }

  /**
   * Show the user the error through dialog.
   *
   * @private
   * @param {string | string[]} message either single-line or multi-line message
   * @param {string} [title]
   *
   * @memberof LoadingBuilderComponent
   */
  private handleError(message: string | string[], title?: string): MatDialogRef<DialogComponent, any> {
    if (typeof message === 'string') {
      message = [message];
    }
    const data: DialogData = {
      title: title ? title : 'Error',
      messages: message
    };
    return this.dialog.open(DialogComponent, { data: data });
  }

  /**
   * Toggle focus on a selected button.
   *
   * @private
   * @param {string} key
   * @memberof LoadingBuilderComponent
   */
  private toggleButtonFocus(key: string) {
    const button = <ButtonBase>this.toolbarConfig.top.find(b => b.key === key);
    button.color = (button.color === undefined || button.color === '') ? 'accent' : '';
  }

  /**
   * Activate or deactivate one button.
   *
   * @private
   * @param {ToolbarConfig} toolbar
   * @param {string} key
   * @param {boolean} activated
   * @memberof LoadingBuilderComponent
   */
  private setButtonActivated(
    toolbar: ToolbarConfig,
    key: string,
    activated: boolean
  ) {
    const button = <ButtonBase>toolbar.top.find(b => b.key === key);
    button.color = activated ? 'accent' : '';
  }

  /**
   * Set current loading status.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private setLoadingStatus(status: boolean) {
    this.isLoading = status;
    this.loading.next(this.isLoading);
  }

  /**
   * Attach clean up signal for the subjects.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private attachSubjectCleanUp() {
    this.loading.pipe(takeUntil(this.ngUnsubscribe));
    this.showLeftHint.pipe(takeUntil(this.ngUnsubscribe));
    this.showRightHint.pipe(takeUntil(this.ngUnsubscribe));
    this.authorizationWarning.pipe(takeUntil(this.ngUnsubscribe));
    this.unsavedWarning.pipe(takeUntil(this.ngUnsubscribe));

    this.fileList$.pipe(takeUntil(this.ngUnsubscribe));
    this.uploadStatus.pipe(takeUntil(this.ngUnsubscribe));
    this.uploadProgress.pipe(takeUntil(this.ngUnsubscribe));
    this.progressUnsubscribe.pipe(takeUntil(this.ngUnsubscribe));
  }

  /**
   * Create a default data parser selection.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private initializeDataParsers() {
    this.tabularParser = new TabularParser();
    this.jsonParser = new JSONParser();
    this.tabularSampleData = undefined;
    this.jsonSampleData = undefined;
  }

  /**
   * Clean the data vertex.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private initializeDataMapping() {
    this.dataVertex = undefined;
  }

  /**
   * Clean the mapping source and target.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private initializeLoadingMapping() {
    this.mappingSource = undefined;
    this.mappingTarget = undefined;
  }

  /**
   * Create default token function.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private initializeTokenFunction() {
    combinedTokenFunctionList.splice(0, combinedTokenFunctionList.length,
      ...builtinTokenFunctionNames.map(tokenName => <TokenFunctionSignature>(
        {
          name: tokenName,
          returnType: builtinTokenFunctionReturnType.get(tokenName),
          paramNumber: builtinTokenFunctionParamNumber.get(tokenName) || 1,
          paramNumberFixed: builtinTokenFunctionParamNumber.get(tokenName) !== undefined,
          doc: builtinTokenFunctionDocument.get(tokenName),
        }
      )));
    this.combinedTokenFunctionList.next(combinedTokenFunctionList);
    this.selectedTokenFunction = combinedTokenFunctionList[0];

    this.loadingBuilderService.getUserDefinedTokenFunctions()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(
        results => {
          combinedTokenFunctionList.splice(combinedTokenFunctionList.length, 0,
            ...results.map(GSQLTokenFunction => <TokenFunctionSignature>(
              {
                name: GSQLTokenFunction.name,
                returnType: [GSQLTokenFunction.returnType.toUpperCase()],
                paramNumber: GSQLTokenFunction.paramNumber || 1,
                paramNumberFixed: GSQLTokenFunction.paramNumber !== undefined,
                code: GSQLTokenFunction.code
              }
            )));
          this.combinedTokenFunctionList.next(combinedTokenFunctionList);
        },
        (err: HttpErrorResponse) => this.handleError(err.error, err.statusText)
      );
  }

  /**
   * Save the current toolbar disabled status.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private saveToolbarDisabledStatus() {
    this.toolbarDisabledStatus = this.toolbarConfig.top.map(
      button => button.disabled
    );
  }

  /**
   * Load the current toolbar disabled status.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private loadToolbarDisabledStatus() {
    this.toolbarConfig.top.forEach((button, i) => {
      button.disabled = this.toolbarDisabledStatus[i];
    });
  }

  /**
   * Update chart configs based on the theme.
   *
   * @memberof LoadingBuilderComponent
   */
  private updateChartConfigs() {
    this.bus.from<AppTheme>('AppTheme')
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(theme => {
        this.currentTheme = theme.isDark ? 'dark' : 'light';
        Chart.defaults.global.defaultFontColor = theme.isDark ? 'rgb(225, 225, 225)' : 'rgb(0, 0, 0)';
        this.setChartTheme(theme.isDark);
      });
  }

  /**
   * Configurations for charts based on theme.
   *
   * @param {boolean} isDarkTheme
   * @memberof LoadingBuilderComponent
   */
  private setChartTheme(isDarkTheme: boolean) {
    const lineChartColor = isDarkTheme ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)';
    const textColor = isDarkTheme ? 'white' : 'black';
    if (this.graphStatisticalTrendLineChart !== undefined) {
      this.graphStatisticalTrendLineChart.destroy();
    }
    const chartContainer = this.graphStatisticalTrendContainer;

    this.graphStatisticalTrendLineChart = new Chart(
      chartContainer.nativeElement,
      this.loadingExecutorService.setGraphStatisticTrendChartTheme(isDarkTheme)
    );

    this.addGraphStatisticTrendChartData();

    this.loadingSpeedChart.options.legend.labels.fontColor = textColor;
    this.loadingSpeedChart.options.scales.xAxes[0].gridLines.color = lineChartColor;
    this.loadingSpeedChart.options.scales.yAxes[0].gridLines.color = lineChartColor;
    this.loadingSpeedChart.options.scales.yAxes[0].ticks.fontColor = textColor;
    this.loadingSpeedChart.options.scales.xAxes[0].ticks.fontColor = textColor;
    this.loadingSpeedChart.update();

    this.dataLinesChart.update();

    this.leftVisService.ChartConfig = this.leftVisService.ChartConfigs.get(this.currentTheme);
    this.rightVisService.ChartConfig = this.rightVisService.ChartConfigs.get(this.currentTheme);
    this.renderLeftChartOnce(true);
    this.renderRightChart();
  }

  /**
   * Handle uploading json key.
   *
   * @param {FileList} files
   * @return {*}
   * @memberof LoadingBuilderComponent
   */
  handleDataSourceAuthFileUpload(files: FileList) {
    if (files.length !== 1) {
      return;
    }

    const file = files.item(0);
    let errMsg: string;
    if (file.size > 5120) {
      // Limit file size to 5KB.
      errMsg = 'The selected file is too large.';
    } else if (file.type !== 'application/json') {
      errMsg = 'The selected file is not a json file.';
    }

    if (errMsg) {
      this.handleError(errMsg);
      return;
    }

    this.gcsServiceAccountKeyFile = file;
    const reader = new FileReader();

    reader.onload = (() => {
      if (!reader.result) {
        this.setLoadingStatus(false);
        this.handleError('Sorry, we can\'t upload the file. Please try again.');
        return;
      }

      this.gcsServiceAccountKeyFileContent = <string>reader.result;
      this.loadingBuilderService.checkDataSourceCredentialFormat(
        this.selectedDataSourceType,
        this.gcsServiceAccountKeyFileContent
      )
        .pipe(
          takeUntil(this.ngUnsubscribe),
          finalize(() => this.setLoadingStatus(false))
        )
        .subscribe(() => {
          this.gcsServiceAccountKeyFormatIsValid = true;
        },
          (err: HttpErrorResponse) => {
            this.gcsServiceAccountKeyFormatIsValid = false;
          }
        );
      this.setLoadingStatus(false);
    });

    this.setLoadingStatus(true);
    reader.readAsText(this.gcsServiceAccountKeyFile);
  }

  /**
   * Changes color of drop area background when there is a drag over event
   * on it.
   *
   * @param {event}
   * @memberof LoadingBuilderComponent
   */
  onDragOver(event: any) {
    event.preventDefault();
    event.stopPropagation();
    this.dropArea.nativeElement.style.background = '#999999';
  }

  /**
   * Changes color of drop area background when there is a drag leave
   * event on it.
   *
   * @param {event}
   * @memberof LoadingBuilderComponent
   */
  onDragLeave(event: any) {
    event.preventDefault();
    event.stopPropagation();
    this.dropArea.nativeElement.style.background = 'transparent';
  }

  /**
   * Changes color of drop area when a there is a drop event on it.
   *
   * @memberof LoadingBuilderComponent
   */
  onDrop() {
    this.dropArea.nativeElement.style.background = 'transparent';
  }

  /**
   * Pop up the constant literal input window and check if the input is valid.
   *
   * @private
   * @param {Node} node
   * @param {number} y
   * @memberof LoadingBuilderComponent
   */
  private onPortClick(node: Node, y: number) {
    if (this.portClickCounter === 1) {
      // Treat the second continous click as a double click event.
      const { y: nodeY } = node.position();
      this.literalTarget = this.rightVisService.handleConstantInput(node, y - nodeY);

      if (this.literalTarget === undefined) {
        return;
      }

      const validationCheck = this.logicService.checkMappingStringLiteralToTarget(this.literalTarget);
      if (validationCheck.success) {
        this.inputConstString = this.setInitialLiteral(
          this.logicService.getCurrentLoadingDetail(this.literalTarget)
        );
        this.onInteraction({ key: 'addConstMapping' });
      } else {
        this.handleError(validationCheck.message);
      }
    } else {
      // This is the first click event.
      this.portClickCounter = 1;
      setTimeout(() => {
        this.portClickCounter = 0;
      }, DOUBLE_CLICK_FREQUENCY);
    }
  }

  /**
   * When mouse hovers over the node, add the delete button.
   *
   * @private
   * @param {Node} node
   * @memberof LoadingBuilderComponent
   */
  private onNodeHover(node: Node) {
    node.addTools({
      name: 'button-remove',
      args: {
        distance: '50%',
        onClick: this.onClickNodeDeleteButton.bind(this)
      }
    });
  }

  /**
   * When click on the delete button on a widget, remove the widget.
   *
   * @private
   * @param {{view: NodeView}} {view}
   * @memberof LoadingBuilderComponent
   */
  private onClickNodeDeleteButton({ view }: { view: NodeView }) {
    this.rightChart.removeCell(view.cell.id);
    if (view.isNodeView) {
      this.logicService.removeMappingWidgets([+view.cell.id]);
    }
  }

  /**
   * When mouse hovers over the edge, add the delete button.
   *
   * @private
   * @param {Edge} edge
   * @param {number} x
   * @param {number} y
   * @memberof LoadingBuilderComponent
   */
  private onEdgeHover(edge: Edge, x: number, y: number) {
    let distance: string;
    let onClickFunction: Function;

    if (edge.id === 'dataFilterEdge') {
      distance = '85%';
      onClickFunction = this.onClickDataFilterEdgeDeleteButton;

      this.rightVisService.showTooltip(
        parseExprJson(
          this.logicService.currentLoadingStatement.style.whereClauseJson,
          this.logicService.graph
        ).toString(),
        x,
        y
      );
    } else {
      distance = '90%';
      onClickFunction = this.onClickEdgeDeleteButton;
    }

    edge.addTools({
      name: 'button-remove',
      args: {
        distance: distance,
        onClick: onClickFunction.bind(this),
      },
    });
  }

  /**
   * When click on the delete button on the data filter edge, remove the edge
   * and reconnect edges to data source table.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private onClickDataFilterEdgeDeleteButton() {
    this.logicService.addWhereClause(<BaseCondition>parseExprJson({
      type: 'NullCondition',
      operands: []
    }));
    this.renderRightChart();
  }

  /**
   * When click on the delete button on an edge, remove the edge.
   *
   * @private
   * @param {{view: EdgeView}} {view}
   * @memberof LoadingBuilderComponent
   */
  private onClickEdgeDeleteButton({ view }: { view: EdgeView }) {
    this.rightChart.removeCell(view.cell.id);
    const currentEdge = view.cell;
    const targetCellId = currentEdge.getTargetCellId();
    const targetPortId = currentEdge.getTargetPortId();

    this.logicService.removeLoadingMappings(
      [
        {
          type: targetCellId === DataMappingConifg.graphEntityCellId ?
            OneColumnMappingTargetType.GraphEntity :
            OneColumnMappingTargetType.MappingWidget,
          mappingWidgetIndex: +targetCellId,
          mappingIndex: +targetPortId,
          paramIndex: +targetPortId,
        }
      ]
    );
    this.renderLeftChart(true);
  }

  /**
   * Trigger enable 'NEXT button, used for UI e2e test.
   *
   * @memberof LoadingBuilderComponent
   */
  enableNextButtonInE2Etest() {
    this.gcsServiceAccountKeyFile = new File([], 'test.json', { type: 'application/json' });
    this.gcsServiceAccountKeyFormatIsValid = true;
  }


  private centerGraph() {
    timer(800)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.leftChartGraphRef?.current?.centerGraph();
      });
  }

  /**
   * Enable the draw mode in the new graph.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private enableDrawMode(enable = true) {
    timer(100)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.leftChartGraphRef.current?.setDrawMode(enable);
      });
  }

  // Start loading executor

  private initialGraphStatisticalTrendLineChart() {
    if (this.graphStatisticalTrendLineChart !== undefined) {
      this.graphStatisticalTrendLineChart.destroy();
    }
    const chartContainer = this.graphStatisticalTrendContainer;
    this.graphStatisticalTrendLineChart = new Chart(
      chartContainer.nativeElement.getContext('2d'),
      this.graphStatisticalTrendLineChartConfig
    );
  }

  /**
   * Adjust elements' appearance based on current user's privileges.
   *
   * @memberof LoadingBuilderComponent
   */
  adjustElementAppearancesBaseOnUserPrivilegeOfLoadingExecutor() {
    let warningText = '';

    // Disable start loading button for the user without 'EXECUTE_LOADINGJOB' privilege.
    if (!this.authService.hasPrivilege(GSQLPrivilege.ExecuteLoadingJob)) {
      this.toolbarConfig.top[ToolbarButtons.StartLoading].disabled = true;
      this.toolbarConfig.top[ToolbarButtons.PauseLoading].disabled = true;
      this.toolbarConfig.top[ToolbarButtons.StopLoading].disabled = true;
      warningText = 'load data';
    }

    if (warningText) {
      this.warning.next(`You don't have privilege to ${warningText} for ` +
        `${this.authService.getCurrentGraph()}.`);
    }
  }

  /**
   * Get the background color of different status.
   *
   * @param {string} status
   * @returns {string}
   * @memberof LoadingBuilderComponent
   */
  getBackgroundOfStatus(status: string): string {
    switch (status) {
      case 'RUNNING': {
        return this.currentTheme === 'dark' ? StatusBackgroundColorDark.Running : StatusBackgroundColorLight.Running;
      }
      case 'PAUSED': {
        return this.currentTheme === 'dark' ? StatusBackgroundColorDark.Paused : StatusBackgroundColorLight.Paused;
      }
      case 'FINISHED': {
        return this.currentTheme === 'dark' ? StatusBackgroundColorDark.Finished : StatusBackgroundColorLight.Finished;
      }
      case 'FAILED': {
        return this.currentTheme === 'dark' ? StatusBackgroundColorDark.Failed : StatusBackgroundColorLight.Failed;
      }
      default: {
        return this.currentTheme === 'dark' ? StatusBackgroundColorDark.Others : StatusBackgroundColorLight.Others;
      }
    }
  }

  getColorOfStatus(status: string): string {
    switch (status) {
      case 'RUNNING': {
        return this.currentTheme === 'dark' ? '#fff' : StatusColorLight.Running;
      }
      case 'PAUSED': {
        return this.currentTheme === 'dark' ? '#fff' : StatusColorLight.Paused;
      }
      case 'FINISHED': {
        return this.currentTheme === 'dark' ? '#fff' : StatusColorLight.Finished;
      }
      case 'FAILED': {
        return this.currentTheme === 'dark' ? '#fff' : StatusColorLight.Failed;
      }
      default: {
        return this.currentTheme === 'dark' ? '#fff' : StatusColorLight.Others;
      }
    }
  }

  /**
   * Set style of loading statistics chart.
   *
   * @param {string} key
   * @returns
   * @memberof LoadingBuilderComponent
   */
  setLoadingStatisticStyle(key: string) {
    const loaded =
      this.currentLoadingProgress.loadedLines !== undefined &&
      !this.currentLoadingProgress.message;
    switch (key) {
      case 'flex':
        if (loaded) {
          return '30%';
        } else {
          return '0%';
        }
      case 'padding':
        if (loaded) {
          return undefined;
        } else {
          return '0';
        }
    }
  }

  /**
   * Update the right side statistics panel.
   *
   * @private
   * @param {(GraphChartDataNode | GraphChartDataLink)} item
   * @memberof LoadingBuilderComponent
   */
  updateStatisticsPanel(item: GraphChartDataNode | GraphChartDataLink) {
    if (item && 'jobName' in item.others) {
      this.currentStatisticsTab = StatisticsTab.LoadingStatistics;
      this.currentLoadingJob = item.others['jobName'];
      if (this.reflow) {
        this.showSingleLoadingStatsReflow = true;
      }
    } else {
      this.currentStatisticsTab = StatisticsTab.GraphStatistics;
      this.currentLoadingJob = undefined;
    }
    this.switchLoadingProgress();
  }

  private setToolbarMode() {
    const jobProgresses: LoadingProgress[] = [];
    this.selectedLoadingJobs.forEach(job => {
      const progress = this.getLatestLoadingProgress(job.others.jobName);
      if (progress !== undefined) {
        jobProgresses.push(progress);
      }
    });
    if (this.selectedLoadingJobs.length === 0) {
      this.loadingProgressMap.forEach(jobProgress => {
        if (jobProgress.length > 0) {
          jobProgresses.push(jobProgress[jobProgress.length - 1].progress);
        }
      });
    }
    let hasRunningJobs = false, hasPausedJobs = false, allJobsRunning = true;
    jobProgresses.forEach(progress => {
      hasRunningJobs = hasRunningJobs || (progress.status === VolatileState.Running);
      hasPausedJobs = hasPausedJobs || (progress.status === TerminalState.Paused);
      allJobsRunning = allJobsRunning && (progress.status === VolatileState.Running);
    });
    const loadingJobChange = this.getLoadingJobChange();
    this.disablePauseLoading = !hasRunningJobs;
    this.disableStopLoading = !(hasRunningJobs || hasPausedJobs);
    this.disableStartLoading = allJobsRunning ||
      loadingJobChange.status === LoadingJobChangeStatus.DataMappingChange ||
      this.isDataMapping;

    // Update toolbar
    this.toolbarConfig.top[ToolbarButtons.StartLoading].disabled = this.disableStartLoading;
    this.toolbarConfig.top[ToolbarButtons.PauseLoading].disabled = this.disablePauseLoading;
    this.toolbarConfig.top[ToolbarButtons.StopLoading].disabled = this.disableStopLoading;

    this.adjustElementAppearancesBaseOnUserPrivilegeOfLoadingExecutor();
  }

  /**
   * Handler for start event.
   * Ask the the user for confirmation.
   * If agree, call start loading.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private handleStartAndResume() {
    // Get those loading jobs with loading statements.
    let loadingJobs: LoadingJobData[];
    if (this.selectedLoadingJobs.length !== 0) {
      loadingJobs = this.logicService.loadingJobs.filter(loadingJob => {
        return (
          loadingJob.loadingStatements.length > 0 &&
          findIndex(
            this.selectedLoadingJobs,
            element => element.others.jobName === loadingJob.jobName
          ) !== -1
        );
      });
    } else {
      loadingJobs = this.logicService.loadingJobs.filter(
        loadingJob => loadingJob.loadingStatements.length > 0
      );
    }

    if (loadingJobs.length === 0) {
      this.handleError('Please create data mapping at Map Data To Graph page first.',
        'Nothing to start');
    } else {
      // Split loading jobs to start or resume based on their latest progress status.
      const loadingJobsToStart: LoadingJobData[] = [];
      const loadingJobsToResume: LoadingJobData[] = [];
      loadingJobs.forEach(loadingJob => {
        const progress = this.getLatestLoadingProgress(loadingJob.jobName);
        if (!progress) {
          return;
        }
        const latestProgressStatus = progress.status;
        if (latestProgressStatus === TerminalState.Paused) {
          loadingJobsToResume.push(loadingJob);
        } else if (Object.values(TerminalState).includes(progress.status as TerminalState)) {
          loadingJobsToStart.push(loadingJob);
        }
      });
      const toStartMessage =
        loadingJobsToStart.map(
          loadingJob =>
            ' - Load "' +
            loadingJob.dataSet.uri +
            '" to ' +
            loadingJob.loadingStatements
              .map(
                loadingStatement =>
                  'vertexName' in loadingStatement
                    ? 'vertex "' +
                    (<LoadToVertexData>loadingStatement).vertexName +
                    '"'
                    : 'edge "' +
                    (<LoadToEdgeData>loadingStatement).edgeName +
                    '"'
              )
              .join(', ') +
            '.'
        );
      const toResumeMessage =
        loadingJobsToResume.map(
          loadingJob =>
            ' - Load "' +
            loadingJob.dataSet.uri +
            '" to ' +
            loadingJob.loadingStatements
              .map(
                loadingStatement =>
                  'vertexName' in loadingStatement
                    ? 'vertex "' +
                    (<LoadToVertexData>loadingStatement).vertexName +
                    '"'
                    : 'edge "' +
                    (<LoadToEdgeData>loadingStatement).edgeName +
                    '"'
              )
              .join(', ') +
            '.'
        );
      const message = ['These data files will be loaded:']
        .concat(toStartMessage)
        .concat(['These data loading procedures will be resumed:'])
        .concat(toResumeMessage)
        .concat(['Do you want to continue?']);
      const data: DialogData = {
        title: 'Confirm',
        messages: message,
        actions: [
          { label: 'CANCEL', value: 0 },
          { label: 'CONTINUE', value: 1, color: 'primary' }
        ]
      };

      const dialogRef = this.dialog
        .open(DialogComponent, { data: data, autoFocus: false });

      dialogRef
        .keydownEvents()
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe(event => {
          if (event.key.toLowerCase() === 'enter') {
            dialogRef.close(1);
          }
        });

      dialogRef
        .afterClosed()
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe(value => {
          if (value === 1) {
            // Clear the current logs.
            this.cleanUpLogStream();
            this.logs.clear();
            // Wait for loading jobs to start.
            this.setLoadingStatus(true);

            // If there are any jobs to resume, mark event as "Resume Loading" otherwise
            // mark as "Start Loading"
            this.loadingExecutorService.logStartOrResumeLoading(!loadingJobsToResume.length);

            // Start the loading jobs.
            forkJoin([
              this.loadingExecutorService.startLoadingJob(this.graphName, loadingJobsToStart),
              this.loadingExecutorService.resumeLoadingJob(this.graphName, loadingJobsToResume.map(job => job.jobName))
            ])
              .pipe(
                takeUntil(this.ngUnsubscribe),
                finalize(() => this.setLoadingStatus(false))
              )
              .subscribe(
                () => this.getLoadingProgress(),
                (err: HttpSourceErrorResponse | string) => {
                  if (typeof err === 'string') {
                    this.handleError(err, 'Error');
                    return;
                  }
                  this.handleError(err.error, err.statusText);
                }
              );
          }
        });
    }
  }

  private handlePause() {
    let loadingJobs: LoadingJobData[];
    if (this.selectedLoadingJobs.length !== 0) {
      loadingJobs = this.logicService.loadingJobs.filter(loadingJob => {
        return (
          loadingJob.loadingStatements.length > 0 &&
          findIndex(
            this.selectedLoadingJobs,
            element => element.others.jobName === loadingJob.jobName
          ) !== -1
        );
      });
    } else {
      loadingJobs = this.logicService.loadingJobs.filter(
        loadingJob => loadingJob.loadingStatements.length > 0
      );
    }
    loadingJobs = loadingJobs.filter(loadingJob => {
      const progress = this.getLatestLoadingProgress(loadingJob.jobName);
      return progress && progress.status === VolatileState.Running;
    });
    this.setLoadingStatus(true);
    this.loadingExecutorService
      .pauseLoadingJob(this.graphName, loadingJobs.map(loadingJob => loadingJob.jobName))
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.setLoadingStatus(false))
      )
      .subscribe(
        () => this.getLoadingProgress(),
        (err: HttpSourceErrorResponse | string) => {
          if (typeof err === 'string') {
            this.handleError(err, 'Error');
            return;
          }
          this.handleError(err.error, err.statusText);
        }
      );
  }

  /**
   * Handler for stop event.
   * Ask the the user for confirmation.
   * If agree, call stop loading.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private handleStop() {
    const data: DialogData = {
      title: 'Warning',
      messages: [
        'If you stop data loading, next time data loading will start from beginning point.',
        'Do you want to continue?'
      ],
      actions: [
        { label: 'CANCEL', value: 0 },
        { label: 'CONTINUE', value: 1, color: 'warn' }
      ]
    };

    const dialogRef = this.dialog
      .open(DialogComponent, { data: data, autoFocus: false });

    dialogRef
      .keydownEvents()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(event => {
        if (event.key.toLowerCase() === 'enter') {
          dialogRef.close(1);
        }
      });

    dialogRef
      .afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(value => {
        if (value === 1) {
          // Wait for loading jobs to stop.
          this.setLoadingStatus(true);

          // Stop the loading jobs.
          let loadingJobs: LoadingJobData[];
          if (this.selectedLoadingJobs.length !== 0) {
            loadingJobs = this.logicService.loadingJobs.filter(loadingJob => {
              return (
                loadingJob.loadingStatements.length > 0 &&
                findIndex(
                  this.selectedLoadingJobs,
                  element => element.others.jobName === loadingJob.jobName
                ) !== -1
              );
            });
          } else {
            loadingJobs = this.logicService.loadingJobs.filter(
              loadingJob => loadingJob.loadingStatements.length > 0
            );
          }
          loadingJobs = loadingJobs.filter(loadingJob => {
            const progress = this.getLatestLoadingProgress(loadingJob.jobName);
            return progress && (progress.status === VolatileState.Running || progress.status === TerminalState.Paused);
          });
          this.loadingExecutorService
            .stopLoadingJob(this.graphName, loadingJobs.map(job => job.jobName))
            .pipe(
              takeUntil(this.ngUnsubscribe),
              finalize(() => this.setLoadingStatus(false))
            )
            .subscribe(
              () => this.getLoadingProgress(),
              () => this.handleError('Stop loading job failed.')
            );
        }
      });
  }

  /**
   * Initialize the loading progress.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private initLoadingProgress() {
    if (this.loadingSpeedChart !== undefined) {
      this.loadingSpeedChart.destroy();
      this.loadingSpeedChart = new Chart(this.loadingSpeedTrendContainer.nativeElement, this.loadingSpeedChartConfig);
    }
    this.loadingProgressMap.clear();
    this.initialLoadingProgress = true;
    this.currentLoadingJob = undefined;
    this.switchLoadingProgress();
  }

  /**
   * Get the latest loading progress of a given job.
   *
   * @private
   * @param {string} job
   * @returns {LoadingProgress}
   * @memberof LoadingBuilderComponent
   */
  private getLatestLoadingProgress(job: string): LoadingProgress {
    const progresses = this.loadingProgressMap.get(job);
    if (progresses !== undefined && progresses.length > 0) {
      return progresses[progresses.length - 1].progress;
    }
  }

  /**
   * Switch the loading progress the user is currently viewing.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private switchLoadingProgress() {
    // When switch loading job, switch loading speed status and reset current loading progress.
    const progresses = this.loadingProgressMap.get(this.currentLoadingJob) || [];

    this.loadingSpeedChart.data.labels = [];
    if (this.loadingSpeedChart.data.datasets && this.loadingSpeedChart.data.datasets[0]) {
      this.loadingSpeedChart.data.datasets[0].data = [];
    }
    this.loadingSpeedChart.update();
    progresses.map(progress => this.generateLoadingSpeedData(progress, progresses));

    if (this.currentLoadingJob === undefined) {
      this.currentLoadingProgress = {
        source: 'No source selected.',
        status: 'N/A'
      };
    } else {
      const selectedJob = this.logicService.loadingJobs.find(job => job.jobName === this.currentLoadingJob);
      if (selectedJob === undefined) {
        return;
      }
      // If the job is not mapped to any vertex or edge type, then status should be DATA NOT MAPPED
      const label = DataSetLogic.createDataSetLabel(
        selectedJob.isChanged,
        selectedJob.dataSet.uri
      );
      if (selectedJob.loadingStatements.length === 0) {
        this.currentLoadingProgress = {
          source: label,
          status: 'DATA NOT MAPPED'
        };
      } else {
        this.currentLoadingProgress = this.getLatestLoadingProgress(selectedJob.jobName);
        if (this.currentLoadingProgress === undefined) {
          this.currentLoadingProgress = {
            source: label,
            status: 'NO DATA YET'
          };
        }
      }
    }

    this.renderCurrentLoadingProgress();
  }

  private roundPercentage(num: number) {
    return Math.round(num * 100) / 100;
  }

  /**
   * Render currently selected loading progress in the right panel.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private renderCurrentLoadingProgress() {
    // Overall table view.
    this.loadingProgressDatabase.addLoadingProgressData(this.currentLoadingProgress);

    // Data lines distribution chart.
    const distributionData = [
      {
        name: 'Loaded',
        value: this.currentLoadingProgress.loadedLines || 0,
        percentage: this.roundPercentage((100 * this.currentLoadingProgress.loadedLines
          / ( this.currentLoadingProgress.loadedLines
            + this.currentLoadingProgress.notEnoughTokenLines
            + this.currentLoadingProgress.oversizeTokenLines )
          ) || 0)
      },
      {
        name: 'Missing Token',
        value: this.currentLoadingProgress.notEnoughTokenLines || 0,
        percentage: this.roundPercentage((100 * this.currentLoadingProgress.notEnoughTokenLines
          / ( this.currentLoadingProgress.loadedLines
            + this.currentLoadingProgress.notEnoughTokenLines
            + this.currentLoadingProgress.oversizeTokenLines)
          ) || 0)
      },
      {
        name: 'Oversize',
        value: this.currentLoadingProgress.oversizeTokenLines || 0,
        percentage: this.roundPercentage((100 * this.currentLoadingProgress.oversizeTokenLines
          / ( this.currentLoadingProgress.loadedLines
            + this.currentLoadingProgress.notEnoughTokenLines
            + this.currentLoadingProgress.oversizeTokenLines)
          ) || 0)
      }
    ];

    this.dataLinesChart.data.labels = [distributionData[0].name + ' (' + distributionData[0].percentage + '%)',
                                       distributionData[1].name + ' (' + distributionData[1].percentage + '%)',
                                       distributionData[2].name + ' (' + distributionData[2].percentage + '%)'];
    if (this.dataLinesChart.data.datasets && this.dataLinesChart.data.datasets[0]) {
      this.dataLinesChart.data.datasets[0].data = [distributionData[0].value, distributionData[1].value, distributionData[2].value];
    }
    this.dataLinesChart.update();

    // Make the resize async.
    timer()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => this.resizeStatisticsCharts());
  }

  /**
   * Update loading status of each loading job, put a tag on each data source icon.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private putLoadingStatusTagOnEachLoadingJob() {
    if (this.isDataMapping) {
      return;
    }
    this.leftVisService.addLoadingStatus(
      this.logicService.loadingJobs.map(loadingJob => {
        let jobProgress = this.getLatestLoadingProgress(loadingJob.jobName);
        if (jobProgress === undefined ||
          this.logicService.loadingJobs.find(job => job.jobName === loadingJob.jobName &&
            job.loadingStatements.length === 0)) {
          let status = 'NO DATA YET', isLoading = false;
          if (this.initialLoadingProgress) {
            status = 'LOADING';
            isLoading = true;
          } else if (loadingJob.loadingStatements.length === 0) {
            status = 'DATA NOT MAPPED';
          }
          jobProgress = {
            source: DataSetLogic.createDataSetLabel(
              loadingJob.isChanged,
              loadingJob.dataSet.uri,
            ),
            status,
            isLoading,
          };
        }
        return jobProgress;
      })
    );
    if (!isEqual(this.loadingJobGraph, this.leftVisService.loadingJobGraph)) {
      this.loadingJobGraph = this.leftVisService.loadingJobGraph;
    }
  }

  /**
   * Resize the graph statistics, loading speed statistics and data line distribution charts.
   *
   * @memberof LoadingBuilderComponent
   */
  resizeStatisticsCharts() {
    this.loadingSpeedChart.resize();
    this.dataLinesChart.update();
    this.graphStatisticalTrendLineChart.resize();
  }

  /**
   * Get graph statistics repeatedly.
   * @memberof LoadingBuilderComponent
   */
  getGraphStatistics(realtime?: boolean) {
    this.loadingExecutorService
      .getGraphStatistics(this.authService.getCurrentGraph(), realtime)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(
        graphStatistics => {
          // Update current graph statistics
          this.graphStatistics = graphStatistics;
          timer()
            .pipe(takeUntil(this.ngUnsubscribe))
            .subscribe(() => this.graphStatisticsDatabase.addGraphStatisticsData(graphStatistics));

          // Update graph trend chart
          this.addGraphStatisticTrendChartData();
          if (realtime) {
            return;
          }

          timer(this.checkGraphStatisticsInterval)
            .pipe(takeUntil(this.ngUnsubscribe))
            .subscribe(() => this.getGraphStatistics());
        },
        () => {
          this.logger.warn('Error in retrieving statistical data.');
          timer(this.checkGraphStatisticsInterval)
            .pipe(takeUntil(this.ngUnsubscribe))
            .subscribe(() => this.getGraphStatistics());
        }
      );
  }

  /**
   * Update either total vertex and total edge within three or less dataset points
   *
   * @private
   * @param {*} render
   * @memberof LoadingBuilderComponent
   */
  private addGraphStatisticTrendChartData() {
    if (this.graphStatistics.totalVertexNumber !== undefined && this.graphStatistics.totalEdgeNumber !== undefined) {
      this.graphStatisticalTrendLineChart.data.labels.push(new Date().toTimeString().split(' ')[0]);
      this.graphStatisticalTrendLineChart.data.datasets.forEach((dataset: { label: string; data: any[]; }) => {
        dataset.data.push(dataset.label === 'Total vertex' ? this.graphStatistics.totalVertexNumber : this.graphStatistics.totalEdgeNumber);
      });
      this.graphStatisticalTrendLineChart.update();
    } else {
      this.logger.warn('Undefined data in graph statistic database.');
    }
    this.updateGraphStatisticalTrendAriaLabel();
  }

  /**
   * Update statistical trend chart aria-describedby attribute.
   *
   * @memberof LoadingBuilderComponent
   */
  updateGraphStatisticalTrendAriaLabel() {
    if (this.graphStatistics.totalVertexNumber !== undefined && this.graphStatistics.totalEdgeNumber !== undefined) {
      this.graphStatisticalTrendAriaLabel = `Graph statistical trend chart, total vertex number is ${this.graphStatistics.totalVertexNumber}, total edge number is ${this.graphStatistics.totalEdgeNumber}.`;
    } else {
      this.graphStatisticalTrendAriaLabel = 'Graph statistical trend chart.';
    }
    this.cdr.detectChanges();
  }

  /**
   * Get graph statistics repeatedly.
   *
   * @private
   * @param {boolean} [daemon]
   * @memberof LoadingBuilderComponent
   */
  private getLoadingProgress(daemon?: boolean) {
    if (this.getLoadingProgressInProgress && daemon) {
      return;
    }

    this.getLoadingProgressInProgress = true;
    const issueTime = Date.now();
    this.loadingExecutorService
      .getLoadingProgress(
        this.graphName,
        this.logicService.loadingJobs.filter(job => job.loadingStatements.length > 0)
      )
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => {
          this.getLoadingProgressInProgress = false;
          this.initialLoadingProgress = false;
        })
      )
      .subscribe(
        (response: Map<string, LoadingProgress>) => {
          if (issueTime < this.loadingProgressTimestamp) {
            return;
          }
          this.loadingProgressTimestamp = issueTime;

          response.forEach((progress, job) => {
            if (!this.loadingProgressMap.has(job)) {
              this.loadingProgressMap.set(job, []);
            }
            const progressPercentage = progress['percentage'];
            progress['percentage'] = undefined;
            progress['loadedSize'] = undefined;

            if (GSQLDataSourceLogic.isKafkaLoadingJob(job)) {
              progress['currentSpeed'] = undefined;
            } else if (this.logicService.isFileLoadingJob(job)) {
              progress['percentage'] = progressPercentage;
            }

            this.loadingProgressMap.get(job).push({
              timestamp: Date.now(),
              progress: progress
            });
          });
          // Update current loading job progress.
          if (this.loadingProgressMap.has(this.currentLoadingJob)) {
            const progresses = this.loadingProgressMap.get(this.currentLoadingJob);
            const latestProgress = progresses[progresses.length - 1];

            // Update loading speed trend chart
            if (this.currentLoadingProgress.source === latestProgress.progress.source) {
              this.generateLoadingSpeedData(latestProgress, progresses);
              this.loadingSpeedChart.update();
            } else {
              progresses.map((progress, i) => this.generateLoadingSpeedData(progress, progresses));
              this.loadingSpeedChart.update();
            }
            this.currentLoadingProgress = latestProgress.progress;
          }
          this.putLoadingStatusTagOnEachLoadingJob();
          this.renderCurrentLoadingProgress();
          this.setToolbarMode();
        },
        () => {
          this.logger.warn('Error in retrieving loading progress.');
        }
      );
  }

  /**
   * Generate one loading speed data point.
   *
   * @private
   * @param {{ timestamp: number, progress: LoadingProgress}} latestProgress
   * @param {{ timestamp: number, progress: LoadingProgress}} prevProgress
   * @returns
   * @memberof LoadingBuilderComponent
   */
  private generateLoadingSpeedData(
    latestProgress: { timestamp: number, progress: LoadingProgress },
    progresses: { timestamp: number, progress: LoadingProgress }[],
  ) {
    if (latestProgress.progress.currentSpeed !== undefined) {
      let speed = latestProgress.progress.currentSpeed;
      for (let i = progresses.length - 1; i >= 0; i--) {
        if (progresses[i].progress.loadedLines !== latestProgress.progress.loadedLines) {
          break;
        }
        if (latestProgress.timestamp - progresses[i].timestamp > 10000) {
          speed = 0;
          break;
        }
      }
      this.loadingSpeedChart.data.labels.push(new Date(latestProgress.timestamp).toString().split(' ')[4]);
      if (this.loadingSpeedChart.data.datasets && this.loadingSpeedChart.data.datasets[0]) {
        this.loadingSpeedChart.data.datasets[0].data.push(speed);
      }
    } else {
      this.loadingSpeedChart.data.labels.push(new Date(latestProgress.timestamp).toString().split(' ')[4]);
      if (this.loadingSpeedChart.data.datasets && this.loadingSpeedChart.data.datasets[0]) {
        this.loadingSpeedChart.data.datasets[0].data.push(0);
      }
    }
    this.loadingSpeedChart.update();
  }

  /** Get file list from server. */
  private getFileInfo() {
    this.loadingExecutorService
      .getFileList()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(
        fileInfos => {
          this.fileInfos = fileInfos;
        },
        () => this.logger.warn('Failed to retrieve file list from server.')
      );
  }

  /**
   * Clean up all the log streams.
   *
   * @private
   * @memberof LoadingBuilderComponent
   */
  private cleanUpLogStream() {
    this.logs.forEach(value => {
      value.stdStream.complete();
      value.errStream.complete();
    });
  }

  /**
   * Check if current loading job is a kafka loading job.
   * @returns {boolean}
   */
  isCurrentLoadingJobKafka(): boolean {
    if (this.currentLoadingJob) {
      return GSQLDataSourceLogic.isKafkaLoadingJob(this.currentLoadingJob);
    }
    return false;
  }
  // End loading executor
}
