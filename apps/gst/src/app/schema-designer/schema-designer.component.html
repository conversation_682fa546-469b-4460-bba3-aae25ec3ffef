<h1 class="cdk-visually-hidden">Design Schema</h1>
<app-loading-indicator [show]="loading | async">
  <app-toolbar-container [config]="toolbarConfig" (interaction)="onInteraction($event)">
    <div fxLayout="row" style="height: 100%;">
      <app-schema-viewer
        [class.hidden]="rightPanelOpened && reflow"
        [style.cursor]="addingVertex ? 'copy' : 'default'"
        #chartContainer
        class="schema-viewer-container"
        fxFlex="calc(100% - {{ rightPanelWidth }})"
        [isSchemaDesign]="true"
        (handlePointer)="handleGraphChartPointer($event)"
        (handleEnter)="handleGraphChartEnter($event)"
      >
      </app-schema-viewer>
      <app-shortcuts-menu
        class="shortcuts-menu-container"
        [shortcuts]="shortcuts">
      </app-shortcuts-menu>
      <div
        [class.hidden]="rightPanelOpened && reflow"
        class="main-panel-warning-container mat-input-warn"
        style.max-width="calc(100% - {{ rightPanelWidth }})">
        <div *ngIf="authorizationWarning | async" id="auth-warning">
          <mat-icon attr.aria-label="{{ 'Warning' | translate }}">warning</mat-icon>
          <span>{{ authorizationWarning | async }}</span>
        </div>
        <div *ngIf="unsavedWarning | async" fxLayout="row" id="unsave-warning">
          <mat-icon attr.aria-label="{{ 'Warning' | translate }}">warning</mat-icon>
          <div>
            <div *ngFor="let warningText of unsavedWarning | async">
              {{ warningText }}
            </div>
          </div>
        </div>
      </div>

      <!-- Hint -->
      <div
        fxLayoutAlign="center center"
        *ngIf="showHint | async"
        class="hint-container hint-text"
        [ngClass]="{
          'hint-container-with-right-panel': rightPanelOpened,
          'hidden': rightPanelOpened && reflow
        }">
        <div fxShow class="hint-table" fxLayout="column">
          <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
            <p class="hint-table-left">{{ 'Create vertex type' | translate }}</p>
            <p class="hint-table-right">
              <span>{{ 'Click' | translate }}</span>
              <mat-icon
                class="icon-in-instruction"
                attr.aria-label="{{ 'Create vertex type' | translate }}">
                add_circle
              </mat-icon>
              <span>{{ 'in toolbar' | translate }}</span>
            </p>
          </div>
          <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
            <p class="hint-table-left">{{ 'Create edge type' | translate }}</p>
            <p class="hint-table-right">
              <span>{{ 'Click' | translate }}</span>
              <mat-icon
                class="icon-in-instruction"
                attr.aria-label="{{ 'Create edge type' | translate }}">
                trending_flat
              </mat-icon>
              <span>{{ 'then choose starting vertex type and target vertex type' | translate }}</span>
            </p>
          </div>
          <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
            <p class="hint-table-left">{{ 'Edit vertex or edge type' | translate }}</p>
            <p class="hint-table-right">
              <span>{{ 'Choose vertex or edge type then click' | translate }}</span>
              <mat-icon
                class="icon-in-instruction"
                attr.aria-label="{{ 'Edit vertex or edge type' | translate }}">
                edit
              </mat-icon>
              <span>{{ 'or double click vertex or edge type' | translate }}</span>
            </p>
          </div>
          <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
            <p class="hint-table-left">{{ 'Delete vertex or edge type' | translate }}</p>
            <p class="hint-table-right">
              <span>{{ 'Choose vertex or edge types then click' | translate }}</span>
              <mat-icon
                class="icon-in-instruction"
                attr.aria-label="{{ 'Delete vertex or edge type' | translate }}">
                delete
              </mat-icon>
              <span>{{ 'in toolbar' | translate }}</span>
            </p>
          </div>
          <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
            <p class="hint-table-left">{{ 'Choose multiple vertex and edge types' | translate }}</p>
            <p class="hint-table-right">
              <span>{{ 'Hold "Shift" key on keyboard' | translate }}</span>
            </p>
          </div>
          <div class="hint-table-row" fxLayout="row" fxLayoutAlign="center start">
            <p class="hint-table-left">{{ 'Publish changes' | translate }}</p>
            <p class="hint-table-right">
              <span>{{ 'Click' | translate }}</span>
              <mat-icon
                class="icon-in-instruction"
                attr.aria-label="{{ 'Publish changes' | translate }}">
                publish
              </mat-icon>
              <span>{{ 'in toolbar' | translate }}</span>
            </p>
          </div>
        </div>
      </div>
      <!-- End of Hint -->

      <!-- Right Panel -->
      <div
        fxFlex="{{ rightPanelWidth }}"
        class="right-panel-container"
        [class.hidden]="rightPanelWidth === '0px'">
        <!-- Right Panel Header -->
        <div fxLayout="row" class="panel-header">
          <h2
            fxFlex="auto"
            attr.aria-label="{{ getRightPanelTitle() | translate }}">
            {{ getRightPanelTitle() | translate }}
          </h2>
          <!-- Action Buttons -->
          <div fxFlex="80px" fxLayout="row">
            <button
              mat-icon-button
              class="panel-btn"
              (click)="closePanel()"
              matTooltip="{{ 'CANCEL' | translate }}"
              attr.aria-label="{{ 'CANCEL' | translate }}">
              <mat-icon attr.aria-label="{{ 'CANCEL' | translate }}">clear</mat-icon>
            </button>
            <button
              *ngIf="selectedEvent === 'viewGlobalTypes'"
              mat-icon-button
              class="panel-btn"
              color="primary"
              (click)="updateGlobalTypes()"
              matTooltip="{{ 'CONFIRM' | translate }}"
              attr.aria-label="{{ 'CONFIRM' | translate }}">
              <mat-icon attr.aria-label="{{ 'CONFIRM' | translate }}">check</mat-icon>
            </button>
            <button
              *ngIf="selectedEvent === 'addVertex' || selectedEvent === 'addEdge'"
              mat-icon-button
              class="panel-btn"
              color="primary"
              (click)="addElement()"
              matTooltip="{{ 'ADD' | translate }}"
              attr.aria-label="{{ 'ADD' | translate }}">
              <mat-icon attr.aria-label="{{ 'ADD' | translate }}">check</mat-icon>
            </button>
            <button
              *ngIf="selectedEvent === 'editAttributes'"
              mat-icon-button
              class="panel-btn"
              class="panel-btn"
              color="primary"
              matTooltip="{{ 'UPDATE' | translate }}"
              (click)="updateElement()"
              attr.aria-label="{{ 'UPDATE' | translate }}">
              <mat-icon attr.aria-label="{{ 'UPDATE' | translate }}">check</mat-icon>
            </button>
          </div>
          <!-- End of Action Buttons -->
        </div>
        <!-- End of Right Panel Header -->

        <div class="panel-content">
          <!-- Vertex Form -->
          <div
            class="panel-form-container" fxLayout="column" fxLayoutGap="12px"
            *ngIf="selectedEvent === 'addVertex' || (selectedEvent === 'editAttributes' && selectedVertex)">
            <div
              *ngIf="selectedEvent === 'editAttributes' && (editWarning | async)"
              fxLayout="row"
              class="right-panel-warning-container mat-input-warn">
              <mat-icon attr.aria-label="{{ 'Warning' | translate }}">warning</mat-icon>
              <div>
                <div *ngFor="let warning of editWarning | async">
                  {{ warning }}
                </div>
              </div>
            </div>
            <mat-form-field class="max-width">
              <input
                matInput
                [placeholder]="vertexForm.name.placeholder | translate"
                [type]="vertexForm.name.type"
                [(ngModel)]="vertex.name"
                [disabled]="
                  disableEditGlobalTypeInGraph(vertex.isLocal) ||
                  disableEditBasedOnUsage(vertex)
                ">
            </mat-form-field>
            <div fxLayout="row" fxLayoutGap="12px" fxLayoutAlign="start center">
              <div fxFlex="calc(98% - 100px)" fxLayoutGap="2%">
                <mat-form-field fxFlex="49%">
                  <input
                    matInput
                    [placeholder]="vertexForm.primaryId.placeholder | translate"
                    [type]="vertexForm.primaryId.type"
                    [(ngModel)]="vertex.primaryId.name"
                    [disabled]="
                      disableEditGlobalTypeInGraph(vertex.isLocal) ||
                      disableEditBasedOnUsage(vertex)
                    ">
                </mat-form-field>
                <mat-form-field fxFlex="49%">
                  <mat-select
                    [placeholder]="vertexForm.primaryIdType.placeholder | translate"
                    [(ngModel)]="vertex.primaryId.type.name"
                    [disabled]="
                      disableEditGlobalTypeInGraph(vertex.isLocal) ||
                      disableEditBasedOnUsage(vertex)
                    ">
                    <mat-option *ngFor="let option of vertexForm.primaryIdType.options" [value]="option.key">
                      {{ option.value }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div fxFlex="100px">
                <mat-checkbox
                  *ngIf="!vertex.primaryId.isPrimaryKey"
                  class="text-center"
                  [(ngModel)]="vertex.config.PRIMARY_ID_AS_ATTRIBUTE"
                  [disabled]="
                    disableEditGlobalTypeInGraph(vertex.isLocal) ||
                    disableEditBasedOnUsage(vertex)
                  ">
                  {{ 'As attribute' | translate }}
                </mat-checkbox>
                <mat-checkbox
                  *ngIf="vertex.primaryId.isPrimaryKey"
                  class="text-center"
                  [(ngModel)]="vertex.primaryId.isPrimaryKey"
                  [disabled]="true">
                  {{ 'Primary key' | translate }}
                </mat-checkbox>
              </div>
            </div>
            <div>
              <span class="hint-text">{{ 'Style' | translate }}</span>
              <div class="box-container" id="color-and-icon-container">
                <!-- Color Picker -->
                <div fxFlex="100%" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="12px">
                  <!-- Color and Icon Preview -->
                  <div fxFlex="52px">
                    <div [style.background]="vertex.style.fillColor" class="vertex-color-icon-container">
                      <!-- for old img -->
                      <img
                        *ngIf="vertexIcon"
                        [src]="vertexIcon.url"
                        [ngClass]="{
                          'icon-preview': !vertexIcon.isUserUploaded,
                          'user-uploaded-icon-preview': vertexIcon.isUserUploaded
                        }"
                        alt="{{ 'Preview' | translate }}"
                      />
                    </div>
                  </div>

                  <mat-form-field fxFlex="calc(100% - 190px)">
                    <input
                      matInput
                      placeholder="{{ 'Color hex' | translate }}"
                      [(colorPicker)]="vertex.style.fillColor"
                      cpOutputFormat="hex"
                      cpPosition="bottom"
                      [value]="vertex.style.fillColor">
                  </mat-form-field>
                  <div fxFlex="120px">
                    <button
                      mat-button color="accent"
                      (click)="openIconSelectorPopup()">
                      {{ 'Select icon' | translate }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div class="attribute-box-container">
              <span class="hint-text">{{ 'Attributes' | translate }}</span>
              <div class="box-container" id="attribute-container">
                <div fxLayout="row">
                  <span fxFlex fxLayoutAlign="center center" class="hint-text">
                    {{ 'Click "+" on the right to add attributes.' | translate }}
                  </span>
                  <!-- Add Attributes Button -->
                  <button
                    fxFlex="40px"
                    mat-icon-button
                    color="accent"
                    (click)="addAttribute()"
                    [disabled]="disableEditGlobalTypeInGraph(vertex.isLocal)"
                    attr.aria-label="{{ 'Add' | translate }}">
                    <mat-icon attr.aria-label="{{ 'Add' | translate }}">add</mat-icon>
                  </button>
                </div>
                <cdk-virtual-scroll-viewport class="virtual-scroll-container" itemSize="120">
                  <div *cdkVirtualFor="let attr of vertex.attributes; let i = index" fxLayout="column">
                    <div id="attr.name" fxLayout="row" class="one-single-attribute" fxLayoutAlign="space-between start">
                      <div fxFlex="79%" fxLayoutGap="2%">
                        <div fxLayout.lt-sm="column" fxFlex="calc(98% - 62px)" fxLayoutGap="2%">
                          <mat-form-field fxFlex="49%">
                            <input
                              matInput
                              class="attribute-name"
                              [placeholder]="vertexForm.attributes[i].name.placeholder | translate"
                              [type]="vertexForm.attributes[i].name.type"
                              [(ngModel)]="attr.name"
                              [disabled]="disableEditGlobalTypeInGraph(vertex.isLocal)">
                          </mat-form-field>
                          <div fxFlex="49%" fxFlex.lt-sm="0px" fxLayout="column" fxLayoutGap="0px">
                            <mat-form-field>
                              <mat-select
                                class="attribute-type-select"
                                [placeholder]="vertexForm.attributes[i].type.placeholder | translate"
                                [(ngModel)]="attr.type.name"
                                (ngModelChange)="setAttributeIndexToFalseIfUnsupportedType(attr)"
                                [disabled]="disableEditGlobalTypeInGraph(vertex.isLocal)">
                                <mat-option
                                  *ngFor="let option of vertexForm.attributes[i].type.options"
                                  [value]="option.key">
                                  {{ option.value }}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                            <ng-template [ngIf]="!['LIST', 'SET', 'MAP', 'UDT'].includes(attr.type.name)">
                              <mat-form-field>
                                <input
                                  matInput
                                  class="attribute-default-value"
                                  [placeholder]="vertexForm.attributes[i].defaultValue.placeholder | translate"
                                  [type]="vertexForm.attributes[i].defaultValue.type"
                                  [(ngModel)]="attr.defaultValue"
                                  matTooltip="{{ attr.type.name === 'DATETIME' ? 'E.g.: yyyy-MM-dd or yyyy-MM-dd hh:mm:ss. For more formats, please refer to the documentation.' : '' }}"
                                  [disabled]="disableEditGlobalTypeInGraph(vertex.isLocal)">
                              </mat-form-field>
                            </ng-template>
                            <ng-template [ngIf]="['LIST', 'SET'].includes(attr.type.name)">
                              <div fxLayout="column">
                                <mat-form-field>
                                  <mat-select
                                    [placeholder]="vertexForm.attributes[i].containerValueType.placeholder | translate"
                                    [(ngModel)]="attr.type.valueTypeName"
                                    [disabled]="disableEditGlobalTypeInGraph(vertex.isLocal)">
                                    <mat-option *ngFor="let option of vertexForm.attributes[i].containerValueType.options" [value]="option.key">
                                      {{ option.value }}
                                    </mat-option>
                                  </mat-select>
                                </mat-form-field>
                                <mat-form-field *ngIf="attr.type.valueTypeName === 'UDT'">
                                  <mat-select
                                    [placeholder]="vertexForm.attributes[i].udtName.placeholder | translate"
                                    [(ngModel)]="attr.type.valueTypeTupleName"
                                    [matTooltip]="getUdtStringWithFields(attr.type.valueTypeTupleName)"
                                    [disabled]="disableEditGlobalTypeInGraph(vertex.isLocal)">
                                    <mat-option *ngFor="let option of vertexForm.attributes[i].udtName.options" [value]="option.key">
                                      {{ option.value }}
                                    </mat-option>
                                  </mat-select>
                                </mat-form-field>
                              </div>
                            </ng-template>
                            <ng-template [ngIf]="attr.type.name === 'UDT'">
                              <mat-form-field>
                                <mat-select
                                  [placeholder]="vertexForm.attributes[i].udtName.placeholder | translate"
                                  [(ngModel)]="attr.type.tupleName"
                                  [matTooltip]="getUdtStringWithFields(attr.type.tupleName)"
                                  [disabled]="disableEditGlobalTypeInGraph(vertex.isLocal)">
                                  <mat-option *ngFor="let option of vertexForm.attributes[i].udtName.options" [value]="option.key">
                                    {{ option.value }}
                                  </mat-option>
                                </mat-select>
                              </mat-form-field>
                            </ng-template>
                            <div fxLayout="column" *ngIf="attr.type.name === 'MAP'">
                              <mat-form-field>
                                <mat-select
                                  [placeholder]="vertexForm.attributes[i].mapKeyType.placeholder | translate"
                                  [(ngModel)]="attr.type.keyTypeName" [disabled]="disableEditGlobalTypeInGraph(vertex.isLocal)">
                                  <mat-option *ngFor="let option of vertexForm.attributes[i].mapKeyType.options" [value]="option.key">
                                    {{ option.value }}
                                  </mat-option>
                                </mat-select>
                              </mat-form-field>
                              <mat-form-field>
                                <mat-select
                                  [placeholder]="vertexForm.attributes[i].containerValueType.placeholder | translate"
                                  [(ngModel)]="attr.type.valueTypeName" [disabled]="disableEditGlobalTypeInGraph(vertex.isLocal)">
                                  <mat-option *ngFor="let option of vertexForm.attributes[i].containerValueType.options" [value]="option.key">
                                    {{ option.value }}
                                  </mat-option>
                                </mat-select>
                              </mat-form-field>
                              <mat-form-field *ngIf="attr.type.valueTypeName === 'UDT'">
                                <mat-select
                                  [placeholder]="vertexForm.attributes[i].udtName.placeholder | translate"
                                  [(ngModel)]="attr.type.valueTypeTupleName"
                                  [matTooltip]="getUdtStringWithFields(attr.type.valueTypeTupleName)"
                                  [disabled]="disableEditGlobalTypeInGraph(vertex.isLocal)">
                                  <mat-option *ngFor="let option of vertexForm.attributes[i].udtName.options" [value]="option.key">
                                    {{ option.value }}
                                  </mat-option>
                                </mat-select>
                              </mat-form-field>
                            </div>
                          </div>
                        </div>
                        <div fxFlex="120px" class="index-checkbox">
                          <mat-checkbox
                            [(ngModel)]="attr.hasIndex"
                            [disabled]="disableEditGlobalTypeInGraph(vertex.isLocal) || attributeIndexNotSupported(attr)">
                            {{ 'Index' | translate }}
                          </mat-checkbox>
                        </div>
                      </div>
                      <div fxFlex="19%" fxLayoutAlign="end center">
                        <button
                          mat-icon-button
                          color="warn"
                          (click)="removeAttribute(i)"
                          [disabled]="disableEditGlobalTypeInGraph(vertex.isLocal)"
                          attr.aria-label="{{ 'Remove' | translate }}">
                          <mat-icon attr.aria-label="{{ 'Remove' | translate }}">remove</mat-icon>
                        </button>
                      </div>
                    </div>
                    <mat-divider
                      *ngIf="vertex.attributes.length > 1 && i !== vertex.attributes.length - 1"></mat-divider>
                  </div>
                </cdk-virtual-scroll-viewport>
              </div>
            </div>
            <div class="attribute-box-container" *ngIf="vertex.embeddingAttributes && vertex.embeddingAttributes.length > 0">
              <span class="hint-text">{{ 'Embedding attributes' | translate }}</span>
              <div class="box-container" id="attribute-container">
                <cdk-virtual-scroll-viewport class="virtual-scroll-container" itemSize="120">
                  <div *cdkVirtualFor="let attr of vertex.embeddingAttributes; let i = index" fxLayout="column">
                    <div id="attr.name" fxLayout="row" class="one-single-attribute" fxLayoutAlign="space-between start">
                      <div fxFlex="79%" fxLayoutGap="2%">
                        <div fxLayout.lt-sm="column" fxFlex="calc(98% - 62px)" fxLayoutGap="2%">
                          <mat-form-field fxFlex="49%">
                            <input
                              matInput
                              class="attribute-name"
                              [placeholder]="vertexForm.embeddingAttributes[i].name.placeholder | translate"
                              [type]="vertexForm.embeddingAttributes[i].name.type"
                              [(ngModel)]="attr.name"
                              [disabled]="true">
                          </mat-form-field>
                          <div fxFlex="49%" fxFlex.lt-sm="0px" fxLayout="column" fxLayoutGap="0px">
                            <mat-form-field>
                              <input
                                matInput
                                class="attribute-id"
                                [placeholder]="vertexForm.embeddingAttributes[i].id.placeholder | translate"
                                [type]="vertexForm.embeddingAttributes[i].id.type"
                                [(ngModel)]="attr.embeddingAttrId"
                                [disabled]="true">
                            </mat-form-field>
                            <mat-form-field>
                              <input
                                matInput
                                class="attribute-dimension"
                                [placeholder]="vertexForm.embeddingAttributes[i].dimension.placeholder | translate"
                                [type]="vertexForm.embeddingAttributes[i].dimension.type"
                                [(ngModel)]="attr.dimension"
                                [disabled]="true">
                            </mat-form-field>
                            <mat-form-field>
                              <input
                                matInput
                                class="attribute-metric"
                                [placeholder]="vertexForm.embeddingAttributes[i].metric.placeholder | translate"
                                [type]="vertexForm.embeddingAttributes[i].metric.type"
                                [(ngModel)]="attr.metric"
                                [disabled]="true">
                            </mat-form-field>
                            <mat-form-field>
                              <mat-select
                                [placeholder]="vertexForm.embeddingAttributes[i].index.placeholder | translate"
                                [(ngModel)]="attr.indexType"
                                [disabled]="true">
                                <mat-option
                                  *ngFor="let option of vertexForm.embeddingAttributes[i].index.options"
                                  [value]="option.key">
                                  {{ option.value }}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                            <mat-form-field>
                              <mat-select
                                [placeholder]="vertexForm.embeddingAttributes[i].type.placeholder | translate"
                                [(ngModel)]="attr.type"
                                [disabled]="true">
                                <mat-option
                                  *ngFor="let option of vertexForm.embeddingAttributes[i].type.options"
                                  [value]="option.key">
                                  {{ option.value }}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                        </div>
                      </div>
                      <div fxFlex="19%" fxLayoutAlign="end center">
                        <button
                          mat-icon-button
                          color="warn"
                          (click)="removeAttribute(i)"
                          [disabled]="true"
                          attr.aria-label="{{ 'Remove' | translate }}">
                          <mat-icon attr.aria-label="{{ 'Remove' | translate }}">remove</mat-icon>
                        </button>
                      </div>
                    </div>
                    <mat-divider
                      *ngIf="vertex.embeddingAttributes.length > 1 && i !== vertex.embeddingAttributes.length - 1"></mat-divider>
                  </div>
                </cdk-virtual-scroll-viewport>
              </div>
            </div>
          </div>
          <!-- End of Vertex Form -->

          <!-- Edge Form -->
          <div
            class="panel-form-container" fxLayout="column" fxLayoutGap="12px"
            *ngIf="selectedEvent === 'addEdge' || (selectedEvent === 'editAttributes' && selectedEdge)">
            <div
              *ngIf="selectedEvent === 'editAttributes' && (editWarning | async)"
              fxLayout="row"
              class="right-panel-warning-container mat-input-warn">
              <mat-icon attr.aria-label="{{ 'Warning' | translate }}">warning</mat-icon>
              <div>
                <div *ngFor="let warning of editWarning | async">
                  {{ warning }}
                </div>
              </div>
            </div>
            <div fxLayout="row" fxLayoutAlign="center center" fxLayoutGap="12px">
              <mat-form-field class="max-width" fxFlex>
                <input
                  matInput
                  [placeholder]="edgeForm.name.placeholder | translate"
                  [type]="edgeForm.name.type"
                  [ngModel]="edge.name"
                  (ngModelChange)="onEdgeNameChange($event)"
                  [disabled]="
                    disableEditGlobalTypeInGraph(edge.isLocal) ||
                    disableEditBasedOnUsage(edge)
                  ">
              </mat-form-field>
              <mat-checkbox
                fxFlex="88px" [(ngModel)]="edge.directed"
                (change)="onDirectedEdgeChange()"
                [disabled]="
                  disableEditGlobalTypeInGraph(edge.isLocal) ||
                  disableEditBasedOnUsage(edge)
                ">
                {{ edgeForm.directed.placeholder | translate }}
              </mat-checkbox>
            </div>
            <div *ngIf="edge.directed" fxLayout="row" fxLayoutAlign="center center" fxLayoutGap="12px">
              <mat-checkbox
                fxFlex="124px"
                [(ngModel)]="edge.hasReverseEdge"
                (change)="onReverseEdgeChange()"
                [disabled]="
                  disableEditGlobalTypeInGraph(edge.isLocal) ||
                  disableEditBasedOnUsage(edge)
                ">
                {{ edgeForm.hasReverseEdge.placeholder | translate }}
              </mat-checkbox>
              <mat-form-field fxFlex="calc(100% - 136px)">
                <input
                  matInput
                  [placeholder]="edgeForm.reverseEdge.placeholder | translate"
                  [type]="edgeForm.reverseEdge.type"
                  [(ngModel)]="edge.reverseEdge"
                  [disabled]="!edge.hasReverseEdge">
              </mat-form-field>
            </div>

            <!-- Edge Pairs -->
            <div>
              <span class="hint-text">{{ 'Source and target vertex types' | translate }}</span>
              <div class="box-container" id="from-to-vertex-container">
                <div
                  *ngFor="let pair of edge.fromToVertexTypePairs; let i = index"
                  fxLayout="row"
                  fxLayoutAlign="center center"
                  fxLayoutGap="12px"
                  id="one-from-to-vertex-pair">
                  <div fxFlex="calc(100% - 52px)" fxLayoutGap="2%">
                    <mat-form-field fxFlex="49%" class="panel-form-field">
                      <mat-select
                        [placeholder]="edgeForm.fromToEdgePairs[i].fromVertexTypeName.placeholder | translate"
                        [(ngModel)]="pair.from"
                        [disabled]="disableEditGlobalTypeInGraph(edge.isLocal) || disableEditBasedOnUsage(edge)">
                        <mat-option *ngFor="let option of edgeForm.fromToEdgePairs[i].fromVertexTypeName.options" [value]="option.key">
                          {{ option.value }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                    <mat-form-field fxFlex="49%" class="panel-form-field">
                      <mat-select
                        [placeholder]="edgeForm.fromToEdgePairs[i].toVertexTypeName.placeholder | translate"
                        [(ngModel)]="pair.to"
                        [disabled]="disableEditGlobalTypeInGraph(edge.isLocal) || disableEditBasedOnUsage(edge)">
                        <mat-option *ngFor="let option of edgeForm.fromToEdgePairs[i].toVertexTypeName.options" [value]="option.key">
                          {{ option.value }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                  </div>

                  <button
                    mat-icon-button
                    color="warn"
                    fxFlex="40px"
                    (click)="removeEdgePair(i)"
                    [disabled]="
                      edge.fromToVertexTypePairs.length === 1 &&
                      (disableEditGlobalTypeInGraph(edge.isLocal) || disableEditBasedOnUsage(edge))
                    "
                    attr.aria-label="{{ 'Remove' | translate }}">
                    <mat-icon attr.aria-label="{{ 'Remove' | translate }}">remove</mat-icon>
                  </button>
                </div>

                <div fxLayout="row">
                  <span fxFlex fxLayoutAlign="center center" class="hint-text">
                    {{ 'Click "+" on the right to add source and target vertex types.' | translate }}
                  </span>
                  <!-- Add Vertex Types Button -->
                  <button
                    fxFlex="40px" mat-icon-button
                    color="accent" (click)="addEdgePair()"
                    [disabled]="disableEditGlobalTypeInGraph(edge.isLocal) || disableEditBasedOnUsage(edge)"
                    attr.aria-label="{{ 'Add' | translate }}">
                    <mat-icon attr.aria-label="{{ 'Add' | translate }}">add</mat-icon>
                  </button>
                </div>
              </div>
            </div>
            <!-- End of Edge Pairs -->
            <div>
              <span class="hint-text">{{ 'Style' | translate }}</span>
              <div class="box-container" id="color-and-icon-container">
                <!-- Color Picker -->
                <div fxLayout="row" fxLayoutAlign="center center" fxLayoutGap="12px">
                  <!-- Color Preview -->
                  <div [style.background]="edge.style.fillColor" class="edge-color-container"></div>

                  <mat-form-field fxFlex>
                    <input
                      matInput
                      placeholder="{{ 'Color hex' | translate }}"
                      [(colorPicker)]="edge.style.fillColor"
                      cpOutputFormat="hex"
                      cpPosition="bottom"
                      [value]="edge.style.fillColor">
                  </mat-form-field>
                </div>
                <!-- End of Color Picker -->
              </div>
            </div>

            <div class="attribute-box-container">
              <span class="hint-text">{{ 'Attributes' | translate }}</span>
              <div class="box-container" id="attribute-container">
                <div fxLayout="row">
                  <span fxFlex fxLayoutAlign="center center" class="hint-text">
                    {{ 'Click "+" on the right to add attributes.' | translate }}
                  </span>
                  <!-- Add Attributes Button -->
                  <button
                    fxFlex="40px"
                    mat-icon-button
                    color="accent"
                    (click)="addAttribute()"
                    [disabled]="disableEditGlobalTypeInGraph(edge.isLocal) ||
                      disableEditDiscriminatorAttributeBasedOnUsage(edge, i)"
                    attr.aria-label="{{ 'Add' | translate }}">
                    <mat-icon attr.aria-label="{{ 'Add' | translate }}">add</mat-icon>
                  </button>
                </div>
                <cdk-virtual-scroll-viewport class="virtual-scroll-container" itemSize="120">
                  <div *cdkVirtualFor="let attr of edge.attributes; let i = index" fxLayout="column">
                    <div fxLayout="row" class="one-single-attribute" fxLayoutAlign="space-between start">
                      <div fxLayout.lt-sm="column" fxFlex="79%" fxLayoutGap="2%">
                        <div fxLayout.lt-sm="column" fxFlex="calc(98% - 62px)" fxLayoutGap="2%">
                          <mat-form-field fxFlex="49%" class="panel-form-field">
                            <input
                              matInput class="attribute-name"
                              [placeholder]="edgeForm.attributes[i].name.placeholder | translate"
                              [type]="edgeForm.attributes[i].name.type"
                              [(ngModel)]="attr.name"
                              [disabled]="disableEditGlobalTypeInGraph(edge.isLocal) ||
                                disableEditDiscriminatorAttributeBasedOnUsage(edge, i)">
                          </mat-form-field>
                          <div fxFlex="49%" fxLayout="column" class="panel-form-field">
                            <mat-form-field *ngIf="!attr.isDiscriminator">
                              <mat-select
                                class="attribute-type-select"
                                [placeholder]="edgeForm.attributes[i].type.placeholder | translate"
                                [(ngModel)]="attr.type.name"
                                [disabled]="disableEditGlobalTypeInGraph(edge.isLocal)">
                                <mat-option *ngFor="let option of edgeForm.attributes[i].type.options" [value]="option.key">
                                  {{ option.value }}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                            <mat-form-field *ngIf="attr.isDiscriminator">
                              <mat-select
                                [placeholder]="edgeForm.attributes[i].type.placeholder | translate"
                                [(ngModel)]="attr.type.name"
                                [disabled]="disableEditGlobalTypeInGraph(edge.isLocal) ||
                                  disableEditDiscriminatorAttributeBasedOnUsage(edge, i)">
                                <mat-option *ngFor="let option of edgeForm.attributes[i].discriminatorType.options" [value]="option.key">
                                  {{ option.value }}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                            <ng-template [ngIf]="!['LIST', 'SET', 'MAP', 'UDT'].includes(attr.type.name)">
                              <mat-form-field>
                                <input
                                  matInput
                                  class="attribute-default-value"
                                  [placeholder]="edgeForm.attributes[i].defaultValue.placeholder | translate"
                                  [type]="edgeForm.attributes[i].defaultValue.type"
                                  [(ngModel)]="attr.defaultValue"
                                  [disabled]="disableEditGlobalTypeInGraph(edge.isLocal) ||
                                    disableEditDiscriminatorAttributeBasedOnUsage(edge, i)">
                              </mat-form-field>
                            </ng-template>
                            <ng-template [ngIf]="['LIST', 'SET'].includes(attr.type.name)">
                              <div fxLayout="column">
                                <mat-form-field>
                                  <mat-select
                                    [placeholder]="edgeForm.attributes[i].containerValueType.placeholder | translate"
                                    [(ngModel)]="attr.type.valueTypeName"
                                    [disabled]="disableEditGlobalTypeInGraph(edge.isLocal)">
                                    <mat-option *ngFor="let option of edgeForm.attributes[i].containerValueType.options" [value]="option.key">
                                      {{ option.value }}
                                    </mat-option>
                                  </mat-select>
                                </mat-form-field>
                                <mat-form-field *ngIf="attr.type.valueTypeName === 'UDT'">
                                  <mat-select
                                    [placeholder]="edgeForm.attributes[i].udtName.placeholder | translate"
                                    [(ngModel)]="attr.type.valueTypeTupleName"
                                    [matTooltip]="getUdtStringWithFields(attr.type.valueTypeTupleName)"
                                    [disabled]="disableEditGlobalTypeInGraph(edge.isLocal)">
                                    <mat-option *ngFor="let option of edgeForm.attributes[i].udtName.options" [value]="option.key">
                                      {{ option.value }}
                                    </mat-option>
                                  </mat-select>
                                </mat-form-field>
                              </div>
                            </ng-template>
                            <ng-template [ngIf]="attr.type.name === 'UDT'">
                              <mat-form-field>
                                <mat-select
                                  [placeholder]="edgeForm.attributes[i].udtName.placeholder | translate"
                                  [(ngModel)]="attr.type.tupleName"
                                  [matTooltip]="getUdtStringWithFields(attr.type.tupleName)"
                                  [disabled]="disableEditGlobalTypeInGraph(edge.isLocal)">
                                  <mat-option *ngFor="let option of edgeForm.attributes[i].udtName.options" [value]="option.key">
                                    {{ option.value }}
                                  </mat-option>
                                </mat-select>
                              </mat-form-field>
                            </ng-template>
                            <div fxLayout="column" *ngIf="attr.type.name === 'MAP'">
                              <mat-form-field>
                                <mat-select [placeholder]="edgeForm.attributes[i].mapKeyType.placeholder | translate"
                                  [(ngModel)]="attr.type.keyTypeName" [disabled]="disableEditGlobalTypeInGraph(edge.isLocal)">
                                  <mat-option *ngFor="let option of edgeForm.attributes[i].mapKeyType.options" [value]="option.key">
                                    {{ option.value }}
                                  </mat-option>
                                </mat-select>
                              </mat-form-field>
                              <mat-form-field>
                                <mat-select [placeholder]="edgeForm.attributes[i].containerValueType.placeholder | translate"
                                  [(ngModel)]="attr.type.valueTypeName" [disabled]="disableEditGlobalTypeInGraph(edge.isLocal)">
                                  <mat-option *ngFor="let option of edgeForm.attributes[i].containerValueType.options" [value]="option.key">
                                    {{ option.value }}
                                  </mat-option>
                                </mat-select>
                              </mat-form-field>
                              <mat-form-field *ngIf="attr.type.valueTypeName === 'UDT'">
                                <mat-select
                                  [placeholder]="edgeForm.attributes[i].udtName.placeholder | translate"
                                  [(ngModel)]="attr.type.valueTypeTupleName"
                                  [matTooltip]="getUdtStringWithFields(attr.type.valueTypeTupleName)"
                                  [disabled]="disableEditGlobalTypeInGraph(edge.isLocal)">
                                  <mat-option *ngFor="let option of edgeForm.attributes[i].udtName.options" [value]="option.key">
                                    {{ option.value }}
                                  </mat-option>
                                </mat-select>
                              </mat-form-field>
                            </div>
                          </div>
                        </div>
                        <div fxFlex="120px" class="index-checkbox">
                          <mat-checkbox
                            [(ngModel)]="attr.isDiscriminator"
                            [disabled]="disableEditGlobalTypeInGraph(edge.isLocal) ||
                              disableEditBasedOnUsage(edge) ||
                              disableDiscriminatorCheckboxWithTooltip(i)[0]"
                            (change)="resetDiscriminators($event, i)"
                            matTooltip="{{ disableDiscriminatorCheckboxWithTooltip(i)[1] | translate }}">
                            {{ 'Discriminator' | translate }}
                          </mat-checkbox>
                        </div>
                      </div>
                      <div fxFlex="19%" fxLayoutAlign="end center">
                        <button
                          mat-icon-button
                          color="warn"
                          (click)="removeAttribute(i)"
                          [disabled]="disableEditGlobalTypeInGraph(edge.isLocal) ||
                            disableEditDiscriminatorAttributeBasedOnUsage(edge, i)"
                          attr.aria-label="{{ 'Remove' | translate }}">
                          <mat-icon attr.aria-label="{{ 'Remove' | translate }}">remove</mat-icon>
                        </button>
                      </div>
                    </div>
                    <mat-divider
                      *ngIf="edge.attributes.length > 1 && i !== edge.attributes.length - 1"></mat-divider>
                  </div>
                </cdk-virtual-scroll-viewport>
              </div>
            </div>
          </div>
          <!-- End of Edge Form -->

          <!-- Global Types Form -->
          <div
            class="panel-form-container" fxLayout="column" fxLayoutGap="12px"
            *ngIf="selectedEvent === 'viewGlobalTypes'">
            <div
              *ngIf="hasDuplicatedTypeNames()"
              fxLayout="row"
              class="right-panel-warning-container mat-input-warn">
              <mat-icon attr.aria-label="{{ 'Warning' | translate }}">warning</mat-icon>
              <div>{{ 'Some global types have duplicate names with local ones.' | translate }}</div>
            </div>
            <div>
              <span class="hint-text" id="vertex-type-global-label">
                {{ 'Vertex Types' | translate }}
              </span>
              <div
                class="box-container"
                id="global-vertex-container">
                <mat-checkbox
                  [checked]="allTypesSelected(globalVertexTypes)"
                  (change)="
                    checkAll(globalVertexTypes, $event);
                    toggleCheckStatus(globalVertexTypes, globalEdgeTypes, true)">
                  {{ 'All' | translate }}
                </mat-checkbox>
                <div class="box-container" fxLayout="column" fxLayoutGap="5px">
                  <div *ngFor="let vertexType of globalVertexTypes">
                    <mat-checkbox
                      [(ngModel)]="vertexType.selected"
                      [disabled]="vertexType.disabled"
                      (change)="toggleCheckStatus([vertexType], globalEdgeTypes, true)"
                      aria-labelledby="vertex-type-global-label">
                      {{vertexType.vertexOrEdgeType.Name}}
                    </mat-checkbox>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <span class="hint-text" id="edge-type-global-label">
                {{ 'Edge Types' | translate }}
              </span>
              <div
                class="box-container"
                id="global-edge-container">
                <mat-checkbox
                  [checked]="allTypesSelected(globalEdgeTypes)"
                  (change)="
                    checkAll(globalEdgeTypes, $event);
                    toggleCheckStatus(globalEdgeTypes, globalVertexTypes)">
                  {{ 'All' | translate }}
                </mat-checkbox>
                <div class="box-container" fxLayout="column" fxLayoutGap="5px">
                  <div *ngFor="let edgeType of globalEdgeTypes">
                    <mat-checkbox
                      [(ngModel)]="edgeType.selected"
                      [disabled]="edgeType.disabled"
                      (change)="toggleCheckStatus([edgeType], globalVertexTypes)"
                      aria-labelledby="edge-type-global-label">
                      {{edgeType.vertexOrEdgeType.Name}}
                    </mat-checkbox>
                  </div>
                </div>
              </div>
            </div>
        </div>
          <!-- End of Global Types Form -->
        </div>
      </div>
      <!-- End of Right Panel -->
    </div>
  </app-toolbar-container>
</app-loading-indicator>

<!-- Settings Popup -->
<ng-template #settingsWindow>
  <div>
    <mat-toolbar color="primary" class="popup-toolbar">
      <h2 mat-dialog-title>{{ 'Settings' | translate }}</h2>
    </mat-toolbar>

    <mat-dialog-content>
      <!-- Show Vertex and Edge Types -->
      <br/>
      <p class="hint-text setting-section-title">{{ 'Show vertex and edge types' | translate }}</p>
      <div class="box-container">
        <div fxLayout="row" fxLayout.lt-md="column" fxLayoutGap="0px">
          <div fxFlex="calc(50% - 0.5px)" fxLayoutAlign="start" fxLayout="column">
            <span class="hint-text" id="vertex-type-settings-label">
              {{ 'Vertex Types' | translate }}
            </span>
            <div id="global-vertex-container">
              <mat-checkbox
                [checked]="everyElementIsShown(settingsShowVertexTypes)"
                (change)="toggleCheckEachElement(settingsShowVertexTypes, $event)"
                aria-labelledby="vertex-type-settings-label">
                {{ 'Show all vertex types' | translate }}
              </mat-checkbox>
              <div
                fxLayout="column"
                fxLayoutGap="5px">
                <div *ngFor="let showVertexType of settingsShowVertexTypes">
                  <mat-checkbox
                    [(ngModel)]="showVertexType[1]"
                    aria-labelledby="vertex-type-settings-label">
                    {{ showVertexType[0] }}
                  </mat-checkbox>
                </div>
              </div>
            </div>
          </div>
          <mat-divider [class.horizontal-separator]="!reflow" [vertical]="!reflow"></mat-divider>
          <div fxFlex="calc(50% - 0.5px)" fxLayoutAlign="start" fxLayout="column">
            <span class="hint-text" id="edge-type-settings-label">{{ 'Edge Types' | translate }}</span>
            <div id="global-edge-container">
              <mat-checkbox
                [checked]="everyElementIsShown(settingsShowEdgeTypes)"
                (change)="toggleCheckEachElement(settingsShowEdgeTypes, $event)"
                aria-labelledby="edge-type-settings-label">
                {{ 'Show all edge types' | translate }}
              </mat-checkbox>
              <div
                fxLayout="column"
                fxLayoutGap="5px">
                <div *ngFor="let showEdgeType of settingsShowEdgeTypes">
                  <mat-checkbox
                    [(ngModel)]="showEdgeType[1]"
                    aria-labelledby="edge-type-settings-label">
                    {{ showEdgeType[0] }}
                  </mat-checkbox>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button
        mat-button
        (click)="closePopup()">
        {{ 'CANCEL' | translate }}
      </button>
      <button mat-button color="primary" (click)="updateSettings()">
        {{ 'APPLY' | translate }}
      </button>
    </mat-dialog-actions>
  </div>
</ng-template>
<!-- End of Settings Popup -->

<ng-template #iconWindow>
  <div>
    <mat-toolbar color="primary" class="popup-toolbar">
      <h2 mat-dialog-title>{{ 'Select icon' | translate }}</h2>
    </mat-toolbar>

    <app-loading-indicator [show]="loading | async">
      <mat-dialog-content fxLayout="column" fxLayoutGap="0px" class="form-container">
        <div fxFlex fxLayoutAlign="start center" fxLayoutGap="12px">
          <div fxFlex="121px">
            <button
              class="upload-button"
              mat-raised-button
              color="primary"
              (click)="openUploadIconPopup()">
              {{ 'UPLOAD ICON' | translate }}
            </button>
          </div>
          <mat-form-field
            fxFlex="calc(100% - 133px)"
            class="filter-container">
            <input
              matInput
              placeholder="{{ 'Filter' | translate}}"
              [(ngModel)]="iconFilter"
              (ngModelChange)="onIconFilterChange($event)"
              [value]="iconFilter">
          </mat-form-field>
        </div>
        <div class="box-container">
          <div *ngIf="userUploadedIcons.length > 0 && (userUploadedIconsForSelect | async).length > 0">
            <h4>{{ 'My icons' | translate }}</h4>
            <div fxLayout="row wrap" fxLayoutGap="2%" class="icon-container">
              <button
                *ngFor="let icon of (userUploadedIconsForSelect | async)"
                mat-button
                class="select-button"
                (click)="selectIcon(icon.iconName)"
                attr.aria-label="{{ 'icon ' + icon.tag }}">
                <div
                  fxFlex="70px"
                  fxLayout="column"
                  fxLayoutAlign="center center">
                  <img alt="{{ 'icon ' + icon.tag }}" [src]="icon.url" class="icon icon-delete">
                  <button
                    (click)="confirmDeleteUserUploadedIcon(icon.iconName); $event.stopPropagation()"
                    matTooltip="{{ 'Delete' | translate }}"
                    mat-icon-button
                    class="overlay"
                    attr.aria-label="{{ 'Delete' | translate }} {{ icon.tag }}">
                    <mat-icon
                      class="hoverAndDelete"
                      attr.aria-label="{{ 'Delete' | translate }} {{ icon.tag }}">
                      close
                    </mat-icon>
                  </button>
                  <span class="icon-desc mat-caption">{{icon.tag}}</span>
                </div>
              </button>
            </div>
            <mat-divider [vertical]="false"></mat-divider>
          </div>
          <h4 class="library-header">{{ 'Library' | translate }}</h4>
          <app-icon-list [filter]="iconFilter"></app-icon-list>
        </div>
      </mat-dialog-content>
    </app-loading-indicator>

    <mat-dialog-actions align="end">
      <button
        mat-button
        (click)="closeIconSelectorPopup()">
        {{ 'CANCEL' | translate }}
      </button>
    </mat-dialog-actions>
  </div>
</ng-template>

<ng-template #uploadIconWindow>
  <div>
    <mat-toolbar color="primary" class="popup-toolbar">
      <h2 mat-dialog-title>{{ 'Upload icon' | translate }}</h2>
    </mat-toolbar>

    <mat-dialog-content fxLayout="column" fxLayoutAlign="center center" class="overflow-hidden">
      <button mat-raised-button class="file-upload-button" color="primary" (click)="fileInput.click()">
        <span>{{ 'CHOOSE FILE' | translate }}</span>
        <input #fileInput type="file" accept=".png" (change)="fileChangeEvent($event)" style="display:none;" />
      </button>
      <mat-hint>{{ 'PNG file only' | translate }}<br></mat-hint>

      <image-cropper
        [imageChangedEvent]="imageChangedEvent"
        [maintainAspectRatio]="true"
        [aspectRatio] = "1"
        [resizeToWidth]="512"
        format="png"
        (imageCropped)="imageCropped($event)"
        (imageLoaded)="imageLoaded()"
        (cropperReady)="cropperReady()"
        (loadImageFailed)="loadImageFailed()">
      </image-cropper>
      <img alt="upload image" hidden="true" [src]="croppedImage">

      <mat-form-field class="upload-icon-name-width">
        <input
          matInput
          placeholder="{{ 'Icon name' | translate }}"
          [(ngModel)]="currentlySelectedIconName"
          (ngModelChange)="setIconName($event)"
          [value]="currentlySelectedIconName"
          required>
      </mat-form-field>
      <div *ngIf="!validateIconName(currentlySelectedIconName).success" class="icon-name-error-text">
        {{ (!currentlySelectedIconName ? 'This field is required.'
          : validateIconName(currentlySelectedIconName).message) | translate}}
      </div>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button mat-button [mat-dialog-close]="0">
        {{ 'CANCEL' | translate }}
      </button>
      <button mat-button color="primary" [mat-dialog-close]="1"
        [disabled]="!currentlySelectedIconName || !croppedImage || !validateIconName(currentlySelectedIconName).success"
        (click)="uploadIcon(currentlySelectedIconName, croppedImage)">
        {{ 'UPLOAD' | translate }}
      </button>
    </mat-dialog-actions>
  </div>
</ng-template>

<ng-template #scWindow>
  <div>
    <h2 mat-dialog-title>{{ 'Confirm changing schema' | translate }}</h2>
    <mat-dialog-content>
      <p *ngIf="!(scStats$ | async)">{{ 'No change to the schema.' | translate }}</p>

      <ng-template [ngIf]="scStats$ | async">
        <p>{{ (scStats$ | async).total }}</p>
        <p
          class="mat-input-warn"
          *ngIf="(scStats$ | async).drop.length > 0">
          DROP:
        </p>
        <p
          class="mat-input-warn"
          *ngFor="let text of (scStats$ | async).drop">
          {{ text }}
        </p>
        <p *ngIf="(scStats$ | async).add.length > 0">ADD:</p>
        <p *ngFor="let text of (scStats$ | async).add">
          {{ text }}
        </p>
        <p *ngIf="(scStats$ | async).modify.length > 0">MODIFY:</p>
        <p *ngFor="let alter of (scStats$ | async).modify">
          - {{ alter.type}} {{ alter.name }}:
          <span class="mat-input-warn">{{ alter.drop }}</span>
          {{ alter.add }}
          <span class="mat-input-warn">{{ alter.dropIndex }}</span>
          {{ alter.addIndex }}.
        </p>
      </ng-template>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button mat-button [mat-dialog-close]="0">
        {{ 'CANCEL' | translate }}
      </button>
      <button mat-button color="primary" [mat-dialog-close]="1">
        {{ 'CONTINUE' | translate }}
      </button>
    </mat-dialog-actions>
  </div>
</ng-template>

<ng-template #vertexEdgeWindow>
  <div>
    <mat-toolbar color="primary" class="popup-toolbar" aria-describedby="dialogDescription">
      <h2 mat-dialog-title>
        {{ 'Schema information' | translate }}
      </h2>
      <p id="dialogDescription" hidden="true">
        {{'This dialog contains a schema information transcript' | translate }}
      </p>
    </mat-toolbar>

    <!-- Visual Information Content-->
    <mat-dialog-content>
      <br/>
      <!-- Vertex Information -->
      <h3>{{ 'Vertex information' | translate }}:</h3>
      <mat-list role="list" attr.aria-label="{{ 'Vertex information' | translate }}" tabindex="0">
        <ng-container *ngFor="let vertex of graphRef.current.getNodes()">
          <mat-list-item tabindex="0">
            <div matSubHeader matLine role="listitem">{{ ('Vertex' | translate) + ': ' + vertex.type }}</div>
            <div matLine *ngFor="let attr of vertex.attrs | keyvalue">
              <div matLine role="listitem">{{ attr.key + ': ' + attr.value }}</div>
            </div>
            <ng-container matLine *ngIf="vertex.embeddingAttrs && vertex.embeddingAttrs.length > 0">
              <div matLine>Embedding attributes:</div>
              <div matLine *ngFor="let attr of vertex.embeddingAttrs">
                <div matLine *ngFor="let obj of attr | keyvalue">
                  <div matLine role="listitem">{{ obj.key + ': ' + obj.value }}</div>
                </div>
              </div>
            </ng-container>
            <div matLine>
              <div matline>
                <div *ngIf="logicService.getAllGlobalTypes().indexOf(vertex.type) !== -1; else notLocal">{{ 'Is a global type' | translate }}</div>
                <ng-template #notLocal matLine>
                  <div matLine role="listitem">{{ 'Not a global type' | translate }}</div>
                </ng-template>
              </div>
            </div>
            <hr matLine />
          </mat-list-item>
        </ng-container>
      </mat-list>
      <!-- End of Vertex Information -->

      <!-- Edge Information -->
      <h3>{{ 'Edge information' | translate }}:</h3>
      <mat-list role="list" attr.aria-label="{{ 'Edge information' | translate }}" tabindex="0">
        <ng-container *ngFor="let edge of graphRef.current.getLinks()">
          <mat-list-item tabindex="0">
            <div matSubHeader matLine role="listitem">{{ ('Edge' | translate) + ': ' + edge.type }}</div>
            <div matLine role="listitem">{{ ('Source' | translate) + ': ' + edge.source.type }}</div>
            <div matLine role="listitem">{{ ('Target' | translate) + ': ' + edge.target.type }}</div>
            <div matLine *ngFor="let attr of edge.attrs | keyvalue">
              <div matLine role="listitem">{{ attr.key + ': ' + attr.value }}</div>
            </div>
            <div matLine>
              <div matline>
                <div *ngIf="logicService.getAllGlobalTypes().indexOf(edge.type) !== -1; else notLocal">{{ 'Is a global type' | translate }}</div>
                <ng-template #notLocal matLine>
                  <div matLine role="listitem">{{ 'Not a global type' | translate }}</div>
                </ng-template>
              </div>
            </div>
            <hr matLine />
          </mat-list-item>
        </ng-container>
      </mat-list>
      <!-- End of Edge Information -->
    </mat-dialog-content>
    <!-- End of Visual Information Content -->

    <mat-dialog-actions align="end">
      <button mat-button mat-dialog-close>
        {{ 'CLOSE' | translate }}
      </button>
    </mat-dialog-actions>
  </div>
</ng-template>
