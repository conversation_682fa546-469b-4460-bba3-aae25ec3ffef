<app-loading-indicator [show]="loading | async">
  <!-- TODO: Deprecate the page header component. -->
  <app-page-header fxHide></app-page-header>

  <!-- Family of Tools Header -->
  <tools-head
    appname="Graph Studio"
    appicon="/favicon.ico"
    innerapp="studio"
    [attr.user]="userName">
  </tools-head>

  <div *ngIf="cloudEnv" class="page-header">
    <cloud-header id="cloud-header">
    </cloud-header>
    <cloud-slider id="cloud-slider">
    </cloud-slider>
  </div>

  <!-- Small Screen Navigation Header -->
  <div class="nav-panel-header"
    fxShow
    fxHide.gt-sm="true"
    fxLayout="row"
    fxLayoutGap="2px"
    fxFlexAlign="start center"
    fxLayoutGap="20px">
    <mat-icon svgIcon="navigation" class="navigation-icon"></mat-icon>

    <h2 id="navigation-button" class="navigation-button">{{ 'Navigation' | translate }}</h2>
    <button
      mat-icon-button
      class="toggle-btn"
      (click)="closeNav = !closeNav"
      aria-labelledby="navigation-button"
      attr.aria-description="{{
        (closeNav ?
        'Click this button to hide the navigation' :
        'Click this button to display the navigation') | translate
      }}">
      <mat-icon [@indicatorRotate]="!closeNav ? 'expanded' : 'collapsed'">
        expand_more
      </mat-icon>

    </button>
  </div>
  <!-- End of Small Screen Navigation Header -->

  <!-- Navigation Container -->
  <div fxLayout.gt-sm="row" fxLayout="column" fxLayoutGap="1px" [ngClass]="{'sidenav-container': !cloudEnv, 'sidenav-container-cloud': cloudEnv}">
    <div
      *ngIf="(closeNav$ | async) === false || !closeNav"
      class="sidenav"
      [ngClass]="{ 'sidenav-close': hideNav }">

      <!-- toggle button -->
      <button
        fxHide
        fxShow.gt-sm="true"
        mat-icon-button
        class="toggle-btn"
        (click)="toggleHideSideNav();"
        aria-label="Click this button to toggle the navigation">
        <mat-icon class="icon-toggle" aria-hidden="true">chevron_left</mat-icon>
      </button>

      <!-- Home button -->
      <mat-nav-list [attr.role]="'tablist'" aria-label="tablist">
        <mat-list-item
          class="home-button"
          [routerLink]="homeNav.routerLink"
          routerLinkActive="active"
          role="tab"
          [attr.aria-label]="homeNav.label"
          [attr.aria-selected]="router.isActive(homeNav.routerLink, false)"
          (click)="navigateToHome($event)"
          (keydown.enter)="navigateToHome($event)"
          tabindex="0">
          <mat-icon
            attr.aria-label="{{ homeNav.label }} icon"
            matListIcon>
            {{homeNav.icon}}
          </mat-icon>
          <p matLine id="{{homeNav.label}} icon">{{ homeNav.label | translate }}</p>
        </mat-list-item>
      </mat-nav-list>
      <!-- End of Home Button -->

      <!-- Graph Menu -->
      <button
        class="graph-menu-btn"
        mat-stroked-button
        [disabled]="!currentGraph"
        #menuTrigger = "matMenuTrigger"
        [matMenuTriggerFor]="graphListMenu"
        matTooltip="{{ 'Switch graph' | translate }}"
        attr.aria-label="{{ 'Switch graph' | translate }}">
        <div *ngIf="!currentGraph">
          {{ 'No graph access' | translate }}
        </div>
        <div
          *ngIf="currentGraph"
          fxLayout="row"
          fxLayoutGap="7px">
          <div
            fxFlex="36px"
            *ngIf="!useGlobal"
            class="graph-menu-icon-container">
            <span class="mat-headline">
              {{ currentGraph.charAt(0).toUpperCase() }}
            </span>
          </div>
          <div
            fxFlex="38px"
            *ngIf="useGlobal"
            class="graph-menu-icon-container-svg-icon">
            <mat-icon aria-label="global view icon" svgIcon="global-svg"></mat-icon>
          </div>
          <div>
            <div fxLayout="column">
              <div class="mat-subheading-2 graph-name">
                {{ (useGlobal ? 'Global View' : currentGraph) | translate }}
              </div>
              <div
                fxLayout="row"
                class="mat-caption user-role"
                fxLayoutAlign="start center">
                <span>{{ currentRole }}</span>
                <button
                  *ngIf="showRestOfRoles().length > 0"
                  mat-icon-button
                  class="role-drop-down"
                  [matTooltip]="showRestOfRoles()"
                  matTooltipPosition="after"
                  matTooltipClass="tooltip-multiline"
                  aria-label="other roles">
                  <mat-icon aria-label="other roles icon">chevron_right</mat-icon>
                </button>
              </div>
            </div>
          </div>
          <div class="dropdown-container">
            <mat-icon svgIcon="dropdown" class="dropdown-icon"></mat-icon>
          </div>
        </div>
      </button>
      <!-- End of Graph Menu -->

      <!-- Navigation -->
      <mat-nav-list [attr.role]="'tablist'" aria-label="tablist">
        <a
          [attr.tabindex]="currentGraph === 'Global' && index !== 0 ? -1 : 0"
          mat-list-item
          *ngFor="let nav of navs; let index = index"
          [routerLink]="nav.routerLink"
          routerLinkActive="active"
          [ngClass]="{
            'disable-link-router': sidenavDisabled(index, nav.privileges)
          }"
          role="tab"
          [attr.aria-label]="nav.label"
          [attr.aria-selected]="router.isActive(nav.routerLink, false)"
          (click)="navigateAndFocus(nav)">
          <mat-icon
            attr.aria-label="{{ nav.label }} icon"
            matListIcon
            *ngIf="!(isSvgIcon(nav.icon) | async)">
            {{nav.icon}}
          </mat-icon>
          <mat-icon
            matListIcon
            attr.aria-label="{{ nav.label }} icon"
            class="svg-icon"
            *ngIf="isSvgIcon(nav.icon) | async"
            [svgIcon]="nav.icon">
          </mat-icon>
          <p matLine id="{{nav.label}} icon">{{ nav.label | translate }}</p>
          <div *ngIf="nav.beta" class="beta-tag">
            <img src="assets/img/beta-tag.png" class="beta-tag-img" alt="beta">
          </div>
        </a>
      </mat-nav-list>
      <!-- End of Navigation -->

      <!-- Graph List -->
      <mat-menu
        #graphListMenu="matMenu"
        class="graph-list-menu"
        xPosition="after"
        yPosition="below"
        overlapTrigger="false">
        <div
          *ngIf="hasGlobalAccess() && graphList.length === 1"
          class="hint-text">
          {{ 'No graph.' | translate }}
        </div>
        <button
          mat-menu-item
          [disabled]="graphName === currentGraph"
          *ngFor="let graphName of graphList"
          id="one-single-graph"
          (click)="confirmSwitchGraph(graphName)">
          <div
            fxLayout="row"
            fxLayoutGap="0px"
            fxLayoutAlign="center center"
            *ngIf="graphName !== GLOBAL_GRAPH_NAME; else isGlobal">
            <div
              fxLayout="row"
              fxLayoutGap="8px"
              fxLayoutAlign="center center"
              matTooltip="{{graphName}}"
              [matTooltipDisabled]="graphName === GLOBAL_GRAPH_NAME">
              <div class="graph-menu-icon-container-small">
                <span class="mat-headline">
                  {{ graphName?.charAt(0).toUpperCase() }}
                </span>
              </div>
              <span
                matLine
                class="graph-name-in-list"
                [ngClass]="{ 'graph-name-in-list-can-not-drop': !canDropGraph() }">
                {{ graphName }}
              </span>
            </div>
            <button
              *ngIf="graphName !== GLOBAL_GRAPH_NAME && getGraphCreator(graphName)"
              mat-icon-button
              class="creator-icon"
              [matTooltip]="getGraphCreator(graphName)"
              matTooltipPosition="above"
              matTooltipClass="tooltip-multiline"
              aria-label="graph creator button">
              <mat-icon aria-label="graph creator">info_outline</mat-icon>
            </button>
            <button
              mat-menu-item
              *ngIf="canDropGraph()"
              [disabled]="graphName === currentGraph"
              (click)="dropGraph(graphName); menuTrigger.closeMenu(); $event.stopPropagation()"
              class="drop-graph-icon"
              matTooltip="{{ 'Drop graph' | translate }} {{graphName}}"
              matTooltipPosition="right">
              <mat-icon aria-label="drop graph icon" [class.disble-delete-btn]="graphName === currentGraph">
                delete
              </mat-icon>
            </button>
          </div>
          <ng-template #isGlobal>
            <div fxLayout="row" fxLayoutGap="8px" fxLayoutAlign="start center">
              <div class="graph-menu-icon-container-small-svg-icon">
                <mat-icon aria-label="global view icon" svgIcon="global-svg"></mat-icon>
              </div>
              <span matLine class="graph-name-in-list graph-name-in-list-can-not-drop">
                {{ 'Global View' | translate }}
              </span>
            </div>
          </ng-template>
        </button>
        <button
          mat-menu-item
          *ngIf="canCreateGraph()"
          id="add-graph-btn"
          (click)="createGraph()">
          <div fxLayout="row" fxLayoutGap="8px">
            <div>
              <mat-icon aria-label="add graph icon" class="add-graph-icon" color="accent">add_circle_outline</mat-icon>
            </div>
            <span matLine class="graph-name-in-list">{{ 'Create a graph' | translate }}</span>
          </div>
        </button>
      </mat-menu>
      <!-- End of Graph List -->
    </div>

    <div
      class="content"
      [fxShow] = "(closeNav$ | async) === false || closeNav"
      fxFlex.xs="calc(100% - 56px)"
      fxFlex.gt-xs="calc(100% - 240px)">
      <div
        tabindex="-1"
        #contentFocusAnchor
        style="position: absolute; width: 0; height: 0; overflow: hidden; outline: none;"
        aria-hidden="true">
      </div>
      <router-outlet></router-outlet>
      <app-notification></app-notification>
    </div>
  </div>
  <!-- End of Navigation Container -->
</app-loading-indicator>

<ng-template #createGraphPopupWindow>
  <div>
    <mat-toolbar color="primary" class="popup-toolbar">
      <h2>{{ 'Create a graph' | translate }}</h2>
    </mat-toolbar>

    <app-loading-indicator [show]="loading | async">
      <mat-dialog-content class="form-container">
        <form [formGroup]="createGraphForm">
          <mat-form-field class="max-width">
            <input
              matInput
              placeholder="{{ 'Graph name' | translate }}"
              [value]="newGraphName"
              (change)="newGraphName = $event.target.value"
              formControlName="graphNameFormControl">
            <mat-error *ngIf="createGraphForm.get('graphNameFormControl').invalid">
              <div *ngIf="createGraphForm.get('graphNameFormControl').errors.invalidName">
                {{ createGraphForm.get('graphNameFormControl').errors.invalidName }}
              </div>
            </mat-error>
          </mat-form-field>
        </form>
        <div>
          <p class="hint-text type-title">{{ 'Choose global vertex and edge types' | translate }}</p>
          <div fxLayout="row" fxLayout.lt-md="column" fxLayoutGap="0px" class="box-container">
            <div fxFlex="calc(50% - 0.5px)" fxLayoutAlign="start">
              <div fxLayout="column" id="global-vertex-type-list" class="full-width">
                <mat-checkbox
                  [checked]="allTypesSelected(globalVertexTypes)"
                  (change)="
                    checkAll(globalVertexTypes, $event);
                    toggleCheckStatus(globalVertexTypes, globalEdgeTypes, true);">
                  {{ 'Choose all vertex types' | translate }}
                </mat-checkbox>
                <mat-checkbox
                  *ngFor="let vertexType of globalVertexTypes"
                  [(ngModel)]="vertexType.selected"
                  (change)="toggleCheckStatus([vertexType], globalEdgeTypes, true)">
                  {{ vertexType.vertexOrEdgeType.Name }}
                </mat-checkbox>
              </div>
            </div>
            <mat-divider
              [class.horizontal-separator]="!closeNav"
              [vertical]="!closeNav">
            </mat-divider>
            <div fxFlex="calc(50% - 0.5px)" fxLayoutAlign="start" fxLayout="column">
              <div fxLayout="column" id="global-edge-type-list" class="full-width">
                <mat-checkbox
                  [checked]="allTypesSelected(globalEdgeTypes)"
                  (change)="
                    checkAll(globalEdgeTypes, $event);
                    toggleCheckStatus(globalEdgeTypes, globalVertexTypes);
                  ">
                  {{ 'Choose all edge types' | translate }}
                </mat-checkbox>
                <mat-checkbox
                  *ngFor="let edgeType of globalEdgeTypes"
                  [(ngModel)]="edgeType.selected"
                  (change)="toggleCheckStatus([edgeType], globalVertexTypes)">
                  {{ edgeType.vertexOrEdgeType.Name }}
                </mat-checkbox>

              </div>
            </div>
          </div>
        </div>
      </mat-dialog-content>
    </app-loading-indicator>

    <mat-dialog-actions align="end">
      <button
        mat-button
        [disabled]="loading | async"
        (click)="closePopupWindow()">
        {{ 'CANCEL' | translate }}
      </button>
      <button
        mat-button
        color="primary"
        [disabled]="createGraphForm.get('graphNameFormControl').invalid || (loading | async)"
        (click)="confirmCreateGraph()">
        {{ 'CREATE' | translate }}
      </button>
    </mat-dialog-actions>
  </div>
</ng-template>
