<app-loading-indicator [show]="loading | async">
  <div class="toolbar-container">
    <app-toolbar-container
      id="visual-editor-toolbar"
      [config]="toolbarConfig"
      (interaction)="onInteraction($event)">
      <app-data-graph-chart
        #dataGraphContainer
        [graph]="schema"
        [graphData]="graphData"
        [graphEvents]="graphEvents"
        [showAttributesWhenMouseHover]="showAttributesWhenMouseHover"
        [editButtonChecked]="editButtonChecked"
        [skipAutoLayout]="skipAutoLayout"
        [graphLayout]="graphLayout"
        (changeGraphLayout)="onLayoutSelection($event)">
      </app-data-graph-chart>
      <app-shortcuts-menu
        #shortcutsMenuContainer
        class="shortcuts-menu-container"
        [shortcuts]="shortcuts">
      </app-shortcuts-menu>
    </app-toolbar-container>
  </div>

  <!-- Refresh <PERSON><PERSON> -->
  <div
    fxFlex="40px"
    class="bottom-left-button"
    *ngIf="canRefresh()">
    <button
      id="refresh-button"
      mat-icon-button
      (click)="refreshExploration()"
      attr.aria-label="{{ 'Refresh' | translate }}">
      <mat-icon
        attr.alt="{{ 'Refresh' | translate }}"
        matTooltip="{{ 'Refresh' | translate }}"
        matTooltipPosition="above">refresh</mat-icon>
    </button>
  </div>

  <div *ngIf="warning | async" class="main-panel-warning-container mat-input-warn" style="top: 36px;">
    <mat-icon attr.aria-label="{{ 'Warning' | translate }}">warning</mat-icon>
    <span>{{ warning | async }}</span>
  </div>
</app-loading-indicator>

<ng-template #popupWindow>
  <div class="visual-editor-popup">
    <!-- Popup Window Toolbar -->
    <mat-toolbar color="primary" class="popup-toolbar">
      <h2 mat-dialog-title id="dialogDescription">{{ getPopupTitle() | translate }}</h2>
      <p id="dialogDescription" hidden="true" *ngIf="selectedEvent === 'visInfo'">
        {{ 'This dialog contains a result chart information transcript' | translate }}
      </p>
    </mat-toolbar>
    <!-- End of Popup Window Toolbar -->

    <!-- Open Exploration -->
    <mat-dialog-content *ngIf="selectedEvent === 'open'">
    <div class="hint-text gap" *ngIf="(savedExplorationList$ | async).length === 0">
      {{ 'You have no previously saved explorations.' | translate }}
    </div>
    <mat-action-list class="detail-list-container">
      <button
        mat-list-item
        *ngFor="let savedExploration of (savedExplorationList$ | async); last as last"
        [class.checked]="savedExploration.name === checkedExploration"
        (click)="checkedExploration = savedExploration.name">
        <div fxLayout="column" fxLayout.gt-sm="row" class="max-width">
          <div
            class="detail-image"
            [ngClass.lt-md]="'detail-image-sm'"
            fxFlex>
            <img [src]=savedExploration.previewImage class="max-width" alt="{{ 'Saved exploration preview image' | translate }}">
          </div>
          <div class="detail-col" fxFlex>
            <div class="file-title">{{ savedExploration.name }}</div>
            <div *ngIf="savedExploration.description" class="hint-text detail-col-middle">
              {{ savedExploration.description }}
            </div>
            <div class="hint-text">{{ savedExploration.timestamp | date: 'yyyy-MM-dd HH:mm:ss' }} {{ 'by' | translate }} {{ savedExploration.username }}</div>
          </div>
        </div>
        <button
          mat-icon-button
          class="detail-close-button"
          matTooltip="{{ 'Delete' | translate }}" matTooltipPosition="after"
          (click)="deleteExploration(savedExploration.name); $event.stopPropagation()"
          attr.aria-label="{{ 'Delete exploration' | translate }}">
          <mat-icon attr.aria-label="{{ 'Delete exploration' | translate }}">close</mat-icon>
        </button>

        <mat-divider [inset]="true" *ngIf="!last"> </mat-divider>

      </button>
    </mat-action-list>
    </mat-dialog-content>
    <!-- End of Open Exploration -->

    <!-- Save Exploration -->
    <mat-dialog-content *ngIf="selectedEvent === 'save'">
      <div fxLayout.gt-sm="row" fxLayout="column" class="max-width">
        <div
          class="detail-image img-preview"
          [ngClass.lt-md]="'detail-image-sm img-preview'"
          fxFlex="50%">
          <img [src]=previewImgURL class="max-width" alt="Preview image">
          <div class="hint-text gap">
            <div>{{ currentDatetime | date: 'yyyy-MM-dd HH:mm:ss' }} {{ 'by' | translate }} {{ currentUsername }}</div>
          </div>
        </div>

        <form class="max-width gap" [formGroup]="saveExplorationForm">
          <mat-form-field class="max-width">
            <input
              matInput
              placeholder="{{ 'File name' | translate }}"
              [value]="explorationNameToSave"
              (change)="explorationNameToSave = $event.target.value"
              formControlName="explorationName"
              required
              attr.aria-label="{{ 'File name' | translate }}">
            <mat-error *ngIf="saveExplorationForm.get('explorationName').invalid">
              <div *ngIf="saveExplorationForm.get('explorationName').errors.required">
                {{ 'Exploration name is required.' | translate }}
              </div>
            </mat-error>
          </mat-form-field>
          <mat-error *ngIf="saveExplorationForm.get('explorationName').invalid">
            <div *ngIf="saveExplorationForm.get('explorationName').errors.maxlength">
              {{ 'Exploration name should be less than 50 characters.' | translate }}
            </div>
            <div *ngIf="saveExplorationForm.get('explorationName').errors.pattern">
              {{ 'Valid exploration name contains only letters, digits and \"-\".' | translate }}
            </div>
          </mat-error>
          <div class="img-preview hint-text">{{ 'Description (optional)' | translate }}</div>
          <div class="description-box">
            <mat-form-field class="max-width">
              <textarea
                matInput
                aria-label="'Description' | translate"
                [value]="explorationDescriptionToSave"
                (input)="explorationDescriptionToSave = $event.target.value"
                formControlName="explorationDescription"
                cdkTextareaAutosize
                cdkAutosizeMaxRows="6"
                aria-label="Description"></textarea>
              <mat-hint align="end">{{ explorationDescriptionToSave.length }} / 256</mat-hint>
              <mat-error *ngIf="saveExplorationForm.get('explorationDescription').invalid">
                {{ 'Description should be less than 256 characters.' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
        </form>
      </div>
    </mat-dialog-content>
    <!-- End of Save Exploration -->

    <!-- Search -->
    <mat-dialog-content
      *ngIf="selectedEvent === 'search'"
      fxLayout="column"
      fxLayoutGap="0px"
      class="form-container">
      <mat-form-field class="max-width">
        <input
          matInput
          placeholder="{{ 'Search content' | translate }}"
          [(ngModel)]="searchKeyword"
          attr.aria-label="{{ 'Search content' | translate }}">
      </mat-form-field>

      <div
        fxFlex.gt-sm="48px"
        fxFlex.lt-md="86px"
        fxLayoutAlign.gt-sm="space-between"
        fxLayout.lt-md="column">
        <div>
          <mat-checkbox [(ngModel)]="searchByVertexId" class="max-width">
            {{ 'Search vertex id' | translate }}
          </mat-checkbox>
        </div>
        <div>
          <mat-checkbox [(ngModel)]="searchByVertexAttrs">
            {{ 'Search vertex attributes' | translate }}
          </mat-checkbox>
        </div>
        <div>
          <mat-slide-toggle
            [checked]="substringMatch"
            (change)="substringMatch = !substringMatch">
            {{ 'Substring match' | translate }}
          </mat-slide-toggle>
        </div>

      </div>
    </mat-dialog-content>
    <!-- End of Search -->

    <!-- Add Vertex -->
    <mat-dialog-content
      *ngIf="selectedEvent === 'addVertex' || (selectedEvent === 'editAttributes' && selectedVertex)"
      class="form-container">
      <div fxLayout="column" fxLayoutGap="0px">
        <mat-form-field>
          <mat-select
            class="vertex-type-select"
            placeholder="{{ 'Vertex type' | translate }}"
            [(ngModel)]="vertex.type"
            (selectionChange)="onVertexTypeChange($event.value)"
            [disabled]="selectedEvent === 'editAttributes'">
            <mat-option *ngFor="let option of vertexTypes" [value]="option">
              {{ option }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field class="max-width">
          <input
            matInput
            placeholder="{{ 'Vertex id' | translate }}"
            [(ngModel)]="vertex.id"
            [disabled]="selectedEvent === 'editAttributes'"
            attr.aria-label="{{ 'Vertex id' | translate }}">
        </mat-form-field>
      </div>

      <span class="hint-text">{{ 'Attributes' | translate }}</span>
      <div class="box-container attribute-container">
        <span *ngIf="selectedVertexAttributes.length === 0" class="hint-text">
          <i>{{ 'No attributes to display.' | translate }}</i>
        </span>
        <app-dynamic-form
          #appDynamicForm
          [questions]="selectedVertexAttributes"
          [vertexTypes]="vertexTypes"
          [nestedLevel]="0"
          [itemSize]="103">
        </app-dynamic-form>
      </div>
    </mat-dialog-content>
    <!-- End of Add Vertex -->

    <!-- Add Edge -->
    <mat-dialog-content
      *ngIf="selectedEvent === 'addEdge' || (selectedEvent === 'editAttributes' && selectedEdge)"
      class="form-container">
      <mat-form-field class="max-width">
        <mat-select
          placeholder="{{ 'Edge type' | translate }}"
          [(ngModel)]="edge.type"
          (selectionChange)="onEdgeTypeChange($event.value)"
          [disabled]="selectedEvent === 'editAttributes'">
          <mat-option *ngFor="let option of edgeTypes" [value]="option">
            {{ option }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <span class="hint-text">{{ 'Attributes' | translate }}</span>
      <div class="box-container attribute-container">
        <span *ngIf="selectedEdgeAttributes.length === 0" class="hint-text">
          <i>{{ 'No attributes to display.' | translate }}</i>
        </span>
        <app-dynamic-form
          #appDynamicForm
          [questions]="selectedEdgeAttributes"
          [vertexTypes]="vertexTypes"
          [isDiscriminatorList]="isDiscriminatorList"
          [nestedLevel]="0"
          [itemSize]="103">
        </app-dynamic-form>
      </div>
    </mat-dialog-content>
    <!-- End of Add Edge -->

    <!-- Settings -->
    <mat-dialog-content *ngIf="selectedEvent === 'settings'">
      <!-- Show Attributes -->
      <br/>
      <p class="hint-text">{{ 'Visual configs of vertex and edge types' | translate }}</p>
      <div [ngClass.gt-sm]="'config-section-container'">
        <div fxLayout.gt-sm="row" fxLayout="column" fxLayoutGap="0px">
          <div
            fxFlex.gt-sm="30%"
            fxFlex="100%"
            fxLayoutAlign="start"
            fxLayout="column"
            class="vertex-edge-type-list-container">
            <!-- Large Screen Layout of Vertex and Edge Types -->
            <div fxHide fxShow.gt-sm="true">
              <p class="list-header">{{ 'Vertex and edge types' | translate }}</p>
              <p class="empty-list-hint hint-text" *ngIf="tmpAttributesForSelected.names.length === 0">
                {{ 'No graph schema in the system.' | translate }}
              </p>
              <mat-nav-list dense class="list-container">
                <a
                  mat-list-item
                  role="tab"
                  [attr.aria-selected]="currentSelectedName === name"
                  href="javascript:void(0)"
                  *ngFor="let name of tmpAttributesForSelected.names"
                  (click)="onCurrentSelectedNameChange(name)"
                  class="config-vertex-edge-type-list"
                  [class.active]="currentSelectedName === name">
                  {{ name }}
                </a>
              </mat-nav-list>
            </div>
          </div>
          <!-- End of Large Screen Layout of Vertex and Edge Types -->

          <!-- Small Screen Layout of Vertex and Edge Types -->
          <mat-form-field fxShow fxHide.gt-sm="true" class="max-width">
            <mat-select
              placeholder="{{ 'Vertex and edge types' | translate }}"
              [(ngModel)]="currentSelectedName"
              (selectionChange)="onCurrentSelectedNameChange($event.value)">
              <mat-option *ngFor="let name of tmpAttributesForSelected.names" [value]="name">
                {{ name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <!-- End of Small Screen Layout of Vertex and Edge Types -->

          <div
            fxFlex.gt-sm="70%"
            fxFlex="100%"
            fxLayoutAlign="start"
            fxLayout="column">
            <p [ngClass.gt-sm]="'list-header'" [ngClass.lt-md]="'hint-text'">{{ 'Visual configs' | translate }}</p>
            <p class="empty-list-hint hint-text" *ngIf="shownAttributes === undefined">
              {{ 'No configs to show.' | translate }}
            </p>
            <div *ngIf="shownAttributes !== undefined" [ngClass.lt-md]="'box-container'">
              <!-- Show Attributes -->
              <div class="visualization-config-container">
                <p class="hint-text">{{ 'Show attributes' | translate }}</p>
                <div class="box-container">
                  <div *ngFor="let elt of shownAttributes">
                    <mat-checkbox [(ngModel)]="elt.checked"> {{ elt.name }} </mat-checkbox>
                  </div>
                </div>
              </div>
              <!-- End of Show Attributes -->

              <!-- Color -->
              <div class="visualization-config-container">
                <p class="hint-text">{{ 'Color' | translate }}</p>
                <div class="box-container">
                  <div fxLayout="row" fxLayoutAlign="space-between center" *ngFor="let config of newVisualizationConfigOfAllTypes.get(currentSelectedName).color; let i = index">
                    <div fxFlex="calc=(100%-128px)" class="box-container color-filter-container">
                      {{ removeAlias(config.filter?.toString()) }}
                    </div>
                    <div fxFlex="24px" [style.background]="config.colorValue" class="color-icon-container">
                    </div>
                    <button mat-icon-button fxFlex="40px" [color]="primary" matTooltip="{{ 'Edit color config' | translate }}" matTooltipPosition="right" (click)="editColorConfig(i)" attr.aria-label="{{ 'Edit color configurations' | translate }}">
                      <mat-icon attr.aria-label="{{ 'Edit color configurations' | translate }}">edit</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" fxFlex="40px" (click)="removeColorConfig(i)" attr.aria-label="{{ 'Remove color configurations' | translate }}">
                      <mat-icon attr.aria-label="{{ 'Remove color configurations' | translate }}">remove</mat-icon>
                    </button>
                  </div>
                  <div fxLayout="row">
                    <span fxFlex fxLayoutAlign="center center" class="hint-text">
                      {{ 'Click \"+\" to add color configs.' | translate }}
                    </span>
                    <!-- Add Attributes Button -->
                    <button fxFlex="40px" mat-icon-button color="accent" (click)="addColorConfig()" attr.aria-label="{{ 'Add color configurations' | translate }}">
                      <mat-icon attr.aria-label="{{ 'Add color configurations' | translate }}">add</mat-icon>
                    </button>
                  </div>
                </div>
              </div>
              <!-- End of Color -->

              <!-- Radius/Thickness -->
              <div class="visualization-config-container">
                <p class="hint-text">{{ (isVertexType(currentSelectedName) ? "Radius" : "Thickness") | translate }}</p>
                <div class="box-container">
                  <div fxLayout="row" fxLayoutGap="0px" fxLayoutAlign="space-between center">
                    <div fxFlex="calc(100%-92px)" class="box-container color-filter-container">
                      {{ removeAlias(newVisualizationConfigOfAllTypes.get(currentSelectedName).size?.toString()) }}
                    </div>
                    <button fxFlex="40px"
                      mat-icon-button
                      id="add-size-config-btn"
                      [color]="primary"
                      matTooltip="{{ 'Edit' | translate }}"
                      matTooltipPosition="right"
                      (click)="editSizeConfig()"
                      attr.aria-label="{{ 'Edit radius configurations' | translate }}">
                      <mat-icon attr.aria-label="{{ 'Edit radius configurations' | translate }}">edit</mat-icon>
                    </button>
                    <button fxFlex="40px"
                      mat-icon-button
                      matTooltip="{{ 'Clear' | translate }}"
                      matTooltipPosition="right"
                      [disabled]="newVisualizationConfigOfAllTypes.get(currentSelectedName)?.size.toString() === ''"
                      (click)="clearSizeConfig()"
                      attr.aria-label="{{ 'Clear radius configurations' | translate }}">
                      <mat-icon attr.aria-label="{{ 'Clear radius configurations' | translate }}">delete</mat-icon>
                    </button>
                  </div>
                  <span *ngIf="isVertexType(currentSelectedName)" class="hint-text">
                    {{ 'The value of radius will be clamped between [20, 200].' | translate }}
                  </span>
                  <span *ngIf="!isVertexType(currentSelectedName)" class="hint-text">
                    {{ 'The value of thickness will be clamped between [1, 30].' | translate }}
                  </span>
                </div>
              </div>
              <!-- End of Radius/Thickness -->
            </div>
          </div>
        </div>
      </div>

      <!-- Number formatting -->
      <br/>
      <p class="hint-text">{{ 'Number formatting' | translate }}</p>
      <div class="box-container">
        <div class="box-container">
          <span class="hint-text">{{ 'Sample' | translate }}</span>
          <span class="sample-value">{{ numberFormattingSample }}</span>
        </div>

        <div
          fxLayout="row"
          fxLayout.lt-md="column"
          fxLayoutAlign="start center"
          fxLayoutAlign.lt-md="start start"
          ngClass.lt-md="reflow-formatting-label">
          <mat-checkbox
            (change)="setNumberFormattingSample()"
            [(ngModel)]="numberFormattingForShowing.thousandsSeparator.enabled"
            fxFlex="35%">
            {{ 'Thousands separator:' | translate }}
          </mat-checkbox>

          <div fxFlex="65%" ngClass.lt-md="reflow-formatting-field">
            <mat-form-field>
              <mat-select
                (selectionChange)="setNumberFormattingSample()"
                [disabled]="!numberFormattingForShowing.thousandsSeparator.enabled"
                [(ngModel)]="numberFormattingForShowing.thousandsSeparator.value">
                <mat-option
                  *ngFor="let separator of numberSeparatorList"
                  [value]="separator"
                >
                  {{ getSeparatorLabel(separator) }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>

        <div
          fxLayout="row"
          fxLayout.lt-md="column"
          fxLayoutAlign="start center"
          fxLayoutAlign.lt-md="start start">
          <mat-checkbox
            (change)="setNumberFormattingSample()"
            [(ngModel)]="numberFormattingForShowing.unifyDecimalSpaces.enabled"
            fxFlex="35%">
            {{ 'Unify decimal spaces:' | translate }}
          </mat-checkbox>
          <div fxFlex="65%" ngClass.lt-md="reflow-formatting-field">
            <mat-form-field>
              <input
                (input)="setNumberFormattingSample()"
                [disabled]="!numberFormattingForShowing.unifyDecimalSpaces.enabled"
                type="number"
                matInput
                [(ngModel)]="numberFormattingForShowing.unifyDecimalSpaces.value"
                max="12"
                min="0">
              <mat-hint>{{ 'An integer between 0 and 12.' | translate }}</mat-hint>
            </mat-form-field>
          </div>
        </div>

        <div
          fxLayout="row"
          fxLayout.lt-md="column"
          fxLayoutAlign="start center"
          fxLayoutAlign.lt-md="start start">
          <span fxFlex="35%">{{ 'Decimal separator:' | translate}}</span>
          <div fxFlex="65%" ngClass.lt-md="reflow-formatting-field">
            <mat-form-field>
              <mat-select
                (selectionChange)="setNumberFormattingSample()"
                [(ngModel)]="numberFormattingForShowing.decimalSeparator.value">
                <mat-option
                  *ngFor="let separator of numberSeparatorList"
                  [value]="separator"
                >
                  {{ getSeparatorLabel(separator) }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>

        <mat-error *ngFor="let error of numberFormattingError">{{ error }}</mat-error>
      </div>

      <!-- Mouse hover show tooltip -->
      <br/>
      <p class="hint-text">{{ 'Tooltip' | translate }}</p>
      <div class="box-container">
        <mat-checkbox [(ngModel)]="showAttributesWhenMouseHover">
          {{ 'Show tooltips when cursor hovers over vertices and edges.' | translate }}
        </mat-checkbox>
      </div>

      <!-- Label font settings -->
      <br/>
      <p class="hint-text">{{ 'Label font' | translate }}</p>
      <div class="box-container">
        <p class="hint-text">{{ 'Size' | translate }}</p>
        <mat-slider
          [(ngModel)]="newLabelFontSize"
          thumbLabel
          class="font-size-slider"
          color="primary"
          min="12"
          max="36"
          attr.aria-label=" {{ 'Label font size slider' | translate }}">
        </mat-slider>
        <p class="hint-text">{{ 'Preview' | translate }}</p>
        <br/>
        <div fxLayout="row" fxLayoutAlign="center center" class="preview-container">
          <div fxFlex="52px">
            <div class="vertex-preview"></div>
            <p
              [style.font-size.px]="newLabelFontSize"
              [ngStyle]="{'margin-left.px': getLabelMarginLeft(newLabelFontSize)}">
              {{ newLabelFontSize }}px
            </p>
          </div>
          <div fxFlex="auto" class="edge-preview">
            <div class="edge-preview-bar"></div>
            <p [style.font-size.px]="newLabelFontSize" class="edge-preview-label">
              {{ newLabelFontSize }}px
            </p>
          </div>
          <div fxFlex="52px">
            <div class="vertex-preview"></div>
            <p
              [style.font-size.px]="newLabelFontSize"
              [ngStyle]="{'margin-left.px': getLabelMarginLeft(newLabelFontSize)}">
              {{ newLabelFontSize }}px
            </p>
          </div>
        </div>
      </div>

    </mat-dialog-content>
    <!-- End of Settings -->

    <!-- Visual Information Content -->
    <mat-dialog-content class="vis-info" *ngIf="selectedEvent === 'visInfo'">
      <br/>
      <!-- Vertex Information -->
      <h3>{{ 'Vertex information' | translate }}:</h3>
      <mat-list role="list" attr.aria-label="{{ 'Vertex information' | translate }}" tabindex="0">
        <ng-container *ngFor="let vertex of getNodes()">
          <mat-list-item role="listitem">
            <div matSubHeader matLine role="listitem">{{ ('Vertex' | translate) + ': ' + vertex.exID }}</div>
            <div matLine role="listitem">{{ ('Type' | translate) + ': ' + vertex.exType }}</div>
            <div matLine *ngFor="let attr of vertex.attrs | keyvalue">
              <div matLine role="listitem">{{ attr.key + ': ' + attr.value }}</div>
            </div>
            <hr matLine/>
          </mat-list-item>
        </ng-container>
      </mat-list>
      <!-- End of Vertex Information -->

      <!-- Edge Information List -->
      <h3>{{ 'Edge information' | translate }}:</h3>
      <mat-list role="list" attr.aria-label="{{ 'Edge information' | translate }}" tabindex="0">
        <ng-container *ngFor="let edge of getLinks()">
          <mat-list-item role="listitem">
            <div matSubHeader matLine role="listitem">{{ ('Edge' | translate) + ': ' + edge.exType }}</div>
            <div matLine role="listitem">{{ ('Source vertex name' | translate) + ': ' + edge.source.exID }}</div>
            <div matLine role="listitem">{{ ('Source vertex type' | translate) + ': ' + edge.source.exType }}</div>
            <div matLine role="listitem">{{ ('Target vertex name' | translate) + ': ' + edge.target.exID }}</div>
            <div matLine role="listitem">{{ ('Target vertex type' | translate) + ': ' + edge.target.exType }}</div>
            <div matLine *ngFor="let attr of edge.attrs | keyvalue">
              <div matLine role="listitem">{{ attr.key + ': ' + attr.value }}</div>
            </div>
            <hr matLine/>
          </mat-list-item>
        </ng-container>
      </mat-list>
      <!-- End of Edge Information -->

      <!-- Highlighted Vertex List -->
      <h3>{{ 'Selected vertex information' | translate }}:</h3>
      <mat-list role="list" attr.aria-label="{{ 'Selected vertex information' | translate }}" *ngIf="graphRef.current" tabindex="0">
        <ng-container *ngFor="let node of graphRef.current.selectedNodes()">
          <mat-list-item>
            <div matSubHeader matLine role="listitem">
              {{ ('Selected vertex' | translate) + ': ' + node.id }}
            </div>
            <div matLine role="listitem">{{ ('Type' | translate) + ': ' + node.type }}</div>
            <div matLine *ngFor="let attr of node.attrs | keyvalue">
              <div matLine role="listitem">{{ attr.key + ': ' + attr.value }}</div>
            </div>
            <hr matLine/>
          </mat-list-item>
        </ng-container>
      </mat-list>
      <!-- End of Highlighted Vertex Information -->

      <!-- Highlighted Edge Information -->
      <h3>{{ 'Selected edge information' | translate }}:</h3>
      <mat-list role="list" attr.aria-label="{{ 'Selected edge information' | translate }}" *ngIf="graphRef.current" tabindex="0">
        <ng-container *ngFor="let link of graphRef.current.selectedEdges()">
          <mat-list-item role="listitem">
            <div matLine role="listitem">{{ ('Selected edge type' | translate) + ': ' + link.type }}</div>
            <div matLine role="listitem">{{ ('Source vertex name' | translate) + ': ' + link.source.id }}</div>
            <div matLine role="listitem">{{ ('Source vertex type' | translate) + ': ' + link.source.type }}</div>
            <div matLine role="listitem">{{ ('Target vertex name' | translate) + ': ' + link.target.id }}</div>
            <div matLine role="listitem">{{ ('Target vertex type' | translate) + ': ' + link.target.type }}</div>
            <div matLine *ngFor="let attr of link.attrs | keyvalue">
              <div matLine role="listitem">{{ attr.key + ': ' + attr.value }}</div>
            </div>
            <hr matLine/>
          </mat-list-item>
        </ng-container>
      </mat-list>
      <!-- End of Highlighted Information -->
    </mat-dialog-content>
    <!-- End of Visual Information Content -->

    <!-- Action Buttons -->
    <mat-dialog-actions align="end">
      <button
        [disabled]="isCopied"
        *ngIf="selectedEvent === 'editAttributes'"
        mat-button color="accent"
        (click)="handleCopyAllAttributes()"
        [matTooltip]="'Copy all attributes' | translate">
        {{ (isCopied ? 'COPIED' : 'COPY') | translate }}
      </button>
      <button mat-button (click)="closePopup()">
        {{ (showingInfo() ? 'CLOSE' : 'CANCEL') | translate }}
      </button>
      <button *ngIf="selectedEvent === 'search'" mat-button color="primary"
              [disabled]="!searchKeyword" (click)="searchVertex()">
        {{ 'SEARCH' | translate }}
      </button>
      <button *ngIf="selectedEvent === 'addVertex' || selectedEvent === 'addEdge'" mat-button
              color="primary" [disabled]="!dynamicForm?.form?.valid" (click)="addElement()">
        {{ 'ADD' | translate }}
      </button>
      <button *ngIf="selectedEvent === 'editAttributes'" mat-button color="primary"
              [disabled]="!dynamicForm?.form?.valid" (click)="updateElement()">
        {{ 'UPDATE' | translate }}
      </button>
      <button
        *ngIf="selectedEvent === 'settings'"
        mat-button color="primary"
        (click)="updateSettings()"
        [disabled]="numberFormattingError.length > 0">
        {{ 'APPLY' | translate }}
      </button>
      <button *ngIf="selectedEvent === 'open'" mat-button (click)="applyExploration()"
              [disabled]="!checkedExploration" color="primary">
        {{ 'OPEN' | translate }}
      </button>
      <button *ngIf="selectedEvent === 'save'" mat-button color="primary"
              (click)="saveExploration()" [disabled]="saveExplorationForm.invalid">
        {{ 'SAVE' | translate }}
      </button>
    </mat-dialog-actions >
    <!-- End of Action Buttons -->
  </div>
</ng-template>

<ng-template #configWindow>
  <div>
    <!-- Popup Window Toolbar -->
    <mat-toolbar color="primary" class="popup-toolbar">
      <h2 id="dialogDescription" *ngIf="selectedConfigControllerEvent === 'editColorConfig'">{{ 'Add color config' | translate }}</h2>
      <h2 id="dialogDescription" *ngIf="selectedConfigControllerEvent === 'editSizeConfig'">{{ 'Add size config' | translate }}</h2>
    </mat-toolbar>
    <!-- End of Popup Window Toolbar -->

    <mat-dialog-content>
      <!-- Config Filter Expression -->
      <div *ngIf="selectedConfigControllerEvent === 'editColorConfig'" class="config-filter">
        <span class="hint-text">{{ 'Color filter' | translate }}</span>
        <div fxLayout="row" fxLayoutGap="8px" fxLayoutAlign="space-between center">
          <div fxFlex="calc(70%-170px)">
            <div class="box-container">
              <span>{{ removeAlias(exprModel?.toString()) }}</span>
            </div>
          </div>
          <!-- Color Selector -->
          <!-- Color Preview -->
          <div fxFlex="40px" [style.background]="selectedColorValue" class="color-container"></div>
          <mat-form-field fxFlex="120px" class="color-hex">
            <input matInput placeholder="{{ 'Color hex' | translate }}" [(colorPicker)]="selectedColorValue" cpOutputFormat="hex" [value]="selectedColorValue" attr.aria-label="{{ 'Color hex' | translate }}">
          </mat-form-field>
          <!-- End of Color Seletor -->
        </div>
      </div>

      <div *ngIf="selectedConfigControllerEvent === 'editSizeConfig'" class="config-filter">
        <span class="hint-text">{{ 'Size expression' | translate }}</span>
        <div class="box-container">
          <span>{{ removeAlias(exprModel?.toString()) }}</span>
        </div>
      </div>
      <!-- End of Config Filter Expression -->

      <!-- Config Filter Modification -->
      <div *ngIf="selectedConfigControllerEvent === 'editColorConfig'">
        <span class="hint-text">{{ 'Build color filter' | translate }}</span>
        <app-expression-form
          [(data)]="exprModel"
          [config]="colorExprFormConfig"
          [schema]="logicService.schema">
        </app-expression-form>
      </div>

      <div *ngIf="selectedConfigControllerEvent === 'editSizeConfig'">
        <span class="hint-text">{{ 'Build size expression' | translate }}</span>
        <app-expression-form
          [(data)]="exprModel"
          [config]="sizeExprFormConfig"
          [schema]="logicService.schema">
        </app-expression-form>
      </div>
      <!-- End of Config Filter Modification -->
    </mat-dialog-content>

    <!-- Action Button -->
    <mat-dialog-actions align="end">
      <button mat-button (click)="closeConfigPopupWindow()">
        {{ 'CANCEL' | translate }}
      </button>
      <button *ngIf="selectedConfigControllerEvent === 'editColorConfig'" mat-button
        color="primary" (click)="applyColorConfig()" [disabled]="!isConfigFilterSemanticCorrect">
        {{ 'ADD' | translate }}
      </button>
      <button *ngIf="selectedConfigControllerEvent === 'editSizeConfig'" mat-button
        color="primary" (click)="applySizeConfig()" [disabled]="!isConfigFilterSemanticCorrect">
        {{ 'ADD' | translate }}
      </button>
    </mat-dialog-actions>
    <!-- End of Action Button -->
  </div>
</ng-template>
