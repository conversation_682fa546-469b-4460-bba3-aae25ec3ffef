{"name": "graph-studio", "description": "TigerGraph GraphStudio", "scripts": {"ng": "ng", "postinstall": "./scripts/fix-third-parties.sh", "postremove": "./scripts/fix-third-parties.sh", "prestart": "yarn build:themes", "start": "NODE_OPTIONS=\"--openssl-legacy-provider --max_old_space_size=8192\" ng serve --host 0.0.0.0 --port 4201 --disable-host-check", "serve": "http-server -p 9877", "prebuild": "yarn build:themes", "build": "NODE_OPTIONS=\"--openssl-legacy-provider --max_old_space_size=8192\" ng build", "build:themes": "./scripts/build-themes.sh", "test": "NODE_OPTIONS=\"--openssl-legacy-provider --max_old_space_size=8192\" ng test --browsers ChromeHeadlessCI --watch false --code-coverage", "eslint": "ng lint", "stylelint": "stylelint \"src/**/*.scss\"", "lint": "npm-run-all -c eslint stylelint", "e2e": "ng e2e --host 0.0.0.0", "e2e-win": "yarn e2e --protractor-config=e2e/protractor-win.conf.js --no-webdriver-update", "license": "./scripts/license/report-license.sh", "wcag:local": "npx cypress open --config-file cypress/config/cypress.local.json", "wcag:dev": "npx cypress open --config-file cypress/config/cypress.dev.json", "wcag:prod": "npx cypress open --config-file cypress/config/cypress.prod.json"}, "private": true, "dependencies": {"@angular/animations": "^12.2.17", "@angular/cdk": "^12.2.13", "@angular/common": "^12.2.17", "@angular/compiler": "^12.2.17", "@angular/core": "^12.2.17", "@angular/flex-layout": "^12.0.0-beta.35", "@angular/forms": "^12.2.17", "@angular/material": "^12.2.13", "@angular/platform-browser": "^12.2.17", "@angular/platform-browser-dynamic": "^12.2.17", "@angular/router": "^12.2.17", "@antv/x6": "^1.33.1", "@babel/traverse": "7.23.2", "@ng-idle/core": "^10.0.0", "@ng-idle/keepalive": "^10.0.0", "@ngx-translate/core": "^10.0.2", "@ngx-translate/http-loader": "3.x", "@tigergraph/cytoscape-edgehandles": "^4.0.1", "@tigergraph/tools-models": "1.1.10", "@tigergraph/tools-ui": "0.2.14-APPS-4080", "@types/mixpanel-browser": "^2.38.0", "baseui": "11.2.0", "chart.js": "2.9.4", "codemirror": "^5.40.0", "core-js": "^2.5.4", "crypto-js": "^4.2.0", "eventsource": "^1.1.1", "file-saver": "^2.0.5", "generate-schema": "^2.6.0", "json5": "^2.2.2", "jsrsasign": "^11.0.0", "jszip": "^3.2.2", "loader-utils": "^2.0.4", "lodash": "^4.17.0", "material-design-icons": "^3.0.0", "mixpanel": "^0.16.0", "mixpanel-browser": "^2.45.0", "ngx-color-picker": "^8.2.0", "ngx-image-cropper": "^1.4.1", "papaparse": "^5.3.1", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-query": "^3.34.19", "resumablejs": "^1.1.0", "roboto-fontface": "^0.10.0", "roboto-mono-webfont": "^2.0.0", "rxjs": "~6.6.7", "socket.io-parser": "^3.3.3", "styletron-engine-atomic": "1.4.8", "styletron-react": "6.0.2", "tslib": "^2.0.0", "xmlhttprequest-ssl": "^1.6.2", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-builders/custom-webpack": "~12.1.3", "@angular-devkit/build-angular": "~12.2.18", "@angular-eslint/builder": "12.7.0", "@angular-eslint/eslint-plugin": "12.7.0", "@angular-eslint/eslint-plugin-template": "12.7.0", "@angular-eslint/schematics": "12.7.0", "@angular-eslint/template-parser": "12.7.0", "@angular/cli": "~12.2.18", "@angular/compiler-cli": "^12.2.17", "@angular/language-service": "^11.2.14", "@tigergraph/build-angular": "12.2.18", "@types/codemirror": "~0.0.63", "@types/cytoscape": "^3.19.2", "@types/file-saver": "^2.0.3", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/jquery": "^3.5.14", "@types/lodash": "~4.14.0", "@types/lodash-es": "~4.14.0", "@types/node": "^12.11.1", "@types/papaparse": "^5.2.6", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/resumablejs": "^1.1.0", "@types/styletron-engine-atomic": "^1.1.1", "@types/styletron-react": "^5.0.3", "@typescript-eslint/eslint-plugin": "4.28.2", "@typescript-eslint/parser": "4.28.2", "axe-core": "^4.4.1", "axios-mock-adapter": "^1.22.0", "cypress": "^9.6.0", "cypress-axe": "^0.14.0", "eslint": "^7.26.0", "file-loader": "^6.2.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "6.4.1", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "karma-junit-reporter": "^1.2.0", "karma-selenium-grid-launcher": "^0.2.0", "lucide-static": "^0.454.0", "npm-run-all": "^4.1.5", "protractor": "~7.0.0", "protractor-jasmine2-screenshot-reporter": "^0.5.0", "sass": "~1.79.6", "selenium-webdriver": "^4.4.0", "stylelint": "^9.6.0", "ts-node": "^10.9.1", "typescript": "~4.5.5"}, "resolutions": {"d3-color": "3.1.0"}, "version": "0.0.0"}