{"name": "graph-studio", "description": "TigerGraph GraphStudio", "scripts": {"ng": "ng", "postinstall": "./scripts/fix-third-parties.sh", "postremove": "./scripts/fix-third-parties.sh", "prestart": "yarn build:themes", "start": "ng serve --host 0.0.0.0 --port 4201 --disable-host-check", "serve": "http-server -p 9877", "prebuild": "yarn build:themes", "build": "ng build", "build:themes": "./scripts/build-themes.sh", "test": "ng test --browsers ChromeHeadlessCI --watch false --code-coverage", "tslint": "ng lint", "stylelint": "stylelint \"src/**/*.scss\"", "lint": "npm-run-all -c tslint stylelint", "e2e": "ng e2e --host 0.0.0.0", "e2e-win": "yarn e2e --protractor-config=e2e/protractor-win.conf.js --no-webdriver-update", "license": "./scripts/license/report-license.sh", "wcag:local": "npx cypress open --config-file cypress/config/cypress.local.json", "wcag:dev": "npx cypress open --config-file cypress/config/cypress.dev.json", "wcag:prod": "npx cypress open --config-file cypress/config/cypress.prod.json"}, "private": true, "dependencies": {"@angular/animations": "^8.2.14", "@angular/cdk": "^8.0.0-rc.0", "@angular/common": "^8.2.14", "@angular/compiler": "^8.2.14", "@angular/core": "^8.2.14", "@angular/flex-layout": "^8.0.0-beta.26", "@angular/forms": "^8.2.14", "@angular/material": "^8.2.3", "@angular/platform-browser": "^8.2.14", "@angular/platform-browser-dynamic": "^8.2.14", "@angular/router": "^8.2.14", "@antv/x6": "^1.33.1", "@babel/traverse": "7.23.2", "@ng-idle/core": "^8.0.0-beta.4", "@ng-idle/keepalive": "^8.0.0-beta.4", "@ngx-translate/core": "^10.0.2", "@ngx-translate/http-loader": "3.x", "@tigergraph/app-ui-lib": "^0.2.4", "@tigergraph/cytoscape-edgehandles": "^4.0.1", "@tigergraph/tools-models": "1.0.115-APPS-3100-**********", "@tigergraph/tools-ui": "0.0.399-acessibility-2", "@types/mixpanel-browser": "^2.38.0", "baseui": "11.2.0", "chart.js": "2.9.4", "codemirror": "^5.40.0", "core-js": "^2.5.4", "crypto-js": "^4.2.0", "eventsource": "^1.1.1", "file-saver": "^2.0.5", "generate-schema": "^2.6.0", "hammerjs": "^2.0.0", "json5": "^2.2.2", "jsrsasign": "^11.0.0", "jszip": "^3.2.2", "loader-utils": "^2.0.4", "lodash": "^4.17.0", "material-design-icons": "^3.0.0", "mixpanel": "^0.16.0", "mixpanel-browser": "^2.45.0", "ngx-color-picker": "^8.2.0", "ngx-image-cropper": "^1.4.1", "papaparse": "^5.3.1", "react": "^17.0.2", "react-colorful": "^5.6.1", "react-dom": "^17.0.2", "react-query": "^3.34.19", "resumablejs": "^1.1.0", "roboto-fontface": "^0.10.0", "roboto-mono-webfont": "^2.0.0", "rxjs": "~6.6.7", "socket.io-parser": "^3.3.3", "styletron-engine-atomic": "1.4.8", "styletron-react": "6.0.2", "tslib": "^1.9.0", "xmlhttprequest-ssl": "^1.6.2", "zone.js": "~0.9.1"}, "devDependencies": {"@angular-devkit/build-angular": "~0.803.29", "@angular/cli": "~8.3.29", "@angular/compiler-cli": "^8.2.14", "@angular/language-service": "^8.2.14", "@types/codemirror": "~0.0.63", "@types/cytoscape": "^3.19.2", "@types/file-saver": "^2.0.3", "@types/jasmine": "~2.8.8", "@types/jasminewd2": "~2.0.3", "@types/jquery": "^3.5.14", "@types/lodash": "~4.14.0", "@types/node": "^12.0.2", "@types/papaparse": "^5.2.6", "@types/react": "^17.0.39", "@types/react-dom": "^17.0.11", "@types/resumablejs": "^1.1.0", "@types/styletron-engine-atomic": "^1.1.1", "@types/styletron-react": "^5.0.3", "axe-core": "^4.4.1", "axios-mock-adapter": "^1.22.0", "codelyzer": "^5.0.1", "cypress": "^9.6.0", "cypress-axe": "^0.14.0", "jasmine-core": "~2.99.1", "jasmine-spec-reporter": "~4.2.1", "karma": "~3.0.0", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "~2.0.1", "karma-jasmine": "~1.1.2", "karma-jasmine-html-reporter": "^0.2.2", "karma-junit-reporter": "^1.2.0", "karma-selenium-grid-launcher": "^0.2.0", "npm-run-all": "^4.1.5", "protractor": "~5.4.0", "protractor-jasmine2-screenshot-reporter": "^0.5.0", "sass": "^1.54.9", "selenium-webdriver": "^4.4.0", "stylelint": "^9.6.0", "ts-node": "^10.9.1", "tslint": "~5.11.0", "typescript": "~4.5.5"}, "browserslist": ["> 0.5%", "last 2 versions", "Firefox ESR", "not dead", "not IE 9-11"], "resolutions": {"d3-color": "3.1.0"}}